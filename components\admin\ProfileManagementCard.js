import { useState, useRef, useEffect } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from 'react-toastify'
import styles from '@/styles/admin/ProfileManagementCard.module.css'

export default function ProfileManagementCard({
  profile,
  artistId,
  onProfileUpdate,
  onProfileError,
  autoStartEditing = false
}) {
  const { user } = useAuth()
  const [isEditing, setIsEditing] = useState(autoStartEditing)
  const [saving, setSaving] = useState(false)
  const [loading, setLoading] = useState(false)
  const [currentProfile, setCurrentProfile] = useState(profile)
  const [formData, setFormData] = useState({
    artist_name: profile?.artist_name || '',
    display_name: profile?.display_name || '',
    bio: profile?.bio || '',
    specializations: profile?.specializations || [],
    skill_level: profile?.skill_level || 'intermediate',
    hourly_rate: profile?.hourly_rate || '',
    profile_image_url: profile?.profile_image_url || ''
  })
  const fileInputRef = useRef(null)

  // Fetch profile data if artistId is provided but no profile prop
  useEffect(() => {
    const fetchProfile = async () => {
      if (artistId && !profile && user?.access_token) {
        setLoading(true)
        try {
          const response = await fetch(`/api/artist/profile/${artistId}`, {
            headers: {
              'Authorization': `Bearer ${user.access_token}`
            }
          })

          if (response.ok) {
            const profileData = await response.json()
            setCurrentProfile(profileData)
            setFormData({
              artist_name: profileData?.artist_name || '',
              display_name: profileData?.display_name || '',
              bio: profileData?.bio || '',
              specializations: profileData?.specializations || [],
              skill_level: profileData?.skill_level || 'intermediate',
              hourly_rate: profileData?.hourly_rate || '',
              profile_image_url: profileData?.profile_image_url || ''
            })
          } else if (response.status === 404) {
            // Profile doesn't exist yet, start in editing mode for creation
            console.log('[ProfileManagementCard] No existing profile found, starting in creation mode')
            setIsEditing(true)
            setCurrentProfile(null)
          } else {
            throw new Error('Failed to fetch profile')
          }
        } catch (error) {
          console.error('[ProfileManagementCard] Error fetching profile:', error)
          if (onProfileError) {
            onProfileError('Failed to load profile data')
          } else {
            toast.error('Failed to load profile data')
          }
        } finally {
          setLoading(false)
        }
      }
    }

    fetchProfile()
  }, [artistId, profile, user?.access_token, onProfileError])

  // Update form data when profile prop changes
  useEffect(() => {
    if (profile) {
      setCurrentProfile(profile)
      setFormData({
        artist_name: profile?.artist_name || '',
        display_name: profile?.display_name || '',
        bio: profile?.bio || '',
        specializations: profile?.specializations || [],
        skill_level: profile?.skill_level || 'intermediate',
        hourly_rate: profile?.hourly_rate || '',
        profile_image_url: profile?.profile_image_url || ''
      })
    }
  }, [profile])

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSpecializationChange = (specialization) => {
    setFormData(prev => ({
      ...prev,
      specializations: prev.specializations.includes(specialization)
        ? prev.specializations.filter(s => s !== specialization)
        : [...prev.specializations, specialization]
    }))
  }

  const handleImageUpload = async (e) => {
    const file = e.target.files[0]
    if (!file) return

    // Validate file type and size
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file')
      return
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      toast.error('Image size must be less than 5MB')
      return
    }

    try {
      setSaving(true)
      
      // Create FormData for file upload
      const uploadData = new FormData()
      uploadData.append('file', file)
      uploadData.append('type', 'profile_image')

      const response = await fetch('/api/upload/image', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${user.access_token}`
        },
        body: uploadData
      })

      if (!response.ok) {
        throw new Error('Failed to upload image')
      }

      const result = await response.json()
      setFormData(prev => ({
        ...prev,
        profile_image_url: result.url
      }))

      toast.success('Profile image uploaded successfully')
    } catch (error) {
      console.error('Error uploading image:', error)
      toast.error('Failed to upload image')
    } finally {
      setSaving(false)
    }
  }

  const handleSave = async () => {
    // Validate required fields
    if (!formData.artist_name?.trim()) {
      toast.error('Artist name is required')
      return
    }
    if (!formData.display_name?.trim()) {
      toast.error('Display name is required')
      return
    }

    try {
      setSaving(true)

      const response = await fetch('/api/artist/profile', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${user.access_token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to update profile')
      }

      const updatedProfile = await response.json()
      setCurrentProfile(updatedProfile)
      setIsEditing(false)

      const isNewProfile = !currentProfile
      const successMessage = isNewProfile ? 'Profile created successfully!' : 'Profile updated successfully!'
      toast.success(successMessage)

      if (onProfileUpdate) {
        onProfileUpdate(updatedProfile)
      }
    } catch (error) {
      console.error('[ProfileManagementCard] Error saving profile:', error)
      const errorMessage = error.message || 'Failed to save profile'
      if (onProfileError) {
        onProfileError(errorMessage)
      } else {
        toast.error(errorMessage)
      }
    } finally {
      setSaving(false)
    }
  }

  const handleCancel = () => {
    // Reset form data to current profile state
    setFormData({
      artist_name: currentProfile?.artist_name || '',
      display_name: currentProfile?.display_name || '',
      bio: currentProfile?.bio || '',
      specializations: currentProfile?.specializations || [],
      skill_level: currentProfile?.skill_level || 'intermediate',
      hourly_rate: currentProfile?.hourly_rate || '',
      profile_image_url: currentProfile?.profile_image_url || ''
    })

    // Only allow canceling if profile already exists
    if (currentProfile) {
      setIsEditing(false)
    } else {
      // For new profiles, show a warning
      toast.info('Please complete your profile to continue')
    }
  }

  const availableSpecializations = [
    'braiding', 'hair', 'painting', 'glitter', 'uv', 'makeup', 'temporary_tattoos'
  ]

  const skillLevels = [
    { value: 'beginner', label: 'Beginner' },
    { value: 'intermediate', label: 'Intermediate' },
    { value: 'advanced', label: 'Advanced' },
    { value: 'expert', label: 'Expert' }
  ]

  // Show loading state
  if (loading) {
    return (
      <div className={styles.card}>
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}></div>
          <p>Loading profile data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.card}>
      <div className={styles.header}>
        <h3>{currentProfile ? 'Profile Management' : 'Create Your Profile'}</h3>
        {!isEditing && currentProfile && (
          <button
            onClick={() => setIsEditing(true)}
            className={styles.editButton}
          >
            Edit Profile
          </button>
        )}
      </div>

      <div className={styles.content}>
        {/* Profile Image */}
        <div className={styles.imageSection}>
          <div className={styles.imageContainer}>
            {formData.profile_image_url ? (
              <img 
                src={formData.profile_image_url} 
                alt="Profile" 
                className={styles.profileImage}
              />
            ) : (
              <div className={styles.imagePlaceholder}>
                <span>No Image</span>
              </div>
            )}
          </div>
          
          {isEditing && (
            <div className={styles.imageActions}>
              <input
                type="file"
                ref={fileInputRef}
                onChange={handleImageUpload}
                accept="image/*"
                style={{ display: 'none' }}
              />
              <button 
                onClick={() => fileInputRef.current?.click()}
                className={styles.uploadButton}
                disabled={saving}
              >
                {saving ? 'Uploading...' : 'Change Photo'}
              </button>
            </div>
          )}
        </div>

        {/* Profile Form */}
        <div className={styles.formSection}>
          <div className={styles.formGroup}>
            <label>Artist Name</label>
            {isEditing ? (
              <input
                type="text"
                name="artist_name"
                value={formData.artist_name}
                onChange={handleInputChange}
                className={styles.input}
              />
            ) : (
              <p className={styles.value}>{currentProfile?.artist_name || 'Not set'}</p>
            )}
          </div>

          <div className={styles.formGroup}>
            <label>Display Name</label>
            {isEditing ? (
              <input
                type="text"
                name="display_name"
                value={formData.display_name}
                onChange={handleInputChange}
                className={styles.input}
                placeholder="Enter your display name"
              />
            ) : (
              <p className={styles.value}>{currentProfile?.display_name || 'Not set'}</p>
            )}
          </div>

          <div className={styles.formGroup}>
            <label>Bio</label>
            {isEditing ? (
              <textarea
                name="bio"
                value={formData.bio}
                onChange={handleInputChange}
                className={styles.textarea}
                rows={3}
                placeholder="Tell us about yourself and your artistic style..."
              />
            ) : (
              <p className={styles.value}>{currentProfile?.bio || 'No bio available'}</p>
            )}
          </div>

          <div className={styles.formGroup}>
            <label>Specializations</label>
            {isEditing ? (
              <div className={styles.specializationGrid}>
                {availableSpecializations.map(spec => (
                  <label key={spec} className={styles.checkboxLabel}>
                    <input
                      type="checkbox"
                      checked={formData.specializations.includes(spec)}
                      onChange={() => handleSpecializationChange(spec)}
                    />
                    <span>{spec.charAt(0).toUpperCase() + spec.slice(1).replace('_', ' ')}</span>
                  </label>
                ))}
              </div>
            ) : (
              <div className={styles.specializationTags}>
                {currentProfile?.specializations?.map(spec => (
                  <span key={spec} className={styles.tag}>
                    {spec.charAt(0).toUpperCase() + spec.slice(1).replace('_', ' ')}
                  </span>
                )) || <span className={styles.noData}>No specializations set</span>}
              </div>
            )}
          </div>

          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label>Skill Level</label>
              {isEditing ? (
                <select
                  name="skill_level"
                  value={formData.skill_level}
                  onChange={handleInputChange}
                  className={styles.select}
                >
                  {skillLevels.map(level => (
                    <option key={level.value} value={level.value}>
                      {level.label}
                    </option>
                  ))}
                </select>
              ) : (
                <p className={styles.value}>
                  {skillLevels.find(l => l.value === currentProfile?.skill_level)?.label || 'Not set'}
                </p>
              )}
            </div>

            <div className={styles.formGroup}>
              <label>Hourly Rate (Optional)</label>
              {isEditing ? (
                <input
                  type="number"
                  name="hourly_rate"
                  value={formData.hourly_rate}
                  onChange={handleInputChange}
                  className={styles.input}
                  min="0"
                  step="0.01"
                  placeholder="Enter your hourly rate"
                />
              ) : (
                <p className={styles.value}>
                  {currentProfile?.hourly_rate ? `$${currentProfile.hourly_rate}` : 'Not set'}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        {isEditing && (
          <div className={styles.actions}>
            {currentProfile && (
              <button
                onClick={handleCancel}
                className={styles.cancelButton}
                disabled={saving}
              >
                Cancel
              </button>
            )}
            <button
              onClick={handleSave}
              className={styles.saveButton}
              disabled={saving}
            >
              {saving ? 'Saving...' : (currentProfile ? 'Save Changes' : 'Create Profile')}
            </button>
          </div>
        )}
      </div>
    </div>
  )
}
