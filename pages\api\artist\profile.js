import { createClient } from '@supabase/supabase-js';
import { getCurrentUserFromRequest } from '@/lib/supabase'; // Assuming this utility exists and works

export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(7);
  console.log(`[${requestId}] /api/artist/profile called, method: ${req.method}`);

  if (req.method !== 'PUT') {
    res.setHeader('Allow', ['PUT']);
    return res.status(405).json({ error: 'Method Not Allowed' });
  }

  try {
    // 1. Authenticate user and get their ID and role
    const { user, error: userError } = await getCurrentUserFromRequest(req);

    if (userError || !user) {
      console.error(`[${requestId}] Authentication error:`, userError?.message || 'No user found');
      return res.status(401).json({ error: 'Unauthorized: ' + (userError?.message || 'No user found') });
    }

    console.log(`[${requestId}] Authenticated user: ${user.id}, role: ${user.role}`);

    // 2. Authorize: Ensure user is an artist or braider
    const allowedRoles = ['artist', 'braider'];
    if (!user.role || !allowedRoles.includes(user.role)) {
      console.warn(`[${requestId}] User ${user.id} with role '${user.role}' attempted to update artist profile. Forbidden.`);
      return res.status(403).json({ error: 'Forbidden: You do not have permission to update this profile.' });
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );

    const profileData = req.body;
    const userId = user.id;

    console.log(`[${requestId}] Received profile data for user ${userId}:`, profileData);

    // Fields to update in artist_profiles
    // Ensure we only try to update fields that exist in the artist_profiles table
    const validFields = {
      artist_name: profileData.artist_name,
      display_name: profileData.display_name,
      bio: profileData.bio,
      specializations: profileData.specializations,
      skill_level: profileData.skill_level,
      hourly_rate: profileData.hourly_rate,
      profile_image_url: profileData.profile_image_url,
      is_profile_complete: true, // Mark profile as complete on any successful update via this endpoint
      updated_at: new Date().toISOString(),
    };

    // Remove undefined fields to avoid issues with Supabase client
    Object.keys(validFields).forEach(key => validFields[key] === undefined && delete validFields[key]);

    if (Object.keys(validFields).length === 1 && validFields.updated_at) {
        // Only updated_at is present, meaning no actual profile data was sent
        // This can happen if the form is submitted empty initially.
        // We should still attempt an upsert to create the record if it's missing.
        console.log(`[${requestId}] No profile data fields to update other than updated_at for user ${userId}. Proceeding with upsert.`);
    }


    // 3. Upsert profile data
    // Upsert will create if not exists (based on user_id), or update if exists.
    const { data: upsertedProfile, error: upsertError } = await supabase
      .from('artist_profiles')
      .upsert({ ...validFields, user_id: userId }, { onConflict: 'user_id', ignoreDuplicates: false })
      .select()
      .single();

    if (upsertError) {
      console.error(`[${requestId}] Supabase error upserting artist profile for user ${userId}:`, upsertError);
      return res.status(500).json({ error: 'Failed to update profile.', details: upsertError.message });
    }

    console.log(`[${requestId}] Profile for user ${userId} successfully upserted:`, upsertedProfile);

    // 4. Return success response
    // The ProfileManagementCard expects the updated profile data in the response.
    return res.status(200).json(upsertedProfile);

  } catch (error) {
    console.error(`[${requestId}] Unexpected error in /api/artist/profile:`, error);
    return res.status(500).json({ error: 'Internal Server Error', details: error.message });
  }
}
