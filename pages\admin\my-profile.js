import { useAuth } from '@/contexts/AuthContext';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import AdminLayout from '@/components/admin/AdminLayout';
import ProfileManagementCard from '@/components/admin/ProfileManagementCard';
import { toast } from 'react-toastify';
import { useRouter } from 'next/router';
import Head from 'next/head';
import styles from '@/styles/admin/MyProfilePage.module.css'; // Create this CSS module

const MyProfilePage = () => {
  const { user, loading: authLoading, supabaseClient, fetchUserProfile } = useAuth();
  const router = useRouter();

  const handleProfileUpdateSuccess = async (updatedProfile) => {
    toast.success('Profile updated successfully!');
    // Optionally, refresh user data in AuthContext if it holds profile details directly
    if (fetchUserProfile) {
      await fetchUserProfile(); // Assuming useAuth provides a way to refresh its user/profile state
    }
    // Redirect back to dashboard or stay on page
    router.push('/admin/artist-braider-dashboard');
  };

  const handleProfileUpdateError = (errorMessage) => {
    toast.error(errorMessage || 'Failed to update profile. Please try again.');
  };

  if (authLoading) {
    return (
      <AdminLayout>
        <div className={styles.loadingContainer}>
          <div className={styles.spinner}></div>
          <p>Loading your profile...</p>
        </div>
      </AdminLayout>
    );
  }

  if (!user) {
    // This should ideally be caught by ProtectedRoute, but as a fallback
    router.push('/staff-login?redirect=/admin/my-profile');
    return (
        <AdminLayout>
            <div className={styles.loadingContainer}><p>Redirecting to login...</p></div>
        </AdminLayout>
    );
  }

  if (!user.artistProfile && !authLoading) {
    // If user is loaded but artistProfile is not (maybe a new user who hasn't completed profile yet)
    // This page assumes a profile exists to be managed.
    // The complete-profile page is for initial setup.
    // However, ProfileManagementCard can handle upsert, so it might still be usable.
    // For now, let's ensure they have an artistProfile record.
    // This check might be redundant if dashboard already redirects to complete-profile.
    toast.warn('Profile data not fully loaded. You might need to complete initial setup.');
    // router.push('/admin/complete-profile'); // Consider this if profile MUST be complete first
    // return <AdminLayout><div className={styles.loadingContainer}><p>Loading profile data...</p></div></AdminLayout>;
  }

  // The artistId for ProfileManagementCard should be user.id (auth.users.id)
  // as ProfileManagementCard uses user.id to fetch/update its specific artist_profiles record.
  // Or, if ProfileManagementCard expects the artist_profiles primary key, ensure it's passed correctly.
  // Based on previous ProfileManagementCard usage, it expects 'artistId' which is user.id (auth.users.id).

  return (
    <ProtectedRoute allowedRoles={['artist', 'braider', 'admin', 'dev']}> {/* Allow admin/dev to see structure if needed */}
      <AdminLayout>
        <Head>
          <title>My Profile | Ocean Soul Sparkles</title>
        </Head>
        <div className={styles.container}>
          <h1 className={styles.title}>Edit Your Profile</h1>
          <p className={styles.description}>
            Keep your information up to date. Changes will be reflected across the platform.
          </p>

          {user && user.id ? (
            <ProfileManagementCard
              // profile={user.artistProfile} // Pass existing profile data if available in AuthContext
              artistId={user.id} // This should be the auth.users.id, ProfileManagementCard handles fetching/updating based on this
              onProfileUpdate={handleProfileUpdateSuccess}
              onProfileError={handleProfileUpdateError}
            />
          ) : (
            <p>Loading user information...</p>
          )}
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
};

export default MyProfilePage;
