-- =============================================
-- SECURITY REMEDIATION TESTING PLAN
-- Ocean Soul Sparkles Database Security Verification
-- =============================================

-- This script provides comprehensive testing for security fixes
-- Run AFTER executing security_remediation_comprehensive.sql

-- =============================================
-- TEST 1: VERIFY CRITICAL AUTH.USERS EXPOSURE FIX
-- =============================================

-- Test that quick_events_summary no longer exposes auth.users data
DO $$
DECLARE
  view_definition text;
  has_auth_users boolean := false;
BEGIN
  SELECT definition INTO view_definition 
  FROM pg_views 
  WHERE viewname = 'quick_events_summary' AND schemaname = 'public';
  
  IF view_definition LIKE '%auth.users%' THEN
    has_auth_users := true;
    RAISE WARNING '❌ CRITICAL: quick_events_summary still contains auth.users reference!';
  ELSE
    RAISE NOTICE '✅ PASS: quick_events_summary no longer exposes auth.users data';
  END IF;
END $$;

-- Test anonymous access is revoked
DO $$
DECLARE
  anon_access_count integer;
BEGIN
  SELECT COUNT(*) INTO anon_access_count
  FROM information_schema.role_table_grants 
  WHERE table_name = 'quick_events_summary' 
  AND grantee = 'anon' 
  AND privilege_type = 'SELECT';
  
  IF anon_access_count > 0 THEN
    RAISE WARNING '❌ CRITICAL: Anonymous users still have access to quick_events_summary!';
  ELSE
    RAISE NOTICE '✅ PASS: Anonymous access to quick_events_summary revoked';
  END IF;
END $$;

-- =============================================
-- TEST 2: VERIFY VIEW ACCESS RESTRICTIONS
-- =============================================

-- Test that sensitive analytics views are protected
DO $$
DECLARE
  view_name text;
  anon_access_count integer;
  total_violations integer := 0;
  sensitive_views text[] := ARRAY[
    'customer_statistics',
    'customer_analytics_summary', 
    'customer_analytics_view',
    'booking_analytics',
    'inventory_summary',
    'event_revenue_analytics'
  ];
BEGIN
  FOREACH view_name IN ARRAY sensitive_views
  LOOP
    SELECT COUNT(*) INTO anon_access_count
    FROM information_schema.role_table_grants 
    WHERE table_name = view_name 
    AND grantee = 'anon' 
    AND privilege_type = 'SELECT';
    
    IF anon_access_count > 0 THEN
      RAISE WARNING '❌ FAIL: Anonymous access still exists for %', view_name;
      total_violations := total_violations + 1;
    END IF;
  END LOOP;
  
  IF total_violations = 0 THEN
    RAISE NOTICE '✅ PASS: All sensitive analytics views protected from anonymous access';
  ELSE
    RAISE WARNING '❌ FAIL: % sensitive views still have anonymous access', total_violations;
  END IF;
END $$;

-- =============================================
-- TEST 3: VERIFY RLS ENABLEMENT
-- =============================================

-- Test that RLS is enabled on previously unprotected tables
DO $$
DECLARE
  table_name text;
  rls_enabled boolean;
  total_violations integer := 0;
  unprotected_tables text[] := ARRAY[
    'booking_status_history',
    'locations',
    'customer_tag_assignments',
    'customer_tags',
    'notification_templates'
  ];
BEGIN
  FOREACH table_name IN ARRAY unprotected_tables
  LOOP
    SELECT row_security INTO rls_enabled
    FROM pg_tables 
    WHERE tablename = table_name AND schemaname = 'public';
    
    IF NOT rls_enabled THEN
      RAISE WARNING '❌ FAIL: RLS not enabled on %', table_name;
      total_violations := total_violations + 1;
    END IF;
  END LOOP;
  
  IF total_violations = 0 THEN
    RAISE NOTICE '✅ PASS: RLS enabled on all previously unprotected tables';
  ELSE
    RAISE WARNING '❌ FAIL: % tables still missing RLS protection', total_violations;
  END IF;
END $$;

-- =============================================
-- TEST 4: VERIFY RLS POLICIES EXIST
-- =============================================

-- Test that RLS policies were created for protected tables
DO $$
DECLARE
  table_name text;
  policy_count integer;
  total_violations integer := 0;
  protected_tables text[] := ARRAY[
    'booking_status_history',
    'locations',
    'customer_tag_assignments',
    'customer_tags',
    'notification_templates'
  ];
BEGIN
  FOREACH table_name IN ARRAY protected_tables
  LOOP
    SELECT COUNT(*) INTO policy_count
    FROM pg_policies 
    WHERE tablename = table_name AND schemaname = 'public';
    
    IF policy_count = 0 THEN
      RAISE WARNING '❌ FAIL: No RLS policies found for %', table_name;
      total_violations := total_violations + 1;
    END IF;
  END LOOP;
  
  IF total_violations = 0 THEN
    RAISE NOTICE '✅ PASS: RLS policies exist for all protected tables';
  ELSE
    RAISE WARNING '❌ FAIL: % tables missing RLS policies', total_violations;
  END IF;
END $$;

-- =============================================
-- TEST 5: FUNCTIONAL TESTING SCENARIOS
-- =============================================

-- Test admin/staff access function
DO $$
BEGIN
  IF public.is_admin_or_staff() IS NOT NULL THEN
    RAISE NOTICE '✅ PASS: is_admin_or_staff() function created successfully';
  ELSE
    RAISE WARNING '❌ FAIL: is_admin_or_staff() function not working';
  END IF;
EXCEPTION
  WHEN OTHERS THEN
    RAISE WARNING '❌ FAIL: is_admin_or_staff() function error: %', SQLERRM;
END $$;

-- =============================================
-- TEST 6: COMPREHENSIVE SECURITY REPORT
-- =============================================

-- Generate comprehensive security status report
SELECT 
  'SECURITY REMEDIATION REPORT' as report_type,
  current_timestamp as generated_at;

-- Check all views for anonymous access
SELECT 
  'VIEW SECURITY CHECK' as check_type,
  table_name,
  CASE 
    WHEN EXISTS(
      SELECT 1 FROM information_schema.role_table_grants g 
      WHERE g.table_name = v.table_name 
      AND g.grantee = 'anon' 
      AND g.privilege_type = 'SELECT'
    ) THEN '❌ VULNERABLE'
    ELSE '✅ PROTECTED'
  END as security_status
FROM information_schema.views v
WHERE v.table_schema = 'public'
AND v.table_name IN (
  'quick_events_summary', 'customer_statistics', 'product_stock_status',
  'booking_analytics', 'popular_time_slots', 'customer_analytics_summary',
  'inventory_summary', 'event_revenue_analytics', 'services_with_pricing',
  'customer_analytics_view', 'artist_availability_view', 
  'services_with_available_artists', 'artist_current_availability',
  'services_quick_event_summary'
)
ORDER BY security_status DESC, table_name;

-- Check all tables for RLS status
SELECT 
  'TABLE RLS CHECK' as check_type,
  tablename as table_name,
  CASE 
    WHEN row_security THEN '✅ RLS ENABLED'
    ELSE '❌ RLS DISABLED'
  END as rls_status,
  CASE 
    WHEN EXISTS(
      SELECT 1 FROM pg_policies p 
      WHERE p.tablename = t.tablename 
      AND p.schemaname = 'public'
    ) THEN '✅ HAS POLICIES'
    ELSE '❌ NO POLICIES'
  END as policy_status
FROM pg_tables t
WHERE t.schemaname = 'public'
AND t.tablename IN (
  'booking_status_history', 'locations', 'customer_tag_assignments',
  'customer_tags', 'notification_templates'
)
ORDER BY rls_status DESC, table_name;

-- =============================================
-- TEST 7: APPLICATION COMPATIBILITY CHECK
-- =============================================

-- Test that essential views still return data structure
DO $$
DECLARE
  view_name text;
  column_count integer;
  essential_views text[] := ARRAY[
    'services_with_pricing',
    'artist_availability_view',
    'services_with_available_artists'
  ];
BEGIN
  FOREACH view_name IN ARRAY essential_views
  LOOP
    SELECT COUNT(*) INTO column_count
    FROM information_schema.columns 
    WHERE table_name = view_name AND table_schema = 'public';
    
    IF column_count > 0 THEN
      RAISE NOTICE '✅ PASS: % view structure intact (% columns)', view_name, column_count;
    ELSE
      RAISE WARNING '❌ FAIL: % view structure broken', view_name;
    END IF;
  END LOOP;
END $$;

-- =============================================
-- FINAL SUMMARY
-- =============================================

DO $$
BEGIN
  RAISE NOTICE '==========================================';
  RAISE NOTICE '🔒 SECURITY TESTING COMPLETED';
  RAISE NOTICE '==========================================';
  RAISE NOTICE 'Review all test results above';
  RAISE NOTICE 'Any ❌ FAIL items require immediate attention';
  RAISE NOTICE 'All ✅ PASS items indicate successful remediation';
  RAISE NOTICE '==========================================';
END $$;
