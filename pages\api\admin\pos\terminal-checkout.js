/**
 * Square Terminal Checkout API - Process payments through Square Terminal hardware
 * Handles checkout creation, status monitoring, and payment completion
 */

import { supabaseAdmin } from '@/lib/supabase-admin'

export default async function handler(req, res) {
  // Generate unique request ID for tracking
  const requestId = `terminal_checkout_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`
  
  console.log(`[${requestId}] Terminal Checkout API request: ${req.method}`)

  // Only allow POST and GET methods
  if (!['POST', 'GET'].includes(req.method)) {
    return res.status(405).json({ 
      error: 'Method not allowed',
      message: `${req.method} method is not supported for this endpoint`
    })
  }

  try {
    // Verify admin authentication
    const authHeader = req.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Valid admin token required'
      })
    }

    const token = authHeader.split(' ')[1]
    
    // Verify token with Supabase
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token)
    
    if (authError || !user) {
      console.error(`[${requestId}] Authentication failed:`, authError)
      return res.status(401).json({
        error: 'Authentication failed',
        message: 'Invalid or expired token'
      })
    }

    // Get Square configuration
    const squareAccessToken = process.env.SQUARE_ACCESS_TOKEN
    const squareEnvironment = process.env.SQUARE_ENVIRONMENT || 'sandbox'
    const squareLocationId = process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID

    if (!squareAccessToken || !squareLocationId) {
      return res.status(500).json({
        error: 'Configuration error',
        message: 'Square API credentials not configured'
      })
    }

    if (req.method === 'POST') {
      // Create new terminal checkout
      return await handleCreateCheckout(req, res, requestId, squareAccessToken, squareEnvironment, squareLocationId)
    } else if (req.method === 'GET') {
      // Get checkout status
      return await handleGetCheckoutStatus(req, res, requestId, squareAccessToken, squareEnvironment)
    }

  } catch (error) {
    console.error(`[${requestId}] Terminal checkout API error:`, error)
    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to process terminal checkout request',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

/**
 * Handle POST request - Create new terminal checkout
 */
async function handleCreateCheckout(req, res, requestId, accessToken, environment, locationId) {
  try {
    const { 
      deviceId, 
      amount, 
      currency = 'AUD', 
      orderDetails,
      skipReceiptScreen = false,
      collectSignature = true,
      allowTipping = false
    } = req.body

    // Validate required fields
    if (!deviceId || !amount) {
      return res.status(400).json({
        error: 'Missing required fields',
        message: 'Device ID and amount are required'
      })
    }

    console.log(`[${requestId}] Creating terminal checkout for device: ${deviceId}, amount: ${amount}`)

    // Square API endpoint for creating terminal checkouts
    const baseUrl = environment === 'production'
      ? 'https://connect.squareup.com'
      : 'https://connect.squareupsandbox.com'

    const url = `${baseUrl}/v2/terminals/checkouts`

    // Generate idempotency key
    const idempotencyKey = `terminal_checkout_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`

    const requestBody = {
      idempotency_key: idempotencyKey,
      checkout: {
        amount_money: {
          amount: Math.round(amount * 100), // Convert to cents
          currency: currency.toUpperCase()
        },
        device_options: {
          device_id: deviceId,
          skip_receipt_screen: skipReceiptScreen,
          collect_signature: collectSignature,
          show_itemized_cart: false
        },
        payment_options: {
          autocomplete: true,
          delay_duration: 'PT5M' // 5 minute timeout
        },
        order_id: orderDetails?.orderId,
        payment_note: orderDetails?.note || `POS Terminal Payment - ${orderDetails?.service || 'Service'}`,
        location_id: locationId
      }
    }

    // Add tipping configuration if enabled
    if (allowTipping) {
      requestBody.checkout.payment_options.accept_partial_authorization = false
      requestBody.checkout.device_options.tip_settings = {
        allow_tipping: true,
        separate_tip_screen: true,
        custom_tip_field: true,
        tip_percentages: [15, 20, 25]
      }
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Square-Version': '2023-10-18'
      },
      body: JSON.stringify(requestBody)
    })

    const responseData = await response.json()

    if (!response.ok) {
      console.error(`[${requestId}] Square API error:`, responseData)
      throw new Error(responseData.errors?.[0]?.detail || 'Failed to create terminal checkout')
    }

    const checkout = responseData.checkout

    console.log(`[${requestId}] Terminal checkout created successfully: ${checkout.id}`)

    return res.status(201).json({
      success: true,
      checkout: {
        id: checkout.id,
        status: checkout.status,
        amountMoney: checkout.amount_money,
        deviceId: checkout.device_options?.device_id,
        paymentId: checkout.payment_id,
        orderId: checkout.order_id,
        createdAt: checkout.created_at,
        updatedAt: checkout.updated_at,
        deadlineDuration: checkout.deadline_duration,
        paymentNote: checkout.payment_note
      },
      message: 'Terminal checkout created successfully'
    })

  } catch (error) {
    console.error(`[${requestId}] Error creating terminal checkout:`, error)
    return res.status(500).json({
      error: 'Failed to create terminal checkout',
      message: error.message
    })
  }
}

/**
 * Handle GET request - Get checkout status
 */
async function handleGetCheckoutStatus(req, res, requestId, accessToken, environment) {
  try {
    const { checkoutId } = req.query

    if (!checkoutId) {
      return res.status(400).json({
        error: 'Missing checkout ID',
        message: 'Checkout ID is required'
      })
    }

    console.log(`[${requestId}] Getting checkout status for: ${checkoutId}`)

    // Square API endpoint for getting checkout status
    const baseUrl = environment === 'production'
      ? 'https://connect.squareup.com'
      : 'https://connect.squareupsandbox.com'

    const url = `${baseUrl}/v2/terminals/checkouts/${checkoutId}`

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Square-Version': '2023-10-18'
      }
    })

    const responseData = await response.json()

    if (!response.ok) {
      console.error(`[${requestId}] Square API error:`, responseData)
      throw new Error(responseData.errors?.[0]?.detail || 'Failed to get checkout status')
    }

    const checkout = responseData.checkout

    console.log(`[${requestId}] Checkout status retrieved: ${checkout.status}`)

    return res.status(200).json({
      success: true,
      checkout: {
        id: checkout.id,
        status: checkout.status,
        amountMoney: checkout.amount_money,
        deviceId: checkout.device_options?.device_id,
        paymentId: checkout.payment_id,
        orderId: checkout.order_id,
        createdAt: checkout.created_at,
        updatedAt: checkout.updated_at,
        paymentNote: checkout.payment_note,
        cancelReason: checkout.cancel_reason
      },
      message: `Checkout status: ${checkout.status}`
    })

  } catch (error) {
    console.error(`[${requestId}] Error getting checkout status:`, error)
    return res.status(500).json({
      error: 'Failed to get checkout status',
      message: error.message
    })
  }
}
