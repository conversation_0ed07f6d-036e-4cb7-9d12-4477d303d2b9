.container {
  max-width: 900px;
  margin: 2rem auto;
  padding: 2rem;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.title {
  color: #333;
  text-align: center;
  margin-bottom: 0.5rem;
  font-size: 2rem;
  font-weight: 600;
}

.description {
  text-align: center;
  margin-bottom: 2.5rem;
  color: #555;
  font-size: 1rem;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  min-height: 300px; /* Ensure it takes some space */
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.loadingContainer p {
  font-size: 1.1rem;
  color: #666;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .container {
    margin: 1rem;
    padding: 1.5rem;
  }

  .title {
    font-size: 1.75rem;
  }

  .description {
    margin-bottom: 2rem;
    font-size: 0.95rem;
  }
}
