import React from 'react';
import styles from '@/styles/admin/ErrorBoundary.module.css';

class DashboardErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { 
      hasError: true,
      errorId: Math.random().toString(36).substring(2, 8)
    };
  }

  componentDidCatch(error, errorInfo) {
    // Log the error details
    console.error('[DashboardErrorBoundary] Caught error:', error);
    console.error('[DashboardErrorBoundary] Error info:', errorInfo);
    
    this.setState({
      error: error,
      errorInfo: errorInfo
    });

    // Filter out browser extension errors
    const errorMessage = error?.message || '';
    const isExtensionError = errorMessage.includes('chrome-extension') ||
                           errorMessage.includes('moz-extension') ||
                           errorMessage.includes('safari-extension') ||
                           errorMessage.includes('overrideMethod') ||
                           errorMessage.includes('cancelled true');

    if (isExtensionError) {
      console.log('[DashboardErrorBoundary] Suppressing browser extension error');
      // Reset error state for extension errors
      setTimeout(() => {
        this.setState({ hasError: false, error: null, errorInfo: null });
      }, 100);
      return;
    }

    // Report non-extension errors to monitoring service if available
    if (typeof window !== 'undefined' && window.reportError) {
      window.reportError(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({ 
      hasError: false, 
      error: null, 
      errorInfo: null,
      errorId: null
    });
  };

  render() {
    if (this.state.hasError) {
      const { error, errorInfo } = this.state;
      
      // Check if this is a browser extension error
      const errorMessage = error?.message || '';
      const isExtensionError = errorMessage.includes('chrome-extension') ||
                             errorMessage.includes('moz-extension') ||
                             errorMessage.includes('safari-extension') ||
                             errorMessage.includes('overrideMethod') ||
                             errorMessage.includes('cancelled true');

      if (isExtensionError) {
        // For extension errors, render children normally
        return this.props.children;
      }

      // Render error UI for actual application errors
      return (
        <div className={styles.errorBoundary}>
          <div className={styles.errorContainer}>
            <div className={styles.errorIcon}>⚠️</div>
            <h2 className={styles.errorTitle}>Dashboard Error</h2>
            <p className={styles.errorMessage}>
              Something went wrong while loading the dashboard. This is usually temporary.
            </p>
            
            <div className={styles.errorActions}>
              <button 
                onClick={this.handleRetry}
                className={styles.retryButton}
              >
                🔄 Try Again
              </button>
              <button 
                onClick={() => window.location.reload()}
                className={styles.refreshButton}
              >
                🔃 Refresh Page
              </button>
            </div>

            {process.env.NODE_ENV === 'development' && (
              <details className={styles.errorDetails}>
                <summary>Error Details (Development Only)</summary>
                <div className={styles.errorStack}>
                  <h4>Error:</h4>
                  <pre>{error && error.toString()}</pre>
                  <h4>Component Stack:</h4>
                  <pre>{errorInfo.componentStack}</pre>
                </div>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default DashboardErrorBoundary;
