.bookingListCard {
  background-color: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px; /* For spacing if multiple cards are used */
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e9ecef;
}

.cardHeader h3 {
  margin: 0;
  font-size: 1.25rem;
  color: #343a40;
}

.filters {
  display: flex;
  gap: 10px;
}

.filterButton {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  background-color: #f8f9fa;
  color: #495057;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s, color 0.2s;
}

.filterButton:hover {
  background-color: #e9ecef;
}

.filterButton.activeFilter {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.filterButton:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.bookingList {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.bookingItem {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 10px;
  transition: box-shadow 0.2s ease-in-out;
}

.bookingItem:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
}

.bookingItemHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.serviceName {
  font-weight: 600;
  font-size: 1.1rem;
  color: #212529;
}

.statusBadge {
  padding: 4px 8px;
  border-radius: 12px; /* Pill shape */
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Status-specific styles */
.statusBadge.pending {
  background-color: #fff3cd; /* Light yellow */
  color: #856404; /* Dark yellow */
}
.statusBadge.confirmed {
  background-color: #d4edda; /* Light green */
  color: #155724; /* Dark green */
}
.statusBadge.completed {
  background-color: #d1ecf1; /* Light blue */
  color: #0c5460; /* Dark blue */
}
.statusBadge.cancelled, .statusBadge.canceled { /* Handle both spellings */
  background-color: #f8d7da; /* Light red */
  color: #721c24; /* Dark red */
}
.statusBadge.upcoming {
  background-color: #e2e3e5; /* Light grey */
  color: #495057; /* Dark grey */
}
.statusBadge.unknown {
  background-color: #e9ecef;
  color: #495057;
}


.bookingItemBody p {
  margin: 4px 0;
  font-size: 0.9rem;
  color: #495057;
}

.bookingItemBody p strong {
  color: #212529;
}

.notes {
  font-style: italic;
  color: #6c757d;
  font-size: 0.85rem;
}

.loadMoreButton {
  display: block;
  width: 100%;
  padding: 10px;
  margin-top: 15px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
}

.loadMoreButton:hover {
  background-color: #0056b3;
}

.loadMoreButton:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

.loadingState, .emptyState, .error {
  text-align: center;
  padding: 20px;
  color: #6c757d;
  font-size: 1rem;
}

.error {
  color: #721c24;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
  border-radius: 4px;
  padding: 10px;
}

.spinner {
  width: 24px;
  height: 24px;
  border: 3px solid rgba(0,0,0,0.1);
  border-left-color: #007bff;
  border-radius: 50%;
  display: inline-block;
  animation: spin 1s linear infinite;
  margin-right: 8px;
  vertical-align: middle;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}
