/**
 * Square Reader Callback Page - <PERSON><PERSON> returns from Square POS app
 * Processes transaction results and maintains session continuity
 */

import { useEffect, useState } from 'react'
import { useRouter } from 'next/router'
import AdminLayout from '@/components/admin/AdminLayout'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import AuthenticationGuard from '@/components/admin/AuthenticationGuard'
import styles from '@/styles/admin/POS.module.css'

export default function ReaderCallbackPage() {
  const router = useRouter()
  const [processing, setProcessing] = useState(true)
  const [result, setResult] = useState(null)
  const [error, setError] = useState(null)
  const [sessionData, setSessionData] = useState(null)

  useEffect(() => {
    processSquareCallback()
  }, [router.query])

  const processSquareCallback = async () => {
    try {
      console.log('Processing Square Reader callback...')
      
      // Get URL parameters from Square POS app return
      const urlParams = new URLSearchParams(window.location.search)
      
      // Restore session data
      const storedSession = sessionStorage.getItem('square_reader_session')
      const storedTransaction = sessionStorage.getItem('square_reader_transaction')
      
      if (storedSession) {
        const session = JSON.parse(storedSession)
        setSessionData(session)
        console.log('Restored session data:', session)
      }

      // Process different callback scenarios
      if (urlParams.has('data')) {
        // iOS callback with JSON data
        await processIOSCallback(urlParams, storedTransaction)
      } else {
        // Android callback with individual parameters
        await processAndroidCallback(urlParams, storedTransaction)
      }

    } catch (error) {
      console.error('Error processing Square callback:', error)
      setError(error.message)
      setProcessing(false)
    }
  }

  const processIOSCallback = async (urlParams, storedTransaction) => {
    try {
      const dataParam = urlParams.get('data')
      if (!dataParam) {
        throw new Error('No data parameter received from Square POS')
      }

      const data = JSON.parse(decodeURIComponent(dataParam))
      console.log('iOS callback data:', data)

      if (data.error_code) {
        // Handle error response
        const errorMessage = getErrorMessage(data.error_code)
        setError(errorMessage)
        setProcessing(false)
        
        // Record failed transaction
        await recordTransactionResult({
          status: 'failed',
          error_code: data.error_code,
          error_message: errorMessage,
          client_transaction_id: data.client_transaction_id
        })
        
        return
      }

      // Handle successful transaction
      const transactionResult = {
        transactionId: data.transaction_id,
        clientTransactionId: data.client_transaction_id,
        status: data.status || 'completed',
        platform: 'ios'
      }

      await handleSuccessfulTransaction(transactionResult, storedTransaction)

    } catch (error) {
      console.error('Error processing iOS callback:', error)
      throw error
    }
  }

  const processAndroidCallback = async (urlParams, storedTransaction) => {
    try {
      console.log('Android callback parameters:', Object.fromEntries(urlParams))

      const errorCode = urlParams.get('com.squareup.pos.ERROR_CODE')
      
      if (errorCode) {
        // Handle error response
        const errorMessage = getErrorMessage(errorCode)
        setError(errorMessage)
        setProcessing(false)
        
        // Record failed transaction
        await recordTransactionResult({
          status: 'failed',
          error_code: errorCode,
          error_message: errorMessage,
          client_transaction_id: urlParams.get('com.squareup.pos.CLIENT_TRANSACTION_ID')
        })
        
        return
      }

      // Handle successful transaction
      const transactionResult = {
        transactionId: urlParams.get('com.squareup.pos.SERVER_TRANSACTION_ID'),
        clientTransactionId: urlParams.get('com.squareup.pos.CLIENT_TRANSACTION_ID'),
        status: 'completed',
        platform: 'android'
      }

      await handleSuccessfulTransaction(transactionResult, storedTransaction)

    } catch (error) {
      console.error('Error processing Android callback:', error)
      throw error
    }
  }

  const handleSuccessfulTransaction = async (transactionResult, storedTransaction) => {
    try {
      console.log('Processing successful transaction:', transactionResult)

      // Parse stored transaction data
      let transactionData = {}
      if (storedTransaction) {
        transactionData = JSON.parse(storedTransaction)
      }

      // Create payment record in database
      const paymentResult = await createPaymentRecord({
        ...transactionResult,
        ...transactionData,
        amount: sessionData?.amount || transactionData.amount,
        currency: sessionData?.currency || transactionData.currency,
        orderDetails: sessionData?.orderDetails || transactionData.orderDetails
      })

      setResult({
        success: true,
        transactionId: transactionResult.transactionId,
        clientTransactionId: transactionResult.clientTransactionId,
        amount: sessionData?.amount || transactionData.amount,
        currency: sessionData?.currency || transactionData.currency,
        paymentId: paymentResult.paymentId
      })

      setProcessing(false)

      // Auto-redirect back to POS after 3 seconds
      setTimeout(() => {
        redirectToPOS(paymentResult)
      }, 3000)

    } catch (error) {
      console.error('Error handling successful transaction:', error)
      throw error
    }
  }

  const createPaymentRecord = async (transactionData) => {
    try {
      const response = await fetch('/api/admin/pos/process-payment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        },
        body: JSON.stringify({
          paymentMethod: 'square_reader',
          paymentDetails: {
            transactionId: transactionData.transactionId,
            clientTransactionId: transactionData.clientTransactionId,
            amount: transactionData.amount,
            currency: transactionData.currency,
            platform: transactionData.platform,
            readerType: transactionData.readerType
          },
          orderDetails: transactionData.orderDetails,
          sessionId: sessionData?.sessionId
        })
      })

      if (!response.ok) {
        throw new Error('Failed to create payment record')
      }

      const result = await response.json()
      return result

    } catch (error) {
      console.error('Error creating payment record:', error)
      throw error
    }
  }

  const recordTransactionResult = async (resultData) => {
    try {
      const response = await fetch('/api/admin/pos/reader-transaction-result', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        },
        body: JSON.stringify(resultData)
      })

      if (!response.ok) {
        console.error('Failed to record transaction result')
      }
    } catch (error) {
      console.error('Error recording transaction result:', error)
    }
  }

  const getErrorMessage = (errorCode) => {
    const errorMessages = {
      'user_canceled': 'Payment was cancelled by user',
      'invalid_request': 'Invalid payment request',
      'unauthorized': 'Unauthorized access to Square POS',
      'payment_declined': 'Payment was declined',
      'network_error': 'Network connection error',
      'pos_not_installed': 'Square POS app is not installed',
      'pos_not_logged_in': 'Please log in to Square POS app first',
      'reader_not_connected': 'Card reader is not connected',
      'reader_error': 'Card reader error occurred'
    }
    
    return errorMessages[errorCode] || `Payment failed: ${errorCode}`
  }

  const redirectToPOS = (paymentResult) => {
    // Clean up session storage
    sessionStorage.removeItem('square_reader_session')
    sessionStorage.removeItem('square_reader_transaction')
    
    // Redirect back to POS with success parameters
    const redirectUrl = `/admin/pos?payment_success=true&payment_id=${paymentResult.paymentId}&transaction_id=${paymentResult.transactionId}`
    router.push(redirectUrl)
  }

  const handleManualReturn = () => {
    // Clean up and return to POS
    sessionStorage.removeItem('square_reader_session')
    sessionStorage.removeItem('square_reader_transaction')
    router.push('/admin/pos')
  }

  return (
    <AuthenticationGuard>
      <ProtectedRoute adminOnly>
        <AdminLayout title="Square Reader Payment">
          <div className={styles.callbackContainer}>
            {processing && (
              <div className={styles.processingCallback}>
                <div className={styles.loadingSpinner}></div>
                <h2>Processing Payment Result</h2>
                <p>Please wait while we process your Square Reader payment...</p>
              </div>
            )}

            {result && (
              <div className={styles.successCallback}>
                <div className={styles.successIcon}>✅</div>
                <h2>Payment Successful!</h2>
                <div className={styles.resultDetails}>
                  <div className={styles.resultItem}>
                    <span className={styles.resultLabel}>Amount:</span>
                    <span className={styles.resultValue}>
                      ${parseFloat(result.amount || 0).toFixed(2)} {result.currency}
                    </span>
                  </div>
                  <div className={styles.resultItem}>
                    <span className={styles.resultLabel}>Transaction ID:</span>
                    <span className={styles.resultValue}>{result.transactionId}</span>
                  </div>
                  <div className={styles.resultItem}>
                    <span className={styles.resultLabel}>Payment ID:</span>
                    <span className={styles.resultValue}>{result.paymentId}</span>
                  </div>
                </div>
                <p className={styles.redirectMessage}>
                  Returning to POS Terminal in 3 seconds...
                </p>
                <button 
                  className={styles.returnButton}
                  onClick={handleManualReturn}
                >
                  Return to POS Now
                </button>
              </div>
            )}

            {error && (
              <div className={styles.errorCallback}>
                <div className={styles.errorIcon}>❌</div>
                <h2>Payment Failed</h2>
                <p className={styles.errorMessage}>{error}</p>
                <button 
                  className={styles.returnButton}
                  onClick={handleManualReturn}
                >
                  Return to POS
                </button>
              </div>
            )}

            {sessionData && (
              <div className={styles.sessionInfo}>
                <h3>Session Information</h3>
                <div className={styles.sessionDetails}>
                  <div className={styles.sessionItem}>
                    <span>Service:</span>
                    <span>{sessionData.orderDetails?.service}</span>
                  </div>
                  <div className={styles.sessionItem}>
                    <span>Amount:</span>
                    <span>${parseFloat(sessionData.amount || 0).toFixed(2)}</span>
                  </div>
                  <div className={styles.sessionItem}>
                    <span>Session ID:</span>
                    <span>{sessionData.sessionId}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </AdminLayout>
      </ProtectedRoute>
    </AuthenticationGuard>
  )
}
