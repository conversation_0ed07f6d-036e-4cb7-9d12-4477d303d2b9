<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>POS Terminal Service Synchronization Audit</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1400px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%); color: white; padding: 2rem; border-radius: 10px; margin-bottom: 2rem; }
        .audit-section { background: white; margin: 20px 0; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .success { background-color: #d4edda; border-left: 4px solid #28a745; padding: 15px; margin: 10px 0; }
        .error { background-color: #f8d7da; border-left: 4px solid #dc3545; padding: 15px; margin: 10px 0; }
        .warning { background-color: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 10px 0; }
        .info { background-color: #d1ecf1; border-left: 4px solid #17a2b8; padding: 15px; margin: 10px 0; }
        .comparison-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0; }
        .mode-panel { background: #f8f9fa; padding: 20px; border-radius: 8px; border: 2px solid #dee2e6; }
        .mode-panel.full-booking { border-color: #007bff; }
        .mode-panel.quick-event { border-color: #28a745; }
        .mode-title { font-size: 1.2rem; font-weight: bold; margin-bottom: 15px; text-align: center; }
        .service-list { max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; background: white; }
        .service-item { padding: 8px; border-bottom: 1px solid #eee; display: flex; justify-content: between; align-items: center; }
        .service-name { font-weight: 500; flex: 1; }
        .service-flags { display: flex; gap: 5px; }
        .flag { padding: 2px 6px; border-radius: 3px; font-size: 11px; font-weight: bold; }
        .flag.true { background: #28a745; color: white; }
        .flag.false { background: #dc3545; color: white; }
        .flag.undefined { background: #6c757d; color: white; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .stat-card { background: white; padding: 15px; border-radius: 8px; text-align: center; border: 1px solid #ddd; }
        .stat-number { font-size: 2rem; font-weight: bold; color: #4ECDC4; }
        .stat-label { color: #666; font-size: 0.9rem; }
        button { background: #4ECDC4; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 1rem; margin: 5px; }
        button:hover { background: #44A08D; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .loading { text-align: center; padding: 40px; }
        .spinner { border: 4px solid #f3f3f3; border-top: 4px solid #4ECDC4; border-radius: 50%; width: 40px; height: 40px; animation: spin 1s linear infinite; margin: 0 auto; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .discrepancy { background: #fff3cd; border: 1px solid #ffc107; padding: 10px; margin: 5px 0; border-radius: 4px; }
        .sync-status { padding: 10px; border-radius: 6px; margin: 10px 0; font-weight: bold; }
        .sync-status.perfect { background: #d4edda; color: #155724; }
        .sync-status.issues { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏪 POS Terminal Service Synchronization Audit</h1>
            <p>Comprehensive analysis of service synchronization between Full Booking Mode and Quick Event Mode</p>
        </div>

        <div class="audit-section">
            <h2>🎯 Audit Controls</h2>
            <button onclick="runComprehensiveAudit()" id="auditBtn">Run Comprehensive Audit</button>
            <button onclick="clearResults()">Clear Results</button>
            <button onclick="exportReport()">Export Report</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        let auditData = {
            apiServices: [],
            fullBookingServices: [],
            quickEventServices: [],
            discrepancies: [],
            timestamp: null
        };

        async function runComprehensiveAudit() {
            const auditBtn = document.getElementById('auditBtn');
            auditBtn.disabled = true;
            auditBtn.textContent = 'Running Audit...';
            
            clearResults();
            showLoading();

            try {
                // Step 1: Fetch API data
                await auditAPIEndpoint();
                
                // Step 2: Simulate component filtering
                await simulateComponentFiltering();
                
                // Step 3: Compare and analyze
                await compareServiceLists();
                
                // Step 4: Generate recommendations
                await generateRecommendations();
                
                addResult('✅ Comprehensive audit completed successfully!', 'success');
            } catch (error) {
                addResult(`❌ Audit failed: ${error.message}`, 'error');
            } finally {
                auditBtn.disabled = false;
                auditBtn.textContent = 'Run Comprehensive Audit';
                hideLoading();
            }
        }

        async function auditAPIEndpoint() {
            addResult('🔍 Step 1: Auditing POS Services API Endpoint...', 'info');
            
            try {
                const response = await fetch('/api/admin/pos/services-with-artists');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                auditData.apiServices = data.services || [];
                auditData.timestamp = new Date().toISOString();
                
                addResult(`✅ API returned ${auditData.apiServices.length} services`, 'success');
                
                // Analyze API data
                const visibilityStats = analyzeVisibilityFlags(auditData.apiServices);
                displayVisibilityStats(visibilityStats);
                
            } catch (error) {
                throw new Error(`API audit failed: ${error.message}`);
            }
        }

        async function simulateComponentFiltering() {
            addResult('🔍 Step 2: Simulating Component Filtering Logic...', 'info');
            
            // Simulate ServiceTileGrid (Full Booking) - NO FILTERING
            auditData.fullBookingServices = [...auditData.apiServices];
            addResult(`📊 Full Booking Mode: ${auditData.fullBookingServices.length} services (no filtering applied)`, 'info');
            
            // Simulate QuickEventServiceSelector filtering
            auditData.quickEventServices = auditData.apiServices.filter(service => {
                const visibleOnEvents = service.visible_on_events === true || service.visible_on_events === 'true';
                const visibleOnPos = service.visible_on_pos === true || service.visible_on_pos === 'true';
                const hasVisibilityFlags = service.visible_on_events !== undefined || service.visible_on_pos !== undefined;
                return hasVisibilityFlags ? (visibleOnEvents || visibleOnPos) : true;
            });
            
            addResult(`📊 Quick Event Mode: ${auditData.quickEventServices.length} services (visibility filtering applied)`, 'info');
        }

        async function compareServiceLists() {
            addResult('🔍 Step 3: Comparing Service Lists...', 'info');
            
            const fullBookingIds = new Set(auditData.fullBookingServices.map(s => s.id));
            const quickEventIds = new Set(auditData.quickEventServices.map(s => s.id));
            
            // Find discrepancies
            const onlyInFullBooking = auditData.fullBookingServices.filter(s => !quickEventIds.has(s.id));
            const onlyInQuickEvent = auditData.quickEventServices.filter(s => !fullBookingIds.has(s.id));
            
            auditData.discrepancies = {
                onlyInFullBooking,
                onlyInQuickEvent,
                totalDiscrepancies: onlyInFullBooking.length + onlyInQuickEvent.length
            };
            
            // Display comparison
            displayServiceComparison();
            
            if (auditData.discrepancies.totalDiscrepancies === 0) {
                addResult('✅ Perfect synchronization: Both modes show identical services', 'success');
            } else {
                addResult(`⚠️ Synchronization issues found: ${auditData.discrepancies.totalDiscrepancies} discrepancies`, 'warning');
            }
        }

        function analyzeVisibilityFlags(services) {
            return {
                total: services.length,
                visibleOnPos: services.filter(s => s.visible_on_pos === true).length,
                visibleOnEvents: services.filter(s => s.visible_on_events === true).length,
                visibleOnBoth: services.filter(s => s.visible_on_pos === true && s.visible_on_events === true).length,
                noVisibilityFlags: services.filter(s => s.visible_on_pos === undefined && s.visible_on_events === undefined).length
            };
        }

        function displayVisibilityStats(stats) {
            const statsHTML = `
                <div class="audit-section">
                    <h3>📊 Service Visibility Analysis</h3>
                    <div class="stats">
                        <div class="stat-card">
                            <div class="stat-number">${stats.total}</div>
                            <div class="stat-label">Total Services</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${stats.visibleOnPos}</div>
                            <div class="stat-label">Visible on POS</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${stats.visibleOnEvents}</div>
                            <div class="stat-label">Visible on Events</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${stats.visibleOnBoth}</div>
                            <div class="stat-label">Visible on Both</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">${stats.noVisibilityFlags}</div>
                            <div class="stat-label">No Visibility Flags</div>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('results').insertAdjacentHTML('beforeend', statsHTML);
        }

        function displayServiceComparison() {
            const syncStatus = auditData.discrepancies.totalDiscrepancies === 0 ? 'perfect' : 'issues';
            const syncMessage = auditData.discrepancies.totalDiscrepancies === 0 
                ? '✅ Perfect Synchronization' 
                : `⚠️ ${auditData.discrepancies.totalDiscrepancies} Synchronization Issues`;
            
            const comparisonHTML = `
                <div class="audit-section">
                    <h3>🔄 Service List Comparison</h3>
                    <div class="sync-status ${syncStatus}">${syncMessage}</div>
                    
                    <div class="comparison-grid">
                        <div class="mode-panel full-booking">
                            <div class="mode-title">📋 Full Booking Mode (${auditData.fullBookingServices.length} services)</div>
                            <div class="service-list">
                                ${auditData.fullBookingServices.map(service => `
                                    <div class="service-item">
                                        <span class="service-name">${service.name}</span>
                                        <div class="service-flags">
                                            <span class="flag ${service.visible_on_pos}">${service.visible_on_pos === true ? 'POS' : service.visible_on_pos === false ? 'No POS' : 'POS?'}</span>
                                            <span class="flag ${service.visible_on_events}">${service.visible_on_events === true ? 'Events' : service.visible_on_events === false ? 'No Events' : 'Events?'}</span>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                        
                        <div class="mode-panel quick-event">
                            <div class="mode-title">⚡ Quick Event Mode (${auditData.quickEventServices.length} services)</div>
                            <div class="service-list">
                                ${auditData.quickEventServices.map(service => `
                                    <div class="service-item">
                                        <span class="service-name">${service.name}</span>
                                        <div class="service-flags">
                                            <span class="flag ${service.visible_on_pos}">${service.visible_on_pos === true ? 'POS' : service.visible_on_pos === false ? 'No POS' : 'POS?'}</span>
                                            <span class="flag ${service.visible_on_events}">${service.visible_on_events === true ? 'Events' : service.visible_on_events === false ? 'No Events' : 'Events?'}</span>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                    
                    ${auditData.discrepancies.onlyInFullBooking.length > 0 ? `
                        <div class="discrepancy">
                            <strong>⚠️ Services only in Full Booking Mode (${auditData.discrepancies.onlyInFullBooking.length}):</strong><br>
                            ${auditData.discrepancies.onlyInFullBooking.map(s => `• ${s.name} (POS: ${s.visible_on_pos}, Events: ${s.visible_on_events})`).join('<br>')}
                        </div>
                    ` : ''}
                    
                    ${auditData.discrepancies.onlyInQuickEvent.length > 0 ? `
                        <div class="discrepancy">
                            <strong>⚠️ Services only in Quick Event Mode (${auditData.discrepancies.onlyInQuickEvent.length}):</strong><br>
                            ${auditData.discrepancies.onlyInQuickEvent.map(s => `• ${s.name} (POS: ${s.visible_on_pos}, Events: ${s.visible_on_events})`).join('<br>')}
                        </div>
                    ` : ''}
                </div>
            `;
            
            document.getElementById('results').insertAdjacentHTML('beforeend', comparisonHTML);
        }

        async function generateRecommendations() {
            addResult('🔍 Step 4: Generating Recommendations...', 'info');
            
            let recommendations = [];
            
            if (auditData.discrepancies.totalDiscrepancies > 0) {
                recommendations.push('🔧 CRITICAL: Implement consistent filtering logic in both components');
                recommendations.push('📝 Update ServiceTileGrid to apply the same visibility filtering as QuickEventServiceSelector');
                recommendations.push('🔄 Ensure both components use identical service filtering criteria');
            }
            
            if (auditData.discrepancies.onlyInFullBooking.length > 0) {
                recommendations.push(`⚠️ ${auditData.discrepancies.onlyInFullBooking.length} services appear only in Full Booking Mode due to missing/false visibility flags`);
            }
            
            const noVisibilityFlags = auditData.apiServices.filter(s => s.visible_on_pos === undefined && s.visible_on_events === undefined);
            if (noVisibilityFlags.length > 0) {
                recommendations.push(`🏷️ ${noVisibilityFlags.length} services have undefined visibility flags - set default values`);
            }
            
            if (recommendations.length === 0) {
                recommendations.push('✅ No issues found - service synchronization is working correctly');
            }
            
            const recommendationsHTML = `
                <div class="audit-section">
                    <h3>💡 Recommendations</h3>
                    ${recommendations.map(rec => `<div class="info">${rec}</div>`).join('')}
                </div>
            `;
            
            document.getElementById('results').insertAdjacentHTML('beforeend', recommendationsHTML);
        }

        function addResult(message, type = 'info') {
            const resultHTML = `<div class="${type}">${message}</div>`;
            document.getElementById('results').insertAdjacentHTML('beforeend', resultHTML);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            auditData = { apiServices: [], fullBookingServices: [], quickEventServices: [], discrepancies: [], timestamp: null };
        }

        function showLoading() {
            const loadingHTML = `
                <div class="audit-section loading" id="loading">
                    <div class="spinner"></div>
                    <p>Running comprehensive audit...</p>
                </div>
            `;
            document.getElementById('results').insertAdjacentHTML('beforeend', loadingHTML);
        }

        function hideLoading() {
            const loading = document.getElementById('loading');
            if (loading) loading.remove();
        }

        function exportReport() {
            if (!auditData.timestamp) {
                alert('Please run an audit first');
                return;
            }
            
            const report = {
                timestamp: auditData.timestamp,
                summary: {
                    totalServices: auditData.apiServices.length,
                    fullBookingServices: auditData.fullBookingServices.length,
                    quickEventServices: auditData.quickEventServices.length,
                    discrepancies: auditData.discrepancies.totalDiscrepancies
                },
                discrepancies: auditData.discrepancies,
                services: auditData.apiServices.map(s => ({
                    id: s.id,
                    name: s.name,
                    visible_on_pos: s.visible_on_pos,
                    visible_on_events: s.visible_on_events,
                    inFullBooking: auditData.fullBookingServices.some(fs => fs.id === s.id),
                    inQuickEvent: auditData.quickEventServices.some(qs => qs.id === s.id)
                }))
            };
            
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `pos-sync-audit-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // Auto-run audit on page load
        window.addEventListener('load', () => {
            addResult('🌊 POS Terminal Service Synchronization Audit Ready', 'info');
            addResult('Click "Run Comprehensive Audit" to analyze service synchronization between Full Booking and Quick Event modes', 'info');
        });
    </script>
</body>
</html>
