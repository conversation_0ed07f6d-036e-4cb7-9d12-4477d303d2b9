-- Ocean Soul Sparkles Services Database Optimization for Quick Event Mode
-- This script optimizes services for Quick Event Mode while preserving existing data relationships
-- SAFE APPROACH: Updates existing services and adds new ones without breaking foreign keys

-- Start transaction
BEGIN;

-- First, let's backup existing data by creating a backup table
CREATE TABLE IF NOT EXISTS services_backup_pre_optimization AS
SELECT * FROM services;

CREATE TABLE IF NOT EXISTS service_pricing_tiers_backup_pre_optimization AS
SELECT * FROM service_pricing_tiers;

-- Step 1: Update visibility flags for existing services to hide them from Quick Event Mode initially
UPDATE services SET
    visible_on_events = false,
    visible_on_pos = false
WHERE status = 'active';

-- Step 2: Clear only the pricing tiers (these can be safely deleted as they don't have critical foreign keys)
DELETE FROM service_pricing_tiers;

-- Step 3: Insert optimized services for Quick Event Mode
-- These will be the ONLY services visible in Quick Event Mode and Events interfaces

-- Category: Special (3 services - note: removing duplicate Custom Pair from Hair & Braiding)
INSERT INTO services (id, name, description, duration, price, color, category, status, visible_on_public, visible_on_pos, visible_on_events) VALUES
(gen_random_uuid(), 'Custom Pair', 'Custom designed pair for special occasions', 5, 150.00, '#FF6B6B', 'Special', 'active', true, true, true),
(gen_random_uuid(), 'PreMade Set', 'Ready-to-go pre-made set', 5, 80.00, '#4ECDC4', 'Special', 'active', true, true, true),
(gen_random_uuid(), 'PreMade Pair', 'Ready-to-go pre-made pair', 5, 150.00, '#45B7D1', 'Special', 'active', true, true, true);

-- Category: Body Painting (5 services)
INSERT INTO services (id, name, description, duration, price, color, category, status, visible_on_public, visible_on_pos, visible_on_events) VALUES
(gen_random_uuid(), 'BodyPaint Regular', 'Regular body painting session with variable pricing', 30, 0.00, '#FF9F43', 'Body Painting', 'active', true, true, true),
(gen_random_uuid(), 'BodyPaint Small', 'Small area body painting', 5, 40.00, '#FF9F43', 'Body Painting', 'active', true, true, true),
(gen_random_uuid(), 'BodyPaint Medium', 'Medium area body painting', 10, 65.00, '#FF9F43', 'Body Painting', 'active', true, true, true),
(gen_random_uuid(), 'BodyPaint Large', 'Large area body painting', 15, 90.00, '#FF9F43', 'Body Painting', 'active', true, true, true),
(gen_random_uuid(), 'BodyPaint XLarge', 'Extra large area body painting', 20, 150.00, '#FF9F43', 'Body Painting', 'active', true, true, true);

-- Category: Airbrush (4 services)
INSERT INTO services (id, name, description, duration, price, color, category, status, visible_on_public, visible_on_pos, visible_on_events) VALUES
(gen_random_uuid(), 'Full Torso & Extremities', 'Complete airbrush coverage for torso and extremities', 45, 275.00, '#A55EEA', 'Airbrush', 'active', true, true, true),
(gen_random_uuid(), 'Facepaint', 'Airbrush face painting', 5, 30.00, '#A55EEA', 'Airbrush', 'active', true, true, true),
(gen_random_uuid(), 'Neck Tattoo', 'Airbrush neck tattoo design', 5, 40.00, '#A55EEA', 'Airbrush', 'active', true, true, true),
(gen_random_uuid(), 'Popular: Face + Neck', 'Popular combination of face and neck airbrush', 5, 45.00, '#A55EEA', 'Airbrush', 'active', true, true, true);

-- Category: Face Painting (4 services)
INSERT INTO services (id, name, description, duration, price, color, category, status, visible_on_public, visible_on_pos, visible_on_events) VALUES
(gen_random_uuid(), 'Facepaint & Small', 'Face painting with small additional area', 5, 55.00, '#26DE81', 'Face Painting', 'active', true, true, true),
(gen_random_uuid(), 'Face + Medium Area', 'Face painting with medium additional area', 10, 80.00, '#26DE81', 'Face Painting', 'active', true, true, true),
(gen_random_uuid(), 'Face + Large area', 'Face painting with large additional area', 15, 110.00, '#26DE81', 'Face Painting', 'active', true, true, true),
(gen_random_uuid(), 'Kids facepaint', 'Special face painting for children', 5, 20.00, '#26DE81', 'Face Painting', 'active', true, true, true);

-- Category: Glitter & Gems (8 services)
INSERT INTO services (id, name, description, duration, price, color, category, status, visible_on_public, visible_on_pos, visible_on_events) VALUES
(gen_random_uuid(), 'Glitter One Side', 'Glitter application on one side', 5, 25.00, '#FD79A8', 'Glitter & Gems', 'active', true, true, true),
(gen_random_uuid(), 'Glitter Both Sides', 'Glitter application on both sides', 5, 30.00, '#FD79A8', 'Glitter & Gems', 'active', true, true, true),
(gen_random_uuid(), 'Glitter Shoulder & 1 Eye', 'Glitter on shoulder and one eye', 5, 40.00, '#FD79A8', 'Glitter & Gems', 'active', true, true, true),
(gen_random_uuid(), 'Glitter Chest', 'Glitter application on chest area', 10, 50.00, '#FD79A8', 'Glitter & Gems', 'active', true, true, true),
(gen_random_uuid(), 'Glitter Butt', 'Glitter application on buttocks area', 10, 55.00, '#FD79A8', 'Glitter & Gems', 'active', true, true, true),
(gen_random_uuid(), 'Glitter Boobs', 'Glitter application on breast area', 10, 60.00, '#FD79A8', 'Glitter & Gems', 'active', true, true, true),
(gen_random_uuid(), 'Short Beard', 'Glitter application for short beard', 5, 30.00, '#FD79A8', 'Glitter & Gems', 'active', true, true, true),
(gen_random_uuid(), 'Long Beard', 'Glitter application for long beard', 5, 40.00, '#FD79A8', 'Glitter & Gems', 'active', true, true, true);

-- Category: Hair & Braiding (16 services - consolidated from duplicates)
INSERT INTO services (id, name, description, duration, price, color, category, status, visible_on_public, visible_on_pos, visible_on_events) VALUES
(gen_random_uuid(), 'Add on - Reusable Bubble Hairtie', 'Reusable bubble hair tie add-on service', 25, 165.00, '#6C5CE7', 'Hair & Braiding', 'active', true, true, true),
(gen_random_uuid(), '2 braids with extensions & a pair', 'Two braids with extensions and a pair', 45, 250.00, '#6C5CE7', 'Hair & Braiding', 'active', true, true, true),
(gen_random_uuid(), 'Regular', 'Regular braiding service with variable pricing', 20, 0.00, '#6C5CE7', 'Hair & Braiding', 'active', true, true, true),
(gen_random_uuid(), 'Favourite: 2 braids EXT, 2 Reusable', 'Popular choice: 2 braids with extensions and 2 reusable accessories', 55, 250.00, '#6C5CE7', 'Hair & Braiding', 'active', true, true, true),
(gen_random_uuid(), '1 Braid', 'Single braid styling', 20, 40.00, '#6C5CE7', 'Hair & Braiding', 'active', true, true, true),
(gen_random_uuid(), '2 Braids', 'Two braid styling', 30, 55.00, '#6C5CE7', 'Hair & Braiding', 'active', true, true, true),
(gen_random_uuid(), '2 Boxer Braids & Extensions', 'Two boxer braids with extensions', 40, 100.00, '#6C5CE7', 'Hair & Braiding', 'active', true, true, true),
(gen_random_uuid(), '2 Small Front or Back Braids', 'Two small braids at front or back', 40, 55.00, '#6C5CE7', 'Hair & Braiding', 'active', true, true, true),
(gen_random_uuid(), '2 Small Front Braids, with Space B', 'Two small front braids with space buns', 40, 95.00, '#6C5CE7', 'Hair & Braiding', 'active', true, true, true),
(gen_random_uuid(), '3x Side braids', 'Three side braids styling', 30, 55.00, '#6C5CE7', 'Hair & Braiding', 'active', true, true, true),
(gen_random_uuid(), '4 Boxer Braids - No EXT', 'Four boxer braids without extensions', 60, 100.00, '#6C5CE7', 'Hair & Braiding', 'active', true, true, true),
(gen_random_uuid(), '4 Boxer Braids with EXT', 'Four boxer braids with extensions', 80, 170.00, '#6C5CE7', 'Hair & Braiding', 'active', true, true, true),
(gen_random_uuid(), '6 Boxer Braids No EXT', 'Six boxer braids without extensions', 80, 140.00, '#6C5CE7', 'Hair & Braiding', 'active', true, true, true),
(gen_random_uuid(), '6 Braids EXT (down/pony)', 'Six braids with extensions in down or ponytail style', 105, 220.00, '#6C5CE7', 'Hair & Braiding', 'active', true, true, true),
(gen_random_uuid(), 'Viking braids (Mohawk & 4/6 side)', 'Viking style with mohawk and 4-6 side braids', 45, 140.00, '#6C5CE7', 'Hair & Braiding', 'active', true, true, true),
(gen_random_uuid(), 'Add on - Reusable Bubble Braids', 'Single reusable bubble braid add-on', 5, 80.00, '#6C5CE7', 'Hair & Braiding', 'active', true, true, true),
(gen_random_uuid(), 'Add on - 2 Reusable Bubble Braids', 'Two reusable bubble braids add-on', 5, 150.00, '#6C5CE7', 'Hair & Braiding', 'active', true, true, true),
(gen_random_uuid(), 'Add on - Braid Border (Each braid)', 'Braid border add-on per braid', 10, 30.00, '#6C5CE7', 'Hair & Braiding', 'active', true, true, true);

-- Step 4: Update service categories table to ensure consistency
UPDATE service_categories SET name = 'Special' WHERE name = 'Special Events';
INSERT INTO service_categories (id, name, description) VALUES
(gen_random_uuid(), 'Body Painting', 'Professional body painting services')
ON CONFLICT (name) DO NOTHING;

-- Step 5: Create a summary view for validation
CREATE OR REPLACE VIEW services_quick_event_summary AS
SELECT
    category,
    COUNT(*) as service_count,
    ARRAY_AGG(name ORDER BY name) as service_names,
    SUM(CASE WHEN visible_on_events AND visible_on_pos THEN 1 ELSE 0 END) as quick_event_ready_count
FROM services
WHERE status = 'active'
GROUP BY category
ORDER BY category;

-- Step 6: Validation query to show the results
SELECT 'QUICK EVENT MODE SERVICES SUMMARY' as info;
SELECT * FROM services_quick_event_summary WHERE quick_event_ready_count > 0;

COMMIT;
