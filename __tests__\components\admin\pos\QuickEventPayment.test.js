import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import QuickEventPayment from '@/components/admin/pos/QuickEventPayment'

// Mock child components
jest.mock('@/components/admin/pos/PaymentMethodSelector', () => ({ onPaymentMethodSelect }) => (
  <div>
    PaymentMethodSelector
    <button onClick={() => onPaymentMethodSelect('cash', {})}>Pay with Cash</button>
    <button onClick={() => onPaymentMethodSelect('card', {})}>Pay with Card</button>
  </div>
))
jest.mock('@/components/admin/pos/POSSquarePaymentNew', () => ({ onSuccess }) => (
  <div>
    POSSquarePaymentNew
    <button onClick={() => onSuccess({ paymentId: 'sq-123', paymentDetails: { some: 'detail' } })}>Simulate Square Success</button>
  </div>
))
jest.mock('@/components/admin/pos/POSSquareTerminal', () => () => <div>POSSquareTerminal</div>) // Simplified for this test
jest.mock('@/components/admin/pos/POSSquareReader', () => () => <div>POSSquareReader</div>) // Simplified for this test

// Mock Supabase auth
jest.mock('@/lib/supabase', () => ({
  supabase: {
    auth: {
      getSession: jest.fn().mockResolvedValue({
        data: { session: { user: { id: 'test-user-id' }, access_token: 'test-token' } },
        error: null,
      }),
    },
  },
}))

// Mock fetch
global.fetch = jest.fn()

const mockCart = [
  {
    service: { id: 's1', name: 'Face Painting Deluxe' },
    tier: { id: 't1b', name: 'Deluxe', duration: 30, price: 20.00 },
  },
  {
    service: { id: 's2', name: 'Glitter Tattoo Sparkly' },
    tier: { id: 't2a', name: 'Small Sparkle', duration: 10, price: 5.50 },
  },
]

const mockEmptyCart = []

describe('QuickEventPayment', () => {
  let mockOnBack
  let mockOnComplete

  beforeEach(() => {
    mockOnBack = jest.fn()
    mockOnComplete = jest.fn()
    global.fetch.mockClear()
    // Mock successful API response by default for fetch
    global.fetch.mockResolvedValueOnce({
      ok: true,
      json: () => Promise.resolve({ success: true, transaction: { id: 'txn_123' } }),
    })
  })

  test('renders correctly with cart items and calculates total amount', () => {
    render(<QuickEventPayment cart={mockCart} onBack={mockOnBack} onComplete={mockOnComplete} />)

    // Check item display
    expect(screen.getByText('Face Painting Deluxe')).toBeInTheDocument()
    expect(screen.getByText(/Deluxe \(30 min\) - \$20.00/)).toBeInTheDocument()
    expect(screen.getByText('Glitter Tattoo Sparkly')).toBeInTheDocument()
    expect(screen.getByText(/Small Sparkle \(10 min\) - \$5.50/)).toBeInTheDocument()

    // Check total amount (20.00 + 5.50 = 25.50)
    expect(screen.getByText('Total:')).toBeInTheDocument()
    expect(screen.getByText('$25.50')).toBeInTheDocument() // Assuming safeFormatCurrency works as expected
  })

  test('handles empty cart gracefully', () => {
    render(<QuickEventPayment cart={mockEmptyCart} onBack={mockOnBack} onComplete={mockOnComplete} />)
    expect(screen.getByText('Total:')).toBeInTheDocument()
    expect(screen.getByText('$0.00')).toBeInTheDocument()
    // Check that no service/tier names are rendered if cart is empty
    expect(screen.queryByText('Face Painting Deluxe')).not.toBeInTheDocument()
  })

  test('processes cash payment and calls createQuickEventTransaction with correct payload', async () => {
    render(<QuickEventPayment cart={mockCart} onBack={mockOnBack} onComplete={mockOnComplete} />)

    fireEvent.click(screen.getByText('Pay with Cash'))

    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledTimes(1)
    })

    expect(global.fetch).toHaveBeenCalledWith(
      '/api/admin/pos/create-quick-event',
      expect.objectContaining({
        method: 'POST',
        headers: expect.objectContaining({
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token',
        }),
        body: JSON.stringify({
          items: mockCart.map(item => ({
            service: { id: item.service.id, name: item.service.name },
            tier: { id: item.tier.id, name: item.tier.name, duration: item.tier.duration, price: item.tier.price },
          })),
          payment: {
            method: 'cash',
            amount: 25.50, // 20.00 + 5.50
            currency: 'AUD',
            details: null, // Cash payment has null details
          },
        }),
      })
    )
    expect(mockOnComplete).toHaveBeenCalledWith(expect.objectContaining({ success: true }))
  })

  test('processes card payment via POSSquarePaymentNew and calls createQuickEventTransaction', async () => {
    render(<QuickEventPayment cart={mockCart} onBack={mockOnBack} onComplete={mockOnComplete} />)

    fireEvent.click(screen.getByText('Pay with Card')) // Select card method

    // POSSquarePaymentNew mock should be visible now
    expect(screen.getByText('POSSquarePaymentNew')).toBeInTheDocument()

    // Simulate a successful payment from POSSquarePaymentNew
    fireEvent.click(screen.getByText('Simulate Square Success'))


    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledTimes(1)
    })

    expect(global.fetch).toHaveBeenCalledWith(
      '/api/admin/pos/create-quick-event',
      expect.objectContaining({
        body: JSON.stringify({
          items: mockCart.map(item => ({
            service: { id: item.service.id, name: item.service.name },
            tier: { id: item.tier.id, name: item.tier.name, duration: item.tier.duration, price: item.tier.price },
          })),
          payment: {
            method: 'card', // Updated based on how paymentMethodType is set in handleSquarePaymentSuccess
            amount: 25.50,
            currency: 'AUD',
            details: { paymentId: 'sq-123', paymentDetails: { some: 'detail' } }
          },
        }),
      })
    )
    expect(mockOnComplete).toHaveBeenCalledWith(expect.objectContaining({ success: true }))
  })

  test('displays error message if createQuickEventTransaction fails', async () => {
    // Override default successful mock for this test
    global.fetch.mockReset() // Clear previous mock
    global.fetch.mockResolvedValueOnce({
      ok: false,
      json: () => Promise.resolve({ error: 'Failed to create transaction' }),
      status: 500,
      statusText: "Internal Server Error"
    })

    render(<QuickEventPayment cart={mockCart} onBack={mockOnBack} onComplete={mockOnComplete} />)

    fireEvent.click(screen.getByText('Pay with Cash'))

    await waitFor(() => {
      expect(screen.getByText('Failed to create transaction')).toBeInTheDocument()
    })
    expect(mockOnComplete).not.toHaveBeenCalled()
  })

  test('calls onBack when back button is clicked', () => {
    render(<QuickEventPayment cart={mockCart} onBack={mockOnBack} onComplete={mockOnComplete} />)
    fireEvent.click(screen.getByText('← Back to Cart'))
    expect(mockOnBack).toHaveBeenCalledTimes(1)
  })
})
