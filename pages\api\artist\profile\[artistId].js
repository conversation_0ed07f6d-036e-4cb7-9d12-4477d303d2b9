import { createClient } from '@supabase/supabase-js';
import { getCurrentUserFromRequest } from '@/lib/supabase';

export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(7);
  console.log(`[${requestId}] /api/artist/profile/[artistId] called, method: ${req.method}`);

  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({ error: 'Method Not Allowed' });
  }

  try {
    // 1. Authenticate user and get their ID and role
    const { user, error: userError } = await getCurrentUserFromRequest(req);

    if (userError || !user) {
      console.error(`[${requestId}] Authentication error:`, userError?.message || 'No user found');
      return res.status(401).json({ error: 'Unauthorized: ' + (userError?.message || 'No user found') });
    }

    console.log(`[${requestId}] Authenticated user: ${user.id}, role: ${user.role}`);

    const { artistId } = req.query;

    // 2. Authorize: Ensure user can access this profile
    // Users can only access their own profile unless they're admin/dev
    const allowedRoles = ['dev', 'admin'];
    const canAccessAnyProfile = allowedRoles.includes(user.role);
    const isOwnProfile = user.id === artistId;

    if (!canAccessAnyProfile && !isOwnProfile) {
      console.warn(`[${requestId}] User ${user.id} attempted to access profile ${artistId}. Forbidden.`);
      return res.status(403).json({ error: 'Forbidden: You can only access your own profile.' });
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.SUPABASE_SERVICE_ROLE_KEY
    );

    // 3. Fetch profile data
    const { data: profile, error: profileError } = await supabase
      .from('artist_profiles')
      .select('*')
      .eq('user_id', artistId)
      .maybeSingle();

    if (profileError) {
      console.error(`[${requestId}] Supabase error fetching artist profile for user ${artistId}:`, profileError);
      return res.status(500).json({ error: 'Failed to fetch profile.', details: profileError.message });
    }

    if (!profile) {
      console.log(`[${requestId}] No profile found for user ${artistId}`);
      return res.status(404).json({ error: 'Profile not found' });
    }

    console.log(`[${requestId}] Profile for user ${artistId} successfully fetched`);

    // 4. Return profile data
    return res.status(200).json(profile);

  } catch (error) {
    console.error(`[${requestId}] Unexpected error in /api/artist/profile/[artistId]:`, error);
    return res.status(500).json({ error: 'Internal Server Error', details: error.message });
  }
}
