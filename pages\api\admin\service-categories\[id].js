import { supabaseAdmin } from '../../../../lib/supabase'; // Adjusted path

// Helper function to serialize category data (duplicated for now)
// TODO: Consider moving to a shared utility file
const serializeCategory = (category) => {
  if (!category) return null;
  return {
    id: String(category.id || ''),
    name: String(category.name || ''),
    description: category.description === null ? null : String(category.description || ''),
    parent_id: category.parent_id === null ? null : String(category.parent_id || ''),
  };
};

export default async function handler(req, res) {
  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({ success: false, error: `Method ${req.method} not allowed.` });
  }

  const { id } = req.query;
  console.log(`🔍 Fetching category by ID: ${id}`);

  if (!id) {
    return res.status(400).json({ success: false, error: 'Category ID is required.' });
  }

  const parsedId = parseInt(id, 10);
  if (isNaN(parsedId)) {
    return res.status(400).json({ success: false, error: 'Category ID must be an integer.' });
  }

  try {
    const { data: category, error } = await supabaseAdmin
      .from('service_categories')
      .select('id, name, description, parent_id')
      .eq('id', parsedId)
      .single();

    if (error) {
      // Check if the error is because the item was not found (PGRST116)
      // Supabase .single() throws an error if no row is found or multiple rows are found.
      if (error.code === 'PGRST116') { // PGRST116: "The result contains 0 rows"
        console.log(`ℹ️ Category with ID ${parsedId} not found.`);
        return res.status(404).json({ success: false, error: `Category with ID ${parsedId} not found.` });
      }
      console.error(`❌ Database error fetching category ID ${parsedId}:`, error);
      throw error; // Throw other errors to be caught by the generic catch block
    }

    // Note: .single() will return null if no row is found (without error if `maybeSingle()` was used, but `single()` errors)
    // The PGRST116 check above handles the "not found" case for .single()
    // If for some reason .single() returned null without an error (e.g. if it was maybeSingle()), this would be a fallback.
    if (!category) {
        console.log(`ℹ️ Category with ID ${parsedId} not found (should have been caught by PGRST116).`);
        return res.status(404).json({ success: false, error: `Category with ID ${parsedId} not found.` });
    }

    const serializedCategory = serializeCategory(category);
    console.log(`✅ Category ID ${parsedId} fetched and serialized:`, serializedCategory);
    res.status(200).json({ success: true, category: serializedCategory });

  } catch (error) {
    console.error(`💥 Error fetching category ID ${parsedId}:`, error);
    res.status(500).json({
      success: false,
      error: `Failed to fetch category with ID ${parsedId}.`,
      details: error.message,
    });
  }
}
