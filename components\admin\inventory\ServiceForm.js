import { useState, useEffect, useCallback, useMemo } from 'react';
import { getAuthToken } from '@/lib/auth-utils';
import { resolveImageUrl } from '@/lib/image-utils';
import PricingTiersForm from './PricingTiersForm';
import styles from '@/styles/admin/ServiceForm.module.css';

export default function ServiceForm({ service, onSave, onCancel, onDelete }) {
  console.log('🔍 ServiceForm - Component function executing', { service });

  // Form state - ensure all values are primitive types to prevent React Error #130
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    duration: '',
    price: '',
    color: '#6a0dad',
    category: '',
    category_id: '',
    image_url: '',
    status: 'active',
    featured: false,
    visible_on_public: true,
    visible_on_pos: true,
    visible_on_events: true
  });

  // Component state
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [uploadingImage, setUploadingImage] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [validationErrors, setValidationErrors] = useState({});
  const [pricingTiers, setPricingTiers] = useState([]);
  // State for dynamic categories
  const [fetchedCategoriesList, setFetchedCategoriesList] = useState([]);
  const [categoriesLoading, setCategoriesLoading] = useState(true);
  const [categoriesError, setCategoriesError] = useState(null);

  // State for "Add New Category" (Inline) Modal
  const [showAddCategoryModal, setShowAddCategoryModal] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [newCategoryDescription, setNewCategoryDescription] = useState('');
  const [newCategoryParentId, setNewCategoryParentId] = useState('');
  const [addCategoryModalError, setAddCategoryModalError] = useState(null);
  const [addCategoryModalSubmitting, setAddCategoryModalSubmitting] = useState(false);

  // State for "Edit Selected Category" (Inline) Modal
  const [showEditCategoryModal, setShowEditCategoryModal] = useState(false);
  const [editingCategoryData, setEditingCategoryData] = useState({ id: null, name: '', description: '', parent_id: '' });
  const [editCategoryModalError, setEditCategoryModalError] = useState(null);
  const [editCategoryModalSubmitting, setEditCategoryModalSubmitting] = useState(false);
  const [fetchingEditCategoryDetails, setFetchingEditCategoryDetails] = useState(false);


  // Stable callback for pricing tiers to prevent infinite loops
  const handlePricingTiersChange = useCallback((newTiers) => {
    setPricingTiers(newTiers);
  }, []);

  // Memoize pricing tiers to prevent infinite loops
  const initialPricingTiers = useMemo(() => {
    // console.log('🔍 ServiceForm - initialPricingTiers useMemo called', { service: service?.id });
    if (!service) return [];
    if (service.pricing_tiers && Array.isArray(service.pricing_tiers)) return service.pricing_tiers;
    return [{
      id: null, name: 'Standard', description: 'Standard service duration and pricing',
      duration: String(service.duration || '60'), price: String(service.price || '50'),
      is_default: true, sort_order: 1
    }];
  }, [service?.id, service?.duration, service?.price, service?.pricing_tiers]);

  // Fetch service categories from API
  const fetchServiceCategories = useCallback(async () => {
    setCategoriesLoading(true);
    setCategoriesError(null);
    try {
      const response = await fetch('/api/admin/service-categories');
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to fetch categories: ${response.status}`);
      }
      const data = await response.json();
      setFetchedCategoriesList(data.categories || []);
    } catch (err) {
      console.error('ServiceForm - Error fetching categories:', err);
      setCategoriesError(err.message);
      setFetchedCategoriesList([]);
    } finally {
      setCategoriesLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchServiceCategories();
  }, [fetchServiceCategories]);

  // Load service data if editing
  useEffect(() => {
    // console.log('🔍 ServiceForm - useEffect for service update called', { serviceId: service?.id });
    if (service) {
      setFormData({
        name: String(service.name || ''),
        description: String(service.description || ''),
        duration: String(service.duration || ''),
        price: String(service.price || ''),
        color: String(service.color || '#6a0dad'),
        category: String(service.category || ''),
        category_id: String(service.category_id || ''),
        image_url: String(resolveImageUrl(service.image_url) || ''),
        status: String(service.status || 'active'),
        featured: Boolean(service.featured),
        visible_on_public: service.visible_on_public !== undefined ? Boolean(service.visible_on_public) : true,
        visible_on_pos: service.visible_on_pos !== undefined ? Boolean(service.visible_on_pos) : true,
        visible_on_events: service.visible_on_events !== undefined ? Boolean(service.visible_on_events) : true,
      });
      setPricingTiers(initialPricingTiers);
    }
  }, [service, initialPricingTiers]);

  // Handle form field changes
  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : String(value),
    }));
    if (validationErrors[name]) {
      setValidationErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  // --- Add Category Modal Functions ---
  const handleOpenAddCategoryModal = () => {
    setShowAddCategoryModal(true);
    setNewCategoryName('');
    setNewCategoryDescription('');
    setNewCategoryParentId('');
    setAddCategoryModalError(null);
  };

  const handleCloseAddCategoryModal = () => {
    setShowAddCategoryModal(false);
  };

  const handleNewCategoryInputChange = (e) => {
    const { name, value } = e.target;
    if (name === 'newCategoryNameModal') setNewCategoryName(value);
    else if (name === 'newCategoryDescriptionModal') setNewCategoryDescription(value);
    else if (name === 'newCategoryParentIdModal') setNewCategoryParentId(value);
  };

  const handleAddNewCategorySubmit = async (e) => {
    e.preventDefault();
    if (!newCategoryName.trim()) {
      setAddCategoryModalError('Category name is required.');
      return;
    }
    setAddCategoryModalError(null);
    setAddCategoryModalSubmitting(true);
    const payload = {
      name: newCategoryName.trim(),
      description: newCategoryDescription.trim() === '' ? null : newCategoryDescription.trim(),
      parent_id: newCategoryParentId ? parseInt(newCategoryParentId, 10) : null,
    };
    try {
      const response = await fetch('/api/admin/service-categories', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      const responseData = await response.json();
      if (!response.ok) throw new Error(responseData.error || `API Error: ${response.status}`);
      alert('Category added successfully!');
      await fetchServiceCategories();
      if (responseData.category && responseData.category.id) {
        setFormData(prev => ({ ...prev, category_id: String(responseData.category.id) }));
      }
      handleCloseAddCategoryModal();
      setNewCategoryName(''); setNewCategoryDescription(''); setNewCategoryParentId('');
    } catch (err) {
      setAddCategoryModalError(err.message || 'Failed to add category.');
    } finally {
      setAddCategoryModalSubmitting(false);
    }
  };

  // --- Edit Category Modal Functions ---
  const handleOpenEditCategoryModal = async () => {
    if (!formData.category_id) {
      alert("Please select a category to edit.");
      return;
    }
    setFetchingEditCategoryDetails(true);
    setEditCategoryModalError(null);
    try {
      const response = await fetch(`/api/admin/service-categories/${formData.category_id}`);
      const responseData = await response.json();
      if (!response.ok) throw new Error(responseData.error || `Failed to fetch category details.`);
      setEditingCategoryData({
        id: responseData.category.id,
        name: responseData.category.name || '',
        description: responseData.category.description || '',
        parent_id: responseData.category.parent_id || '',
      });
      setShowEditCategoryModal(true);
    } catch (err) {
      setError(err.message || 'Failed to load category details.');
    } finally {
      setFetchingEditCategoryDetails(false);
    }
  };

  const handleCloseEditCategoryModal = () => {
    setShowEditCategoryModal(false);
    setEditingCategoryData({ id: null, name: '', description: '', parent_id: '' });
  };

  const handleEditingCategoryInputChange = (e) => {
    const { name, value } = e.target;
    if (name === 'editingCategoryName') setEditingCategoryData(prev => ({ ...prev, name: value }));
    else if (name === 'editingCategoryDescription') setEditingCategoryData(prev => ({ ...prev, description: value }));
    else if (name === 'editingCategoryParentId') setEditingCategoryData(prev => ({ ...prev, parent_id: value }));
  };

  const handleEditCategorySubmit = async (e) => {
    e.preventDefault();
    if (!editingCategoryData.name.trim()) {
      setEditCategoryModalError('Category name is required.');
      return;
    }
    setEditCategoryModalError(null);
    setEditCategoryModalSubmitting(true);
    const payload = {
      id: editingCategoryData.id,
      name: editingCategoryData.name.trim(),
      description: editingCategoryData.description.trim() === '' ? null : editingCategoryData.description.trim(),
      parent_id: editingCategoryData.parent_id ? parseInt(editingCategoryData.parent_id, 10) : null,
    };
    try {
      const response = await fetch(`/api/admin/service-categories`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      const responseData = await response.json();
      if (!response.ok) throw new Error(responseData.error || `API Error: ${response.status}`);
      alert('Category updated successfully!');
      const currentSelectedCatId = editingCategoryData.id; // Keep the current ID selected
      await fetchServiceCategories();
      setFormData(prev => ({ ...prev, category_id: String(currentSelectedCatId) })); // Ensure it remains selected
      handleCloseEditCategoryModal();
    } catch (err) {
      setEditCategoryModalError(err.message || 'Failed to update category.');
    } finally {
      setEditCategoryModalSubmitting(false);
    }
  };

  // --- Main Service Form Functions ---
  const validateForm = () => {
    const errors = {};
    if (!formData.name.trim()) errors.name = 'Service name is required';
    if (!formData.price.trim()) errors.price = 'Price is required';
    else if (isNaN(parseFloat(formData.price)) || parseFloat(formData.price) < 0) errors.price = 'Price must be a valid positive number';
    if (!formData.duration.trim()) errors.duration = 'Duration is required';
    else if (isNaN(parseInt(formData.duration, 10)) || parseInt(formData.duration, 10) <= 0) errors.duration = 'Duration must be a valid positive number (in minutes)';
    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleImageUpload = async (file) => {
    // ... (existing image upload logic - keeping it concise for this diff) ...
    if (!file) return;
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      setError('Invalid file type. Please upload JPEG, PNG, or WebP images only.');
      return;
    }
    if (file.size > 10 * 1024 * 1024) {
      setError('File size too large. Please upload images smaller than 10MB.');
      return;
    }
    setUploadingImage(true); setError(null);
    try {
      const formDataUpload = new FormData(); formDataUpload.append('image', file);
      const token = await getAuthToken();
      const response = await fetch('/api/admin/uploads/image', {
        method: 'POST', headers: { 'Authorization': `Bearer ${token || ''}` }, body: formDataUpload
      });
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to upload image: ${response.status} - ${errorText}`);
      }
      const data = await response.json();
      if (data.success && data.url) {
        setFormData(prev => ({ ...prev, image_url: String(data.url || '') }));
      } else {
        throw new Error(data.error || 'Upload failed - no URL returned');
      }
    } catch (err) { setError('Failed to upload image. Please try again.'); }
    finally { setUploadingImage(false); }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) handleImageUpload(file);
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) {
      setError('Please fix the validation errors before submitting.');
      return;
    }
    setLoading(true); setError(null);
    try {
      if (!pricingTiers || pricingTiers.length === 0) {
        setError('At least one pricing tier is required.'); setLoading(false); return;
      }
      for (let i = 0; i < pricingTiers.length; i++) {
        const tier = pricingTiers[i];
        if (!tier.name || !tier.duration || !tier.price) {
          setError(`Pricing tier ${i + 1} is incomplete.`); setLoading(false); return;
        }
      }
      const serviceData = {
        name: String(formData.name).trim(),
        description: String(formData.description).trim(),
        duration: parseInt(formData.duration, 10),
        price: parseFloat(formData.price),
        color: String(formData.color),
        category: String(formData.category).trim(),
        category_id: formData.category_id ? parseInt(formData.category_id, 10) : null,
        image_url: String(formData.image_url).trim(),
        status: String(formData.status),
        featured: Boolean(formData.featured),
        visible_on_public: Boolean(formData.visible_on_public),
        visible_on_pos: Boolean(formData.visible_on_pos),
        visible_on_events: Boolean(formData.visible_on_events),
        pricingTiers: pricingTiers.map(tier => ({
          id: tier.id, name: String(tier.name).trim(), description: String(tier.description).trim(),
          duration: parseInt(tier.duration, 10), price: parseFloat(tier.price),
          is_default: Boolean(tier.is_default), sort_order: Number(tier.sort_order)
        }))
      };
      const url = service ? `/api/admin/services/${service.id}` : '/api/admin/services';
      const method = service ? 'PUT' : 'POST';
      const token = await getAuthToken();
      const response = await fetch(url, {
        method, headers: { 'Content-Type': 'application/json', 'Authorization': `Bearer ${token || ''}` },
        body: JSON.stringify(serviceData)
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to save service');
      }
      const { service: savedService } = await response.json();
      if (onSave) onSave(savedService);
    } catch (err) { setError(err.message || 'Failed to save service'); }
    finally { setLoading(false); }
  };

  const handleDelete = async () => {
    // ... (existing delete logic - keeping it concise for this diff) ...
    if (!service || !service.id) return;
    setLoading(true); setError(null);
    try {
      const token = await getAuthToken();
      const response = await fetch(`/api/admin/services/${service.id}`, {
        method: 'DELETE', headers: { 'Authorization': `Bearer ${token || ''}`, 'Content-Type': 'application/json' },
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete service');
      }
      setShowDeleteConfirm(false);
      if (onDelete) onDelete(service);
    } catch (err) { setError(err.message || 'Failed to delete service'); }
    finally { setLoading(false); }
  };

  return (
    <div className={styles.serviceForm}>
      {/* Modal for Adding New Category */}
      {showAddCategoryModal && (
        <div className={styles.inlineModalBackdrop}>
          <div className={styles.inlineModal}>
            <h3>Add New Category</h3>
            {addCategoryModalError && <p className={styles.errorTextModal}>{addCategoryModalError}</p>}
            <form onSubmit={handleAddNewCategorySubmit} className={styles.modalForm}>
              <div className={styles.formGroup}>
                <label htmlFor="newCategoryNameModal">Name <span className={styles.requiredField}>*</span></label>
                <input type="text" id="newCategoryNameModal" name="newCategoryNameModal" value={newCategoryName} onChange={handleNewCategoryInputChange} disabled={addCategoryModalSubmitting} required />
              </div>
              <div className={styles.formGroup}>
                <label htmlFor="newCategoryDescriptionModal">Description</label>
                <textarea id="newCategoryDescriptionModal" name="newCategoryDescriptionModal" value={newCategoryDescription} onChange={handleNewCategoryInputChange} disabled={addCategoryModalSubmitting} rows={3} />
              </div>
              <div className={styles.formGroup}>
                <label htmlFor="newCategoryParentIdModal">Parent Category</label>
                <select id="newCategoryParentIdModal" name="newCategoryParentIdModal" value={newCategoryParentId} onChange={handleNewCategoryInputChange} disabled={addCategoryModalSubmitting || categoriesLoading} className={styles.select}>
                  <option value="">None</option>
                  {fetchedCategoriesList.map(cat => (<option key={cat.id} value={cat.id}>{cat.name}</option>))}
                </select>
              </div>
              <div className={styles.formActions}>
                <button type="button" onClick={handleCloseAddCategoryModal} className={styles.cancelButton} disabled={addCategoryModalSubmitting}>Cancel</button>
                <button type="submit" className={styles.saveButton} disabled={addCategoryModalSubmitting}>
                  {addCategoryModalSubmitting ? 'Adding...' : 'Add Category'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Modal for Editing Selected Category */}
      {showEditCategoryModal && (
        <div className={styles.inlineModalBackdrop}>
          <div className={styles.inlineModal}>
            <h3>Edit Category</h3>
            {editCategoryModalError && <p className={styles.errorTextModal}>{editCategoryModalError}</p>}
            <form onSubmit={handleEditCategorySubmit} className={styles.modalForm}>
              <div className={styles.formGroup}>
                <label htmlFor="editingCategoryName">Name <span className={styles.requiredField}>*</span></label>
                <input type="text" id="editingCategoryName" name="editingCategoryName" value={editingCategoryData.name} onChange={handleEditingCategoryInputChange} disabled={editCategoryModalSubmitting} required />
              </div>
              <div className={styles.formGroup}>
                <label htmlFor="editingCategoryDescription">Description</label>
                <textarea id="editingCategoryDescription" name="editingCategoryDescription" value={editingCategoryData.description} onChange={handleEditingCategoryInputChange} disabled={editCategoryModalSubmitting} rows={3} />
              </div>
              <div className={styles.formGroup}>
                <label htmlFor="editingCategoryParentId">Parent Category</label>
                <select id="editingCategoryParentId" name="editingCategoryParentId" value={editingCategoryData.parent_id} onChange={handleEditingCategoryInputChange} disabled={editCategoryModalSubmitting || categoriesLoading} className={styles.select}>
                  <option value="">None</option>
                  {fetchedCategoriesList
                    .filter(cat => String(cat.id) !== String(editingCategoryData.id))
                    .map(cat => (<option key={cat.id} value={cat.id}>{cat.name}</option>
                  ))}
                </select>
              </div>
              <div className={styles.formActions}>
                <button type="button" onClick={handleCloseEditCategoryModal} className={styles.cancelButton} disabled={editCategoryModalSubmitting}>Cancel</button>
                <button type="submit" className={styles.saveButton} disabled={editCategoryModalSubmitting}>
                  {editCategoryModalSubmitting ? 'Saving...' : 'Save Changes'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      <div className={styles.formHeader}>
        <h2>{service ? 'Edit Service' : 'Add New Service'}</h2>
      </div>

      <form className={styles.form} onSubmit={handleSubmit}>
        {error && (<div className={styles.error}>{error}</div>)}

        {/* Basic Information Section */}
        <div className={styles.section}>
          <h3>Basic Information</h3>
          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="name">Service Name <span className={styles.requiredField}>*</span></label>
              <input type="text" id="name" name="name" value={formData.name} onChange={handleChange} className={`${styles.input} ${validationErrors.name ? styles.fieldError : ''}`} disabled={loading} placeholder="Enter service name" />
              {validationErrors.name && (<span className={styles.helpText} style={{ color: '#e74c3c' }}>{validationErrors.name}</span>)}
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="category_id">Category</label>
              <select id="category_id" name="category_id" value={formData.category_id} onChange={handleChange} className={styles.select} disabled={loading || categoriesLoading}>
                <option value="">Select Category</option>
                {categoriesLoading && <option value="" disabled>Loading categories...</option>}
                {categoriesError && <option value="" disabled>Error loading categories</option>}
                {!categoriesLoading && !categoriesError && fetchedCategoriesList.map(cat => (
                  <option key={cat.id} value={cat.id}>{cat.name}</option>
                ))}
              </select>
              {categoriesError && <span className={styles.helpText} style={{ color: '#e74c3c' }}>Failed to load categories: {categoriesError}</span>}
              <div className={styles.categoryActionsContainer}> {/* New container for buttons */}
                {!categoriesLoading && !categoriesError && (
                  <button type="button" onClick={handleOpenAddCategoryModal} className={styles.inlineButton} disabled={loading || fetchingEditCategoryDetails}>
                    + Add New
                  </button>
                )}
                {!categoriesLoading && !categoriesError && formData.category_id && (
                  <button type="button" onClick={handleOpenEditCategoryModal} className={styles.inlineButton} disabled={loading || fetchingEditCategoryDetails || !formData.category_id} >
                    {fetchingEditCategoryDetails ? 'Loading...' : 'Edit Selected'}
                  </button>
                )}
              </div>
            </div>
          </div>
          <div className={styles.formGroup}>
            <label htmlFor="description">Description</label>
            <textarea id="description" name="description" value={formData.description} onChange={handleChange} className={styles.textarea} disabled={loading} placeholder="Describe the service..." rows={4}/>
          </div>
        </div>

        {/* Pricing & Duration Section */}
        <div className={styles.section}>
          <h3>Pricing & Duration</h3>
          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="price">Price (AUD) <span className={styles.requiredField}>*</span></label>
              <input type="number" id="price" name="price" value={formData.price} onChange={handleChange} className={`${styles.input} ${validationErrors.price ? styles.fieldError : ''}`} disabled={loading} placeholder="0.00" step="0.01" min="0"/>
              {validationErrors.price && (<span className={styles.helpText} style={{ color: '#e74c3c' }}>{validationErrors.price}</span>)}
            </div>
            <div className={styles.formGroup}>
              <label htmlFor="duration">Duration (minutes) <span className={styles.requiredField}>*</span></label>
              <input type="number" id="duration" name="duration" value={formData.duration} onChange={handleChange} className={`${styles.input} ${validationErrors.duration ? styles.fieldError : ''}`} disabled={loading} placeholder="60" min="1"/>
              {validationErrors.duration && (<span className={styles.helpText} style={{ color: '#e74c3c' }}>{validationErrors.duration}</span>)}
            </div>
          </div>
        </div>

        {/* Image Section (Existing - keeping concise) */}
        <div className={styles.section}>
          <h3>Service Image</h3>
          {formData.image_url ? (
            <div className={styles.imagePreviewContainer}>
              <div className={styles.imagePreview}><img src={formData.image_url} alt="Service preview" className={styles.previewImage} />
                <div className={styles.imageActions}>
                  <label htmlFor="imageUpload" className={styles.changeImageButton}>{uploadingImage ? (<><span className={styles.loadingSpinner}></span>Uploading...</>) : ('Change Image')}</label>
                  <button type="button" onClick={() => setFormData(prev => ({ ...prev, image_url: '' }))} className={styles.removeImageButton} disabled={loading || uploadingImage}>Remove Image</button>
                </div>
              </div>
              <div className={styles.imagePath}><strong>Current Image:</strong> {formData.image_url}</div>
            </div>
          ) : (
            <div className={styles.noImageContainer}>
              <div className={styles.noImagePlaceholder}><svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><circle cx="8.5" cy="8.5" r="1.5"/><polyline points="21,15 16,10 5,21"/></svg><p>No image selected</p></div>
              <div className={styles.imageUploadOptions}><label htmlFor="imageUpload" className={styles.uploadButton}>{uploadingImage ? (<><span className={styles.loadingSpinner}></span>Uploading...</>) : (<><svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2"><path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/><polyline points="7,10 12,15 17,10"/><line x1="12" y1="15" x2="12" y2="3"/></svg>Upload Image</>)}</label></div>
            </div>
          )}
          <input type="file" id="imageUpload" className={styles.fileInput} accept="image/jpeg,image/jpg,image/png,image/webp" onChange={handleFileChange} disabled={loading || uploadingImage}/>
          <div className={styles.helpText}><p>💡 <strong>Tip:</strong> Upload images directly for best performance and reliability. Supported formats: JPEG, PNG, WebP (max 10MB)</p></div>
        </div>

        {/* Pricing Tiers Section (Existing) */}
        <div className={styles.section}><PricingTiersForm pricingTiers={pricingTiers} onChange={handlePricingTiersChange} /></div>

        {/* Appearance & Settings Section (Existing - keeping concise) */}
        <div className={styles.section}>
          <h3>Appearance & Settings</h3>
          <div className={styles.formRow}>
            <div className={styles.formGroup}><label htmlFor="color">Accent Color</label><input type="color" id="color" name="color" value={formData.color} onChange={handleChange} className={styles.colorInput} disabled={loading}/><span className={styles.helpText}>This color will be used for service highlights and theming</span></div>
            <div className={styles.formGroup}><label htmlFor="status">Status</label><select id="status" name="status" value={formData.status} onChange={handleChange} className={styles.select} disabled={loading}><option value="active">Active</option><option value="inactive">Inactive</option><option value="draft">Draft</option></select></div>
          </div>
          <div className={styles.formGroup}><label className={styles.checkboxLabel}><input type="checkbox" name="featured" checked={formData.featured} onChange={handleChange} disabled={loading}/>Featured Service</label><span className={styles.helpText}>Featured services are highlighted on the services page</span></div>
        </div>

        {/* Service Visibility Section (Existing - keeping concise) */}
        <div className={styles.section}>
          <h3>Service Visibility</h3><p className={styles.helpText}>Control where this service appears. Uncheck to hide from specific areas.</p>
          <div className={styles.visibilityControls}>
            <div className={styles.formGroup}><label className={styles.checkboxLabel}><input type="checkbox" name="visible_on_public" checked={formData.visible_on_public} onChange={handleChange} disabled={loading}/>Public Book-Online Page</label><span className={styles.helpText}>Show this service on the public booking page for customers</span></div>
            <div className={styles.formGroup}><label className={styles.checkboxLabel}><input type="checkbox" name="visible_on_pos" checked={formData.visible_on_pos} onChange={handleChange} disabled={loading}/>POS Terminal</label><span className={styles.helpText}>Show this service in the Point of Sale terminal interface</span></div>
            <div className={styles.formGroup}><label className={styles.checkboxLabel}><input type="checkbox" name="visible_on_events" checked={formData.visible_on_events} onChange={handleChange} disabled={loading}/>Events Booking</label><span className={styles.helpText}>Show this service for special events and photoshoot bookings</span></div>
          </div>
        </div>

        {/* Form Actions (Existing) */}
        <div className={styles.formActions}>
          <div className={styles.leftActions}>{service && (<button type="button" className={styles.deleteButton} onClick={() => setShowDeleteConfirm(true)} disabled={loading}>Delete Service</button>)}</div>
          <div className={styles.rightActions}><button type="button" onClick={onCancel} className={styles.cancelButton} disabled={loading}>Cancel</button><button type="submit" className={styles.saveButton} disabled={loading || uploadingImage}>{loading ? (<><span className={styles.loadingSpinner}></span>{service ? 'Updating...' : 'Creating...'}</>) : (service ? 'Update Service' : 'Create Service')}</button></div>
        </div>

        {/* Delete Confirmation Modal (Existing) */}
        {showDeleteConfirm && (
          <div className={styles.deleteModal}><div className={styles.deleteModalContent}><h3>Confirm Delete</h3><p>Are you sure you want to delete the service "{service?.name}"?</p><p className={styles.warningText}><strong>Warning:</strong> This action cannot be undone. The service will be removed from:</p><ul className={styles.impactList}><li>Admin service management</li>{service?.visible_on_public && <li>Public Book Online page</li>}{service?.visible_on_pos && <li>POS Terminal interface</li>}{service?.visible_on_events && <li>Events booking system</li>}</ul><div className={styles.deleteModalActions}><button type="button" className={styles.cancelButton} onClick={() => setShowDeleteConfirm(false)} disabled={loading}>Cancel</button><button type="button" className={styles.confirmDeleteButton} onClick={handleDelete} disabled={loading}>{loading ? 'Deleting...' : 'Delete Service'}</button></div></div></div>
        )}
      </form>
    </div>
  );
}
