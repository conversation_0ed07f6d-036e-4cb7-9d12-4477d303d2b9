import { supabase } from '@/lib/supabase';
import { safeSerializeData } from '@/lib/safe-render-utils';
import { setCacheHeaders } from '@/lib/cache-control-utils';

/**
 * Public API endpoint for fetching services available for events booking
 * This endpoint provides services data for events and special occasions
 * No authentication required - only returns active services visible on events
 */
export default async function handler(req, res) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Fetch active services with pricing tiers from database
    // Apply visibility filter for events booking
    const { data: services, error } = await supabase
      .from('services_with_pricing')
      .select('*')
      .eq('status', 'active')
      .eq('visible_on_events', true)
      .order('name');

    if (error) {
      console.error('Error fetching events services:', error);
      return res.status(500).json({ error: 'Failed to fetch events services' });
    }

    // Transform database services to match the format expected by the frontend
    const transformedServices = services.map(service => ({
      id: String(service.id || ''),
      title: String(service.name || ''),
      description: String(service.description || ''),
      image: String(service.image_url || '/images/services/face-paint.jpg'),
      category: String(service.category || 'general'),
      icon: getCategoryIcon(service.category),
      pricing: formatPricingTiers(service.pricing_tiers, service.price, service.duration),
      accentColor: String(service.color || '#4ECDC4'),
      duration: Number(service.duration) || 0,
      featured: Boolean(service.featured),
      pricingTiers: Array.isArray(service.pricing_tiers) ? service.pricing_tiers : [],
      // Events-specific metadata
      eventSuitable: true,
      groupBookingAvailable: true,
      customQuoteAvailable: true
    }));

    // Serialize the data to ensure no complex objects are passed to frontend
    const serializedServices = safeSerializeData(transformedServices);

    // Set appropriate cache headers for events services data
    setCacheHeaders(res, 'events_services', 'GET', false, req.query);

    console.log(`✅ Events Services API: Returned ${serializedServices.length} services`);

    return res.status(200).json({ 
      services: serializedServices,
      totalServices: serializedServices.length,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error in events services API:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

/**
 * Get category icon based on service category
 */
function getCategoryIcon(category) {
  const iconMap = {
    'painting': '🎨',
    'face painting': '🎨',
    'airbrush': '🎨',
    'braiding': '💇',
    'hair': '💇',
    'hair & braiding': '💇',
    'glitter': '✨',
    'glitter & gems': '✨',
    'sparkle': '✨',
    'special': '🎭',
    'special events': '🎭',
    'uv': '🌟',
    'body art': '🎨',
    'makeup': '💄'
  };

  return iconMap[category?.toLowerCase()] || '🎨';
}

/**
 * Format pricing tiers for events display
 */
function formatPricingTiers(pricingTiers, fallbackPrice, fallbackDuration) {
  // Check if we have valid pricing tiers (not just null objects)
  const hasValidTiers = pricingTiers &&
    Array.isArray(pricingTiers) &&
    pricingTiers.length > 0 &&
    pricingTiers.some(tier => tier && tier.id && tier.name && tier.price);

  if (hasValidTiers) {
    // Use pricing tiers from database
    return pricingTiers
      .filter(tier => tier && tier.id) // Filter out null objects
      .map(tier => ({
        title: String(`${tier.name || 'Service'} (${tier.duration || 0} min)`),
        price: String(`$${tier.price || 0}`)
      }));
  } else {
    // Fallback to events-specific pricing format using base service price
    return formatEventsPricing(fallbackPrice, fallbackDuration);
  }
}

/**
 * Format pricing information for events based on service price and duration
 */
function formatEventsPricing(price, duration) {
  const basePrice = parseFloat(price) || 0;

  // Generate events-specific pricing tiers
  return [
    {
      title: 'Individual service',
      price: `$${basePrice.toFixed(0)}`
    },
    {
      title: 'Group bookings (5+ people)',
      price: `from $${(basePrice * 0.85).toFixed(0)} per person`
    },
    {
      title: 'Event packages (10+ people)',
      price: `from $${(basePrice * 0.75).toFixed(0)} per person`
    },
    {
      title: 'Corporate events',
      price: 'Custom quote available'
    },
    {
      title: `Duration: ${duration} minutes per person`,
      price: ''
    }
  ];
}
