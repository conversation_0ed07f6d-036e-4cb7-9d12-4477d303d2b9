<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Event Creation - Ocean Soul Sparkles</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { background: linear-gradient(135deg, #4ECDC4 0%, #44A08D 100%); color: white; padding: 2rem; border-radius: 10px; margin-bottom: 2rem; }
        .form-section { background: white; margin: 20px 0; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .form-group { margin-bottom: 20px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; color: #333; }
        .form-group input, .form-group textarea, .form-group select { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 1rem; }
        .form-group textarea { height: 100px; resize: vertical; }
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; }
        .success { background-color: #d4edda; border-left: 4px solid #28a745; padding: 15px; margin: 10px 0; }
        .error { background-color: #f8d7da; border-left: 4px solid #dc3545; padding: 15px; margin: 10px 0; }
        .warning { background-color: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 10px 0; }
        .info { background-color: #d1ecf1; border-left: 4px solid #17a2b8; padding: 15px; margin: 10px 0; }
        button { background: #4ECDC4; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; font-size: 1rem; margin: 5px; }
        button:hover { background: #44A08D; }
        button:disabled { background: #ccc; cursor: not-allowed; }
        .test-purpose { background: #fff3cd; border: 1px solid #ffc107; padding: 20px; margin: 20px 0; border-radius: 8px; }
        .test-purpose h3 { margin-top: 0; color: #856404; }
        .hypothesis { background: #e7f3ff; border: 1px solid #b3d9ff; padding: 15px; margin: 15px 0; border-radius: 8px; }
        .loading { text-align: center; padding: 20px; }
        .spinner { border: 4px solid #f3f3f3; border-top: 4px solid #4ECDC4; border-radius: 50%; width: 30px; height: 30px; animation: spin 1s linear infinite; margin: 0 auto; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Test Event Creation</h1>
            <p>Create a test event to validate the relationship between formal events and POS Terminal Quick Event Mode</p>
        </div>

        <div class="test-purpose">
            <h3>🎯 Test Purpose</h3>
            <p><strong>Hypothesis:</strong> Quick Event Mode in POS Terminal is independent of formal events and uses service visibility flags only.</p>
            <p><strong>Test Method:</strong> Create a formal event and observe if it affects POS Terminal Quick Event Mode service availability.</p>
        </div>

        <div class="hypothesis">
            <strong>Expected Result:</strong> Creating a formal event should NOT change the services available in POS Terminal Quick Event Mode, confirming they are separate systems.
        </div>

        <div class="form-section">
            <h2>📅 Create Test Event</h2>
            <form id="eventForm">
                <div class="form-group">
                    <label for="eventName">Event Name *</label>
                    <input type="text" id="eventName" name="name" value="Test Festival - Architecture Validation" required>
                </div>

                <div class="form-group">
                    <label for="eventLocation">Location *</label>
                    <input type="text" id="eventLocation" name="location" value="Test Venue, Brisbane QLD" required>
                </div>

                <div class="form-group">
                    <label for="eventDescription">Description</label>
                    <textarea id="eventDescription" name="description">Test event created to validate the architecture relationship between formal events and POS Terminal Quick Event Mode. This event should not affect Quick Event Mode service availability.</textarea>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="startDate">Start Date *</label>
                        <input type="datetime-local" id="startDate" name="start_date" required>
                    </div>
                    <div class="form-group">
                        <label for="endDate">End Date *</label>
                        <input type="datetime-local" id="endDate" name="end_date" required>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="maxCapacity">Max Capacity</label>
                        <input type="number" id="maxCapacity" name="max_capacity" value="100" min="1">
                    </div>
                    <div class="form-group">
                        <label for="eventStatus">Status</label>
                        <select id="eventStatus" name="status">
                            <option value="active">Active</option>
                            <option value="completed">Completed</option>
                            <option value="cancelled">Cancelled</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="expenseBudget">Expense Budget (AUD)</label>
                        <input type="number" id="expenseBudget" name="expense_budget" value="1000" min="0" step="0.01">
                    </div>
                    <div class="form-group">
                        <label for="revenueTarget">Revenue Target (AUD)</label>
                        <input type="number" id="revenueTarget" name="revenue_target" value="2000" min="0" step="0.01">
                    </div>
                </div>

                <button type="submit" id="createBtn">Create Test Event</button>
                <button type="button" onclick="clearForm()">Clear Form</button>
            </form>
        </div>

        <div id="results"></div>
    </div>

    <script>
        // Set default dates (tomorrow to day after tomorrow)
        window.addEventListener('load', () => {
            const now = new Date();
            const tomorrow = new Date(now);
            tomorrow.setDate(tomorrow.getDate() + 1);
            tomorrow.setHours(10, 0, 0, 0);
            
            const dayAfter = new Date(tomorrow);
            dayAfter.setHours(18, 0, 0, 0);
            
            document.getElementById('startDate').value = tomorrow.toISOString().slice(0, 16);
            document.getElementById('endDate').value = dayAfter.toISOString().slice(0, 16);
            
            addResult('🌊 Test Event Creation Ready', 'info');
            addResult('This will create a formal event to test the architecture hypothesis', 'info');
        });

        document.getElementById('eventForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            await createTestEvent();
        });

        async function createTestEvent() {
            const createBtn = document.getElementById('createBtn');
            createBtn.disabled = true;
            createBtn.textContent = 'Creating...';
            
            showLoading();

            try {
                // Get form data
                const formData = new FormData(document.getElementById('eventForm'));
                const eventData = Object.fromEntries(formData.entries());
                
                addResult('🔍 Step 1: Recording POS services BEFORE event creation...', 'info');
                const servicesBefore = await getPOSServices();
                
                addResult('🔍 Step 2: Creating formal event...', 'info');
                const response = await fetch('/api/admin/events', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        // Note: This will likely fail without authentication
                        // But we can still test the concept
                    },
                    body: JSON.stringify(eventData)
                });

                if (response.ok) {
                    const result = await response.json();
                    addResult(`✅ Event created successfully: ${result.event.name}`, 'success');
                    addResult(`📊 Event ID: ${result.event.id}`, 'info');
                    
                    addResult('🔍 Step 3: Recording POS services AFTER event creation...', 'info');
                    const servicesAfter = await getPOSServices();
                    
                    addResult('🔍 Step 4: Comparing service availability...', 'info');
                    compareServices(servicesBefore, servicesAfter);
                    
                } else if (response.status === 401 || response.status === 403) {
                    addResult('⚠️ Authentication required to create events', 'warning');
                    addResult('💡 This confirms events are admin-managed and separate from POS service filtering', 'info');
                    
                    // Still test POS services to show they work independently
                    addResult('🔍 Testing POS services independently...', 'info');
                    const services = await getPOSServices();
                    addResult(`📊 POS services available: ${services.length} (independent of formal events)`, 'success');
                    
                } else {
                    const errorData = await response.json();
                    throw new Error(errorData.error || `HTTP ${response.status}`);
                }

            } catch (error) {
                addResult(`❌ Error: ${error.message}`, 'error');
                
                // Even if event creation fails, test POS independence
                addResult('🔍 Testing POS service independence despite error...', 'info');
                try {
                    const services = await getPOSServices();
                    addResult(`📊 POS services still available: ${services.length}`, 'success');
                    addResult('✅ This confirms POS Quick Event Mode is independent of formal events', 'success');
                } catch (posError) {
                    addResult(`❌ POS services error: ${posError.message}`, 'error');
                }
                
            } finally {
                createBtn.disabled = false;
                createBtn.textContent = 'Create Test Event';
                hideLoading();
            }
        }

        async function getPOSServices() {
            try {
                const response = await fetch('/api/admin/pos/services-with-artists');
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const data = await response.json();
                return data.services || [];
            } catch (error) {
                addResult(`⚠️ Could not fetch POS services: ${error.message}`, 'warning');
                return [];
            }
        }

        function compareServices(before, after) {
            if (before.length === 0 || after.length === 0) {
                addResult('⚠️ Cannot compare services - insufficient data', 'warning');
                return;
            }
            
            if (before.length === after.length) {
                addResult('✅ HYPOTHESIS CONFIRMED: Service count unchanged after event creation', 'success');
                addResult(`📊 Services before: ${before.length}, after: ${after.length}`, 'info');
                addResult('🎯 This proves Quick Event Mode is independent of formal events', 'success');
            } else {
                addResult('❌ UNEXPECTED: Service count changed after event creation', 'error');
                addResult(`📊 Services before: ${before.length}, after: ${after.length}`, 'info');
                addResult('🤔 This suggests there may be a relationship we missed', 'warning');
            }
            
            // Check specific services
            const beforeNames = new Set(before.map(s => s.name));
            const afterNames = new Set(after.map(s => s.name));
            
            const added = after.filter(s => !beforeNames.has(s.name));
            const removed = before.filter(s => !afterNames.has(s.name));
            
            if (added.length > 0) {
                addResult(`➕ Services added: ${added.map(s => s.name).join(', ')}`, 'info');
            }
            if (removed.length > 0) {
                addResult(`➖ Services removed: ${removed.map(s => s.name).join(', ')}`, 'info');
            }
            if (added.length === 0 && removed.length === 0) {
                addResult('✅ Exact same services available - perfect independence confirmed', 'success');
            }
        }

        function clearForm() {
            document.getElementById('eventForm').reset();
            // Reset default dates
            const now = new Date();
            const tomorrow = new Date(now);
            tomorrow.setDate(tomorrow.getDate() + 1);
            tomorrow.setHours(10, 0, 0, 0);
            
            const dayAfter = new Date(tomorrow);
            dayAfter.setHours(18, 0, 0, 0);
            
            document.getElementById('startDate').value = tomorrow.toISOString().slice(0, 16);
            document.getElementById('endDate').value = dayAfter.toISOString().slice(0, 16);
        }

        function addResult(message, type = 'info') {
            const resultHTML = `<div class="${type}">${message}</div>`;
            document.getElementById('results').insertAdjacentHTML('beforeend', resultHTML);
        }

        function showLoading() {
            const loadingHTML = `
                <div class="form-section loading" id="loading">
                    <div class="spinner"></div>
                    <p>Processing test...</p>
                </div>
            `;
            document.getElementById('results').insertAdjacentHTML('beforeend', loadingHTML);
        }

        function hideLoading() {
            const loading = document.getElementById('loading');
            if (loading) loading.remove();
        }
    </script>
</body>
</html>
