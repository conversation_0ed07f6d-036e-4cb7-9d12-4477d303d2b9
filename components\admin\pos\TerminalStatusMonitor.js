/**
 * TerminalStatusMonitor - Real-time monitoring component for Square Terminal devices
 * Provides live status updates, connection monitoring, and health checks
 */

import { useState, useEffect, useCallback } from 'react'
import { supabaseAdmin } from '@/lib/supabase-admin'
import styles from '@/styles/admin/POS.module.css'

export default function TerminalStatusMonitor({
  onDeviceStatusChange,
  showCompactView = false,
  autoRefresh = true,
  refreshInterval = 30000, // 30 seconds
  includeReaderDevices = true
}) {
  const [terminalDevices, setTerminalDevices] = useState([])
  const [readerDevices, setReaderDevices] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [lastUpdate, setLastUpdate] = useState(null)
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Load devices and set up real-time monitoring
  useEffect(() => {
    loadDevices()
    
    if (autoRefresh) {
      const interval = setInterval(loadDevices, refreshInterval)
      return () => clearInterval(interval)
    }
  }, [autoRefresh, refreshInterval])

  // Set up real-time subscriptions for checkout updates
  useEffect(() => {
    const subscription = supabaseAdmin
      .from('terminal_checkout_updates')
      .on('INSERT', (payload) => {
        handleCheckoutUpdate(payload.new)
      })
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  const loadDevices = useCallback(async () => {
    if (!loading) setIsRefreshing(true)

    try {
      // Load Terminal devices
      const terminalResponse = await fetch('/api/admin/pos/terminal-devices', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      })

      let newTerminalDevices = []
      if (terminalResponse.ok) {
        const terminalData = await terminalResponse.json()
        newTerminalDevices = terminalData.devices || []

        // Check for Terminal device status changes
        if (onDeviceStatusChange && terminalDevices.length > 0) {
          newTerminalDevices.forEach(newDevice => {
            const oldDevice = terminalDevices.find(d => d.id === newDevice.id)
            if (oldDevice && oldDevice.status !== newDevice.status) {
              onDeviceStatusChange(newDevice, oldDevice.status)
            }
          })
        }
      }

      // Load Reader devices if enabled
      let newReaderDevices = []
      if (includeReaderDevices) {
        const readerResponse = await fetch('/api/admin/pos/reader-devices', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
          }
        })

        if (readerResponse.ok) {
          const readerData = await readerResponse.json()
          newReaderDevices = readerData.devices || []

          // Check for Reader device status changes
          if (onDeviceStatusChange && readerDevices.length > 0) {
            newReaderDevices.forEach(newDevice => {
              const oldDevice = readerDevices.find(d => d.id === newDevice.id)
              if (oldDevice && oldDevice.status !== newDevice.status) {
                onDeviceStatusChange(newDevice, oldDevice.status)
              }
            })
          }
        }
      }

      setTerminalDevices(newTerminalDevices)
      setReaderDevices(newReaderDevices)
      setLastUpdate(new Date())
      setError('')

    } catch (err) {
      console.error('Error loading device status:', err)
      setError(err.message)
    } finally {
      setLoading(false)
      setIsRefreshing(false)
    }
  }, [terminalDevices, readerDevices, loading, onDeviceStatusChange, includeReaderDevices])

  const handleCheckoutUpdate = (update) => {
    console.log('Terminal checkout update received:', update)
    // Trigger device refresh to get latest status
    loadDevices()
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'PAIRED': return '🟢'
      case 'UNPAIRED': return '🔴'
      case 'UNKNOWN': return '🟡'
      default: return '⚪'
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'PAIRED': return '#28a745'
      case 'UNPAIRED': return '#dc3545'
      case 'UNKNOWN': return '#ffc107'
      case 'Available': return '#28a745'
      case 'Connected': return '#28a745'
      default: return '#6c757d'
    }
  }

  const getDeviceShortName = (device) => {
    if (device.type === 'tap_to_pay') return 'TTP'
    if (device.platform === 'terminal') return `T${device.id.slice(-3)}`
    if (device.platform === 'reader' || device.deviceType === 'reader') return `R${device.id.slice(-3)}`
    return `D${device.id.slice(-3)}`
  }

  const getDeviceTypeLabel = (device) => {
    if (device.platform === 'terminal' || device.deviceType === 'terminal') return 'Terminal'
    if (device.platform === 'reader' || device.deviceType === 'reader') return 'Reader'
    if (device.type === 'tap_to_pay') return 'Tap to Pay'
    return 'Device'
  }

  const formatLastUpdate = () => {
    if (!lastUpdate) return 'Never'
    
    const now = new Date()
    const diff = now - lastUpdate
    const seconds = Math.floor(diff / 1000)
    const minutes = Math.floor(seconds / 60)
    
    if (seconds < 60) return `${seconds}s ago`
    if (minutes < 60) return `${minutes}m ago`
    return lastUpdate.toLocaleTimeString()
  }

  const getAllDevices = () => {
    return [...terminalDevices, ...readerDevices]
  }

  const getOverallStatus = () => {
    const allDevices = getAllDevices()
    if (allDevices.length === 0) return { status: 'none', message: 'No devices configured' }

    const availableCount = allDevices.filter(d =>
      d.status === 'PAIRED' || d.status === 'Available' || d.status === 'Connected'
    ).length
    const totalCount = allDevices.length

    if (availableCount === 0) return { status: 'error', message: 'No devices available' }
    if (availableCount === totalCount) return { status: 'success', message: 'All devices available' }
    return { status: 'warning', message: `${availableCount}/${totalCount} devices available` }
  }

  if (showCompactView) {
    const overall = getOverallStatus()
    
    return (
      <div className={styles.terminalStatusCompact}>
        <div className={styles.compactHeader}>
          <span className={styles.statusIndicator} style={{ color: getStatusColor(overall.status) }}>
            {overall.status === 'success' ? '🟢' : overall.status === 'warning' ? '🟡' : '🔴'}
          </span>
          <span className={styles.statusText}>{overall.message}</span>
          <button 
            className={styles.compactRefreshButton}
            onClick={loadDevices}
            disabled={isRefreshing}
            title="Refresh device status"
          >
            {isRefreshing ? '⟳' : '🔄'}
          </button>
        </div>
        
        {getAllDevices().length > 0 && (
          <div className={styles.compactDeviceList}>
            {getAllDevices().map((device) => (
              <div key={device.id} className={styles.compactDeviceItem}>
                <span className={styles.deviceStatusIcon}>
                  {getStatusIcon(device.status)}
                </span>
                <span className={styles.deviceNameShort}>
                  {device.name?.split(' ')[0] || getDeviceShortName(device)}
                </span>
              </div>
            ))}
          </div>
        )}
        
        <div className={styles.lastUpdateText}>
          Updated: {formatLastUpdate()}
        </div>
      </div>
    )
  }

  return (
    <div className={styles.terminalStatusMonitor}>
      <div className={styles.monitorHeader}>
        <h4>Terminal Device Status</h4>
        <div className={styles.monitorControls}>
          <span className={styles.lastUpdateInfo}>
            Last updated: {formatLastUpdate()}
          </span>
          <button
            className={styles.refreshButton}
            onClick={loadDevices}
            disabled={isRefreshing}
          >
            {isRefreshing ? '⟳ Refreshing...' : '🔄 Refresh'}
          </button>
        </div>
      </div>

      {error && (
        <div className={styles.errorMessage}>
          <span className={styles.errorIcon}>⚠️</span>
          <span className={styles.errorText}>{error}</span>
          <button onClick={() => setError('')} className={styles.dismissError}>×</button>
        </div>
      )}

      {loading ? (
        <div className={styles.loadingState}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading device status...</p>
        </div>
      ) : getAllDevices().length === 0 ? (
        <div className={styles.noDevicesState}>
          <div className={styles.noDevicesIcon}>📱</div>
          <h5>No Payment Devices</h5>
          <p>No Square Terminal or Reader devices have been configured yet.</p>
        </div>
      ) : (
        <div className={styles.deviceStatusList}>
          {terminalDevices.length > 0 && (
            <div className={styles.deviceSection}>
              <h4 className={styles.deviceSectionTitle}>Square Terminal Devices</h4>
              {terminalDevices.map((device) => (
                <div key={device.id} className={styles.deviceStatusCard}>
                  <div className={styles.deviceStatusHeader}>
                    <div className={styles.deviceInfo}>
                      <span className={styles.deviceIcon}>📱</span>
                      <span className={styles.deviceName}>
                        {device.name || `Terminal ${device.id.slice(-4)}`}
                      </span>
                      <span className={styles.deviceTypeLabel}>Terminal</span>
                    </div>
                    <div
                      className={styles.deviceStatus}
                      style={{ color: getStatusColor(device.status) }}
                    >
                      {getStatusIcon(device.status)} {device.status}
                    </div>
                  </div>

                  <div className={styles.deviceStatusDetails}>
                    <div className={styles.statusDetail}>
                      <span className={styles.detailLabel}>Device ID:</span>
                      <span className={styles.detailValue}>
                        {device.deviceId || 'Not paired'}
                      </span>
                    </div>

                    <div className={styles.statusDetail}>
                      <span className={styles.detailLabel}>Code:</span>
                      <span className={styles.detailValue}>{device.code}</span>
                    </div>

                    {device.statusChangedAt && (
                      <div className={styles.statusDetail}>
                        <span className={styles.detailLabel}>Last Change:</span>
                        <span className={styles.detailValue}>
                          {new Date(device.statusChangedAt).toLocaleString()}
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}

          {readerDevices.length > 0 && (
            <div className={styles.deviceSection}>
              <h4 className={styles.deviceSectionTitle}>Square Reader Devices</h4>
              {readerDevices.map((device) => (
                <div key={device.id} className={styles.deviceStatusCard}>
                  <div className={styles.deviceStatusHeader}>
                    <div className={styles.deviceInfo}>
                      <span className={styles.deviceIcon}>{device.icon || '📲'}</span>
                      <span className={styles.deviceName}>{device.name}</span>
                      <span className={styles.deviceTypeLabel}>{getDeviceTypeLabel(device)}</span>
                    </div>
                    <div
                      className={styles.deviceStatus}
                      style={{ color: getStatusColor(device.status) }}
                    >
                      {getStatusIcon(device.status)} {device.status}
                    </div>
                  </div>
              
                  <div className={styles.deviceStatusDetails}>
                    <div className={styles.statusDetail}>
                      <span className={styles.detailLabel}>Platform:</span>
                      <span className={styles.detailValue}>{device.platform}</span>
                    </div>

                    <div className={styles.statusDetail}>
                      <span className={styles.detailLabel}>Type:</span>
                      <span className={styles.detailValue}>{device.type}</span>
                    </div>

                    <div className={styles.statusDetail}>
                      <span className={styles.detailLabel}>Capabilities:</span>
                      <span className={styles.detailValue}>
                        {device.capabilities?.join(', ') || 'Standard'}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      <div className={styles.monitorFooter}>
        <div className={styles.statusSummary}>
          <span>Total: {getAllDevices().length}</span>
          <span>Terminal: {terminalDevices.length}</span>
          <span>Reader: {readerDevices.length}</span>
          <span>Available: {getAllDevices().filter(d =>
            d.status === 'PAIRED' || d.status === 'Available' || d.status === 'Connected'
          ).length}</span>
        </div>
        
        <div className={styles.autoRefreshInfo}>
          {autoRefresh ? (
            <span>Auto-refresh: {refreshInterval / 1000}s</span>
          ) : (
            <span>Auto-refresh: Off</span>
          )}
        </div>
      </div>
    </div>
  )
}
