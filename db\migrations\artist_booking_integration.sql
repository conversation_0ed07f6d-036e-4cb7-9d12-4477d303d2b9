-- Artist/Braider Booking System Integration Migration
-- This migration enhances the existing booking system to support artist assignments

-- =============================================
-- ENHANCE BOOKINGS TABLE FOR ARTIST ASSIGNMENT
-- =============================================

-- Add artist assignment fields to bookings table
ALTER TABLE public.bookings
ADD COLUMN IF NOT EXISTS assigned_artist_id UUID REFERENCES public.artist_profiles(id),
ADD COLUMN IF NOT EXISTS artist_assignment_type TEXT DEFAULT 'auto' CHECK (artist_assignment_type IN ('auto', 'manual', 'customer_preference')),
ADD COLUMN IF NOT EXISTS artist_assignment_notes TEXT,
ADD COLUMN IF NOT EXISTS requires_specific_artist BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS preferred_artist_id UUID REFERENCES public.artist_profiles(id);

-- =============================================
-- ENHANCE ARTIST PROFILES FOR BOOKING INTEGRATION
-- =============================================

-- Add booking-related fields to artist profiles
ALTER TABLE public.artist_profiles
ADD COLUMN IF NOT EXISTS booking_buffer_time INTEGER DEFAULT 15, -- Minutes between bookings
ADD COLUMN IF NOT EXISTS max_daily_bookings INTEGER DEFAULT 8,
ADD COLUMN IF NOT EXISTS accepts_walk_ins BOOLEAN DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS accepts_online_bookings BOOLEAN DEFAULT TRUE,
ADD COLUMN IF NOT EXISTS minimum_advance_booking INTEGER DEFAULT 60, -- Minutes
ADD COLUMN IF NOT EXISTS maximum_advance_booking INTEGER DEFAULT 10080, -- 1 week in minutes
ADD COLUMN IF NOT EXISTS auto_confirm_bookings BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS notification_preferences JSONB DEFAULT '{"email": true, "sms": false, "push": true}';

-- =============================================
-- CREATE ARTIST AVAILABILITY EXCEPTIONS TABLE
-- =============================================

-- Track specific date/time exceptions to regular schedule
CREATE TABLE IF NOT EXISTS public.artist_availability_exceptions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  artist_id UUID REFERENCES public.artist_profiles(id) ON DELETE CASCADE,
  exception_date DATE NOT NULL,
  exception_type TEXT CHECK (exception_type IN ('unavailable', 'custom_hours', 'break')) NOT NULL,
  start_time TIME,
  end_time TIME,
  reason TEXT,
  is_recurring BOOLEAN DEFAULT FALSE,
  recurring_pattern TEXT, -- 'weekly', 'monthly', etc.
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(artist_id, exception_date, exception_type, start_time)
);

-- =============================================
-- CREATE ARTIST BOOKING PREFERENCES TABLE
-- =============================================

-- Store artist preferences for booking management
CREATE TABLE IF NOT EXISTS public.artist_booking_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  artist_id UUID REFERENCES public.artist_profiles(id) ON DELETE CASCADE,
  service_id UUID REFERENCES public.services(id) ON DELETE CASCADE,
  preferred_duration INTEGER, -- Override service default duration
  custom_pricing DECIMAL(10,2), -- Override service default pricing
  requires_consultation BOOLEAN DEFAULT FALSE,
  preparation_time INTEGER DEFAULT 0, -- Minutes needed before service
  cleanup_time INTEGER DEFAULT 0, -- Minutes needed after service
  is_available_for_service BOOLEAN DEFAULT TRUE,
  skill_confidence_level INTEGER CHECK (skill_confidence_level BETWEEN 1 AND 5) DEFAULT 3,
  notes TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(artist_id, service_id)
);

-- =============================================
-- CREATE BOOKING ASSIGNMENT HISTORY TABLE
-- =============================================

-- Track changes to booking assignments for audit purposes
CREATE TABLE IF NOT EXISTS public.booking_assignment_history (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  booking_id UUID REFERENCES public.bookings(id) ON DELETE CASCADE,
  previous_artist_id UUID REFERENCES public.artist_profiles(id),
  new_artist_id UUID REFERENCES public.artist_profiles(id),
  assignment_reason TEXT,
  changed_by UUID REFERENCES auth.users(id),
  changed_at TIMESTAMPTZ DEFAULT NOW(),
  notes TEXT
);

-- =============================================
-- CREATE INDEXES FOR PERFORMANCE
-- =============================================

-- Indexes for booking queries
CREATE INDEX IF NOT EXISTS idx_bookings_assigned_artist ON public.bookings(assigned_artist_id);
CREATE INDEX IF NOT EXISTS idx_bookings_preferred_artist ON public.bookings(preferred_artist_id);
CREATE INDEX IF NOT EXISTS idx_bookings_start_time_artist ON public.bookings(start_time, assigned_artist_id);

-- Indexes for availability queries
CREATE INDEX IF NOT EXISTS idx_artist_availability_schedule_artist_day ON public.artist_availability_schedule(artist_id, day_of_week);
CREATE INDEX IF NOT EXISTS idx_artist_availability_exceptions_artist_date ON public.artist_availability_exceptions(artist_id, exception_date);

-- Indexes for booking preferences
CREATE INDEX IF NOT EXISTS idx_artist_booking_preferences_artist_service ON public.artist_booking_preferences(artist_id, service_id);

-- =============================================
-- CREATE VIEWS FOR EASY QUERYING
-- =============================================

-- View for artist availability with current bookings
CREATE OR REPLACE VIEW public.artist_current_availability AS
SELECT 
  ap.id as artist_id,
  ap.artist_name,
  ap.display_name,
  ap.is_active,
  ap.is_available_today,
  ap.max_daily_bookings,
  -- Count today's bookings
  COALESCE(today_bookings.booking_count, 0) as todays_booking_count,
  -- Calculate availability status
  CASE 
    WHEN NOT ap.is_active THEN 'inactive'
    WHEN NOT ap.is_available_today THEN 'unavailable'
    WHEN COALESCE(today_bookings.booking_count, 0) >= ap.max_daily_bookings THEN 'fully_booked'
    ELSE 'available'
  END as availability_status
FROM public.artist_profiles ap
LEFT JOIN (
  SELECT 
    assigned_artist_id,
    COUNT(*) as booking_count
  FROM public.bookings 
  WHERE DATE(start_time) = CURRENT_DATE 
    AND status NOT IN ('canceled', 'no_show')
    AND assigned_artist_id IS NOT NULL
  GROUP BY assigned_artist_id
) today_bookings ON ap.id = today_bookings.assigned_artist_id;

-- =============================================
-- SAMPLE DATA FOR TESTING
-- =============================================

-- Update existing artist profiles with booking preferences
UPDATE public.artist_profiles 
SET 
  booking_buffer_time = 15,
  max_daily_bookings = 6,
  accepts_walk_ins = true,
  accepts_online_bookings = true,
  minimum_advance_booking = 120, -- 2 hours
  maximum_advance_booking = 20160, -- 2 weeks
  auto_confirm_bookings = false
WHERE id IN (
  '11111111-1111-1111-1111-111111111111',
  '22222222-2222-2222-2222-222222222222',
  '33333333-3333-3333-3333-333333333333'
);

-- =============================================
-- COMMENTS AND DOCUMENTATION
-- =============================================

COMMENT ON TABLE public.artist_availability_exceptions IS 'Tracks specific date/time exceptions to regular artist schedules';
COMMENT ON TABLE public.artist_booking_preferences IS 'Stores artist preferences for specific services including custom pricing and duration';
COMMENT ON TABLE public.booking_assignment_history IS 'Audit trail for booking assignment changes';
COMMENT ON VIEW public.artist_current_availability IS 'Real-time view of artist availability status including current booking load';

-- Success message
DO $$
BEGIN
  RAISE NOTICE '✅ Artist booking integration migration completed successfully';
  RAISE NOTICE '📊 Enhanced bookings table with artist assignment fields';
  RAISE NOTICE '👥 Added artist booking preferences and availability exceptions';
  RAISE NOTICE '📈 Created performance indexes and availability views';
END $$;
