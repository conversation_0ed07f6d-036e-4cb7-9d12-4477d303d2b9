import { useState, useEffect, useCallback } from 'react'
import AdminLayout from '@/components/admin/AdminLayout'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import BookingReminders from '@/components/admin/BookingReminders'
import DevAuthToggle from '@/components/admin/DevAuthToggle'
import { safeRender } from '@/lib/safe-render-utils'
import { authenticatedGet } from '@/lib/unified-auth-fetch'
import styles from '@/styles/admin/Dashboard.module.css'

export default function AdminDashboard() {
  const [summary, setSummary] = useState({
    totalBookings: 0,
    pendingBookings: 0,
    totalCustomers: 0,
    totalRevenue: 0,
    lowStockItems: 0
  })
  const [recentBookings, setRecentBookings] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [refreshKey, setRefreshKey] = useState(0)
  const [lastRefreshed, setLastRefreshed] = useState(new Date())

  // Function to manually refresh dashboard data
  const refreshDashboard = useCallback(() => {
    setRefreshKey(prevKey => prevKey + 1)
    setLastRefreshed(new Date())
  }, [])

  // Fetch dashboard summary data using API routes with cache headers
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('[AdminDashboard] Fetching dashboard data via API routes...');

        // Get total bookings via API route
        let totalBookings = 0;
        try {
          const response = await authenticatedGet('/api/admin/bookings');
          if (response.ok) {
            const data = await response.json();
            totalBookings = data.bookings?.length || 0;
            console.log('[AdminDashboard] Total bookings:', totalBookings);
          } else {
            console.warn('Error fetching total bookings via API:', response.status);
          }
        } catch (err) {
          console.warn('Exception fetching total bookings via API:', err);
        }

        // Get pending bookings via API route
        let pendingBookings = 0;
        try {
          const response = await authenticatedGet('/api/admin/bookings?status=pending');
          if (response.ok) {
            const data = await response.json();
            pendingBookings = data.bookings?.length || 0;
            console.log('[AdminDashboard] Pending bookings:', pendingBookings);
          } else {
            console.warn('Error fetching pending bookings via API:', response.status);
          }
        } catch (err) {
          console.warn('Exception fetching pending bookings via API:', err);
        }

        // Get total customers via API route
        let totalCustomers = 0;
        try {
          const response = await authenticatedGet('/api/admin/customers');
          if (response.ok) {
            const data = await response.json();
            totalCustomers = data.customers?.length || 0;
            console.log('[AdminDashboard] Total customers:', totalCustomers);
          } else {
            console.warn('Error fetching total customers via API:', response.status);
          }
        } catch (err) {
          console.warn('Exception fetching total customers via API:', err);
        }

        // Get total revenue via API route
        let totalRevenue = 0;
        try {
          const response = await authenticatedGet('/api/admin/payments?payment_status=completed');
          if (response.ok) {
            const data = await response.json();
            if (data.payments) {
              totalRevenue = data.payments.reduce((sum, payment) => sum + (parseFloat(payment.amount) || 0), 0);
            }
            console.log('[AdminDashboard] Total revenue:', totalRevenue);
          } else {
            console.warn('Error fetching payments via API:', response.status);
          }
        } catch (err) {
          console.warn('Exception fetching payments via API:', err);
        }

        // Get low stock items via API route
        let lowStockItems = 0;
        try {
          const response = await authenticatedGet('/api/public/products');
          if (response.ok) {
            const data = await response.json();
            if (data.products) {
              lowStockItems = data.products.filter(product =>
                product.stock > 0 && product.stock <= 10
              ).length;
            }
            console.log('[AdminDashboard] Low stock items:', lowStockItems);
          } else {
            console.warn('Error fetching products via API:', response.status);
          }
        } catch (err) {
          console.warn('Exception fetching products via API:', err);
        }

        // Get recent bookings via API route
        let recentBookingsData = [];
        try {
          const response = await authenticatedGet('/api/admin/bookings');
          if (response.ok) {
            const data = await response.json();
            if (data.bookings) {
              // Sort by created_at and take the first 5
              recentBookingsData = data.bookings
                .sort((a, b) => new Date(b.created_at || 0) - new Date(a.created_at || 0))
                .slice(0, 5);
            }
            console.log('[AdminDashboard] Recent bookings:', recentBookingsData.length);
          } else {
            console.warn('Error fetching recent bookings via API:', response.status);
          }
        } catch (err) {
          console.warn('Exception fetching recent bookings via API:', err);
        }

        setSummary({
          totalBookings,
          pendingBookings,
          totalCustomers,
          totalRevenue,
          lowStockItems
        });

        setRecentBookings(recentBookingsData);
        setLoading(false);
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        setError(error.message || 'Failed to load dashboard data');

        // Set default values to prevent UI issues
        setSummary({
          totalBookings: 0,
          pendingBookings: 0,
          totalCustomers: 0,
          totalRevenue: 0,
          lowStockItems: 0
        });

        setRecentBookings([]);
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [refreshKey])

  return (
    <ProtectedRoute>
      <AdminLayout title="Dashboard">
        <div className={styles.dashboard}>
          <div className={styles.dashboardHeader}>
            <div className={styles.dashboardTitle}>
              <h1>Dashboard</h1>
              <div className={styles.lastRefreshed} suppressHydrationWarning={true}>
                Last updated: {lastRefreshed.toLocaleTimeString()}
              </div>
            </div>
            <div className={styles.dashboardActions}>
              <button
                className={styles.refreshButton}
                onClick={refreshDashboard}
                disabled={loading}
              >
                {loading ? 'Refreshing...' : 'Refresh Data'}
              </button>
            </div>
          </div>
          {loading ? (
            <div className={styles.loading}>
              <div className={styles.loadingSpinner}></div>
              <p>Loading dashboard data...</p>
            </div>
          ) : error ? (
            <div className={styles.error}>
              <div className={styles.errorIcon}>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="12" cy="12" r="10"></circle>
                  <line x1="12" y1="8" x2="12" y2="12"></line>
                  <line x1="12" y1="16" x2="12.01" y2="16"></line>
                </svg>
              </div>
              <h3>Error Loading Dashboard</h3>
              <p>{error}</p>
              <button
                className={styles.retryButton}
                onClick={() => window.location.reload()}
              >
                Retry
              </button>
            </div>
          ) : (
            <>
              <div className={styles.summaryCards}>
                <div className={styles.summaryCard}>
                  <div className={styles.summaryIcon}>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                      <line x1="16" y1="2" x2="16" y2="6"></line>
                      <line x1="8" y1="2" x2="8" y2="6"></line>
                      <line x1="3" y1="10" x2="21" y2="10"></line>
                    </svg>
                  </div>
                  <div className={styles.summaryContent}>
                    <h3>Total Bookings</h3>
                    <p className={styles.summaryValue}>{summary.totalBookings}</p>
                  </div>
                </div>

                <div className={styles.summaryCard}>
                  <div className={styles.summaryIcon}>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <circle cx="12" cy="12" r="10"></circle>
                      <polyline points="12 6 12 12 16 14"></polyline>
                    </svg>
                  </div>
                  <div className={styles.summaryContent}>
                    <h3>Pending Bookings</h3>
                    <p className={styles.summaryValue}>{summary.pendingBookings}</p>
                  </div>
                </div>

                <div className={styles.summaryCard}>
                  <div className={styles.summaryIcon}>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                      <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                  </div>
                  <div className={styles.summaryContent}>
                    <h3>Total Customers</h3>
                    <p className={styles.summaryValue}>{summary.totalCustomers}</p>
                  </div>
                </div>

                <div className={styles.summaryCard}>
                  <div className={styles.summaryIcon}>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="12" y1="1" x2="12" y2="23"></line>
                      <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                    </svg>
                  </div>
                  <div className={styles.summaryContent}>
                    <h3>Total Revenue</h3>
                    <p className={styles.summaryValue}>${summary.totalRevenue.toFixed(2)}</p>
                  </div>
                </div>

                <div className={styles.summaryCard}>
                  <div className={styles.summaryIcon}>
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                      <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                      <line x1="12" y1="22.08" x2="12" y2="12"></line>
                    </svg>
                  </div>
                  <div className={styles.summaryContent}>
                    <h3>Low Stock Items</h3>
                    <p className={styles.summaryValue}>{summary.lowStockItems}</p>
                  </div>
                </div>
              </div>

              <div className={styles.dashboardGrid}>
                <div className={styles.dashboardSection}>
                  <h2 className={styles.sectionTitle}>Recent Bookings</h2>

                  {recentBookings.length > 0 ? (
                    <div className={styles.recentBookings}>
                      <table className={styles.bookingsTable}>
                        <thead>
                          <tr>
                            <th>Customer</th>
                            <th>Service</th>
                            <th>Date</th>
                            <th>Time</th>
                            <th>Status</th>
                          </tr>
                        </thead>
                        <tbody>
                          {recentBookings.map(booking => {
                            try {
                              return (
                                <tr key={booking.id}>
                                  <td>{safeRender(booking.customers?.name, 'Unknown')}</td>
                                  <td>{safeRender(booking.services?.name, 'Unknown')}</td>
                                  <td>{booking.start_time ? new Date(booking.start_time).toLocaleDateString() : 'N/A'}</td>
                                  <td>
                                    {booking.start_time && booking.end_time ? (
                                      <>
                                        {new Date(booking.start_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                        {' - '}
                                        {new Date(booking.end_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                                      </>
                                    ) : 'N/A'}
                                  </td>
                                  <td>
                                    <span className={`${styles.status} ${styles[safeRender(booking.status, 'unknown')]}`}>
                                      {safeRender(booking.status, 'Unknown')}
                                    </span>
                                  </td>
                                </tr>
                              );
                            } catch (error) {
                              console.error('Error rendering booking row:', error, 'Booking:', booking);
                              return (
                                <tr key={booking.id || Math.random()}>
                                  <td colSpan="5" style={{ color: 'red', padding: '10px' }}>
                                    Error displaying booking data. Please refresh the page.
                                  </td>
                                </tr>
                              );
                            }
                          })}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className={styles.noData}>No recent bookings found</div>
                  )}
                </div>

                <div className={styles.dashboardSection}>
                  <BookingReminders />
                </div>
              </div>

              {/* Development Authentication Toggle - only visible in development mode */}
              {process.env.NODE_ENV === 'development' && (
                <DevAuthToggle />
              )}
            </>
          )}
        </div>
      </AdminLayout>
    </ProtectedRoute>
  )
}
