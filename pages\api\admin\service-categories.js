import { supabaseAdmin } from '../../../lib/supabase';

// Helper function to serialize category data
const serializeCategory = (category) => {
  if (!category) return null;
  return {
    id: String(category.id || ''),
    name: String(category.name || ''),
    description: category.description === null ? null : String(category.description || ''), // Allow null description
    parent_id: category.parent_id === null ? null : String(category.parent_id || ''), // Allow null parent_id
  };
};

// GET handler
async function handleGet(req, res) {
  try {
    console.log('🔍 Service Categories API - GET: Fetching categories');
    const { data: categories, error } = await supabaseAdmin
      .from('service_categories')
      .select('id, name, description, parent_id')
      .order('name');

    if (error) {
      console.error('❌ Service Categories API - GET: Database error:', error);
      throw error;
    }

    const serializedCategories = categories?.map(serializeCategory) || [];
    console.log(`✅ Service Categories API - GET: Found and serialized ${serializedCategories.length} categories`);
    res.status(200).json({ success: true, categories: serializedCategories });

  } catch (error) {
    console.error('💥 Service Categories API - GET: Error:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch service categories', details: error.message });
  }
}

// POST handler
async function handlePost(req, res) {
  try {
    console.log('🔍 Service Categories API - POST: Creating category');
    const { name, description, parent_id } = req.body;

    // Validate input
    if (typeof name !== 'string' || name.trim() === '') {
      return res.status(400).json({ success: false, error: 'Name is required and must be a non-empty string.' });
    }
    if (description !== undefined && description !== null && typeof description !== 'string') {
      return res.status(400).json({ success: false, error: 'Description must be a string if provided.' });
    }
    let parsedParentId = null;
    if (parent_id !== undefined && parent_id !== null) {
      parsedParentId = parseInt(parent_id, 10);
      if (isNaN(parsedParentId)) {
        return res.status(400).json({ success: false, error: 'Parent ID must be an integer if provided.' });
      }
    }

    const newCategoryData = {
      name: name.trim(),
      description: description === undefined ? null : description, // Use null if not provided
      parent_id: parsedParentId,
    };

    const { data: category, error } = await supabaseAdmin
      .from('service_categories')
      .insert(newCategoryData)
      .select()
      .single();

    if (error) {
      console.error('❌ Service Categories API - POST: Database error:', error);
      // Could be a duplicate name or invalid parent_id foreign key
      if (error.code === '23505') { // Unique violation
        return res.status(409).json({ success: false, error: 'A category with this name already exists.', details: error.message });
      }
      if (error.code === '23503') { // Foreign key violation
        return res.status(400).json({ success: false, error: 'Invalid parent_id. The parent category does not exist.', details: error.message });
      }
      throw error;
    }

    const serializedCategory = serializeCategory(category);
    console.log('✅ Service Categories API - POST: Category created:', serializedCategory);
    res.status(201).json({ success: true, category: serializedCategory });

  } catch (error) {
    console.error('💥 Service Categories API - POST: Error:', error);
    res.status(500).json({ success: false, error: 'Failed to create service category', details: error.message });
  }
}

// DELETE handler
async function handleDelete(req, res) {
  try {
    const { id } = req.query; // Assuming ID is passed as a query parameter for DELETE
    console.log(`🔍 Service Categories API - DELETE: Attempting to delete category with ID: ${id}`);

    if (!id) {
      return res.status(400).json({ success: false, error: 'Category ID is required.' });
    }
    const parsedId = parseInt(id, 10);
    if (isNaN(parsedId)) {
      return res.status(400).json({ success: false, error: 'Category ID must be an integer.' });
    }

    // Check for child categories
    const { data: childCategories, error: childCheckError } = await supabaseAdmin
      .from('service_categories')
      .select('id', { count: 'exact' })
      .eq('parent_id', parsedId);

    if (childCheckError) {
      console.error('❌ Service Categories API - DELETE: Error checking for child categories:', childCheckError);
      throw childCheckError;
    }
    if (childCategories && childCategories.length > 0) {
      return res.status(409).json({ success: false, error: 'Category has sub-categories and cannot be deleted. Please delete or reassign sub-categories first.' });
    }

    // Check for services using this category
    // Assuming services table has a category_id foreign key
    const { data: services, error: serviceCheckError } = await supabaseAdmin
      .from('services') // Make sure 'services' is your actual services table name
      .select('id', { count: 'exact' })
      .eq('category_id', parsedId);

    if (serviceCheckError) {
      console.error('❌ Service Categories API - DELETE: Error checking for services using category:', serviceCheckError);
      throw serviceCheckError;
    }

    if (services && services.length > 0) {
      return res.status(409).json({ success: false, error: 'Category is in use by services and cannot be deleted. Please reassign services to another category first.' });
    }

    // Proceed with deletion
    const { data: deletedCategory, error: deleteError, count } = await supabaseAdmin
      .from('service_categories')
      .delete()
      .eq('id', parsedId)
      .select(); // Select to get data of deleted row for count, though Supabase delete might not return data directly, count is more reliable if supported this way

    if (deleteError) {
      console.error('❌ Service Categories API - DELETE: Database error during deletion:', deleteError);
      throw deleteError;
    }

    // Based on Supabase v2, .delete() without .select() returns an object with data (potentially null) and count.
    // If you add .select() it would return the deleted rows. For just checking if something was deleted,
    // checking if anything was returned by select (if data is not empty) or relying on count (if available from delete directly) is needed.
    // Let's assume the delete operation itself doesn't throw an error if the row doesn't exist, but returns count 0.
    // The API call for delete in supabase-js v2 returns { data, error, count }.
    // If a .select() is chained, data would be the selected items (i.e., the deleted ones).
    // If no .select() is chained, data is null, and count is the number of deleted rows.
    // The provided code for POST/PUT uses .select().single() which implies select is used.
    // For delete, if we use .select(), data would be an array of deleted items.

    // A more robust check for "not found" might be to check if count is 0.
    // If the .delete().eq().select() returns the deleted rows, then if `deletedCategory` is empty and no error, it means not found.
    // Let's adjust based on the common Supabase pattern: delete and check `count`.
    // The current mock structure for supabaseAdmin does not have a `count` field in the response.
    // For the purpose of this implementation, if `deletedCategory` (from .select()) is empty and no error, it means "not found".
    // However, the actual Supabase client might return `count` directly on the delete operation object if `.select()` is not used.
    // Let's stick to checking the returned data from `.select()` as per other handlers.

    // If we use .select() after .delete(), and the row doesn't exist, `deletedCategory` will be an empty array.
    // If the row exists and is deleted, `deletedCategory` will be an array with the deleted item(s).
    // Let's assume `delete().select()` returns the array of deleted items.
    const itemsDeleted = deletedCategory ? deletedCategory.length : 0;

    if (itemsDeleted === 0) {
        return res.status(404).json({ success: false, error: `Category with ID ${parsedId} not found or already deleted.` });
    }

    console.log(`✅ Service Categories API - DELETE: Category with ID ${parsedId} deleted successfully. Items deleted: ${itemsDeleted}`);
    res.status(200).json({ success: true, message: `Category with ID ${parsedId} deleted successfully.` });

  } catch (error) {
    console.error('💥 Service Categories API - DELETE: Error:', error);
    res.status(500).json({ success: false, error: 'Failed to delete service category', details: error.message });
  }
}


// PUT handler
async function handlePut(req, res) {
  try {
    console.log('🔍 Service Categories API - PUT: Updating category');
    const { id, name, description, parent_id } = req.body;

    // Validate ID
    if (id === undefined || id === null) {
      return res.status(400).json({ success: false, error: 'Category ID is required.' });
    }
    const parsedId = parseInt(id, 10);
    if (isNaN(parsedId)) {
      return res.status(400).json({ success: false, error: 'Category ID must be an integer.' });
    }

    const updateData = {};
    let hasUpdate = false;

    // Validate and add fields to updateData if provided
    if (name !== undefined) {
      if (typeof name !== 'string' || name.trim() === '') {
        return res.status(400).json({ success: false, error: 'Name must be a non-empty string if provided.' });
      }
      updateData.name = name.trim();
      hasUpdate = true;
    }

    if (description !== undefined) {
      if (description !== null && typeof description !== 'string') {
        return res.status(400).json({ success: false, error: 'Description must be a string or null if provided.' });
      }
      updateData.description = description;
      hasUpdate = true;
    }

    if (parent_id !== undefined) {
      if (parent_id === null) {
        updateData.parent_id = null;
      } else {
        const parsedParentId = parseInt(parent_id, 10);
        if (isNaN(parsedParentId)) {
          return res.status(400).json({ success: false, error: 'Parent ID must be an integer or null if provided.' });
        }
        // Prevent setting category's parent_id to its own id
        if (parsedParentId === parsedId) {
          return res.status(400).json({ success: false, error: 'A category cannot be its own parent.' });
        }
        updateData.parent_id = parsedParentId;
      }
      hasUpdate = true;
    }

    if (!hasUpdate) {
      return res.status(400).json({ success: false, error: 'No update data provided. Please provide name, description, or parent_id to update.' });
    }

    const { data: category, error } = await supabaseAdmin
      .from('service_categories')
      .update(updateData)
      .eq('id', parsedId)
      .select()
      .single();

    if (error) {
      console.error('❌ Service Categories API - PUT: Database error:', error);
       // Could be a duplicate name or invalid parent_id foreign key
      if (error.code === '23505') { // Unique violation
        return res.status(409).json({ success: false, error: 'A category with this name already exists.', details: error.message });
      }
      if (error.code === '23503') { // Foreign key violation
        return res.status(400).json({ success: false, error: 'Invalid parent_id. The parent category does not exist.', details: error.message });
      }
      throw error;
    }

    if (!category) {
      return res.status(404).json({ success: false, error: `Category with ID ${parsedId} not found.` });
    }

    const serializedCategory = serializeCategory(category);
    console.log('✅ Service Categories API - PUT: Category updated:', serializedCategory);
    res.status(200).json({ success: true, category: serializedCategory });

  } catch (error) {
    console.error('💥 Service Categories API - PUT: Error:', error);
    res.status(500).json({ success: false, error: 'Failed to update service category', details: error.message });
  }
}


export default async function handler(req, res) {
  if (req.method === 'GET') {
    return handleGet(req, res);
  } else if (req.method === 'POST') {
    return handlePost(req, res);
  } else if (req.method === 'PUT') {
    return handlePut(req, res);
  } else {
    res.setHeader('Allow', ['GET', 'POST', 'PUT', 'DELETE']);
    return res.status(405).json({ success: false, error: `Method ${req.method} not allowed.` });
  }
}
