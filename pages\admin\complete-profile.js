import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import Head from 'next/head';
import { useAuth } from '@/contexts/AuthContext';
import Layout from '@/components/Layout';
import ProfileManagementCard from '@/components/admin/ProfileManagementCard';
import { toast } from 'react-toastify';
import styles from '@/styles/CompleteProfile.module.css'; // We might need to create this CSS module

const CompleteProfilePage = () => {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false); // For profile submission loading state
  const [error, setError] = useState('');

  // Remove the premature profile completion check that was causing redirects
  // The dashboard will handle checking profile completion after the user explicitly saves their profile

  // Protect route: redirect if not logged in or not an artist/braider
  useEffect(() => {
    if (!authLoading && !user) {
      console.log('[CompleteProfilePage] No user found, redirecting to staff login');
      router.push('/admin/login?redirect=/admin/complete-profile');
    } else if (user && !['artist', 'braider'].includes(user.role)) {
      console.warn(`[CompleteProfilePage] User with role ${user.role} attempted to access complete-profile page. Redirecting.`);
      toast.error("You don't have permission to access this page.");
      router.push('/admin/login'); // Redirect to admin login instead
    }
  }, [user, authLoading, router]);

  const handleProfileUpdateSuccess = async (updatedProfile) => {
    setIsLoading(true);
    setError('');
    console.log('[CompleteProfilePage] Profile updated successfully:', updatedProfile);
    toast.success('Profile completed! Redirecting to your dashboard...');

    // Now, we need to ensure the `artist_profiles` table reflects completion.
    // The ProfileManagementCard's internal save should handle most of it.
    // We will add/ensure an `is_profile_complete: true` or similar is set by the API.
    // For now, we assume the API /api/artist/profile will handle setting the profile as complete.

    // A more robust way would be to update a specific flag like `is_profile_complete` to true
    // in the `artist_profiles` table via the `/api/artist/profile` endpoint.
    // This is noted as a DB schema consideration.

    // For now, we assume the profile is complete enough if `artist_name` is set,
    // which is handled by ProfileManagementCard.

    // After a short delay to allow toast to be seen.
    setTimeout(() => {
      router.push('/admin/artist-braider-dashboard');
    }, 2000);
  };

  const handleProfileUpdateError = (errorMessage) => {
    setIsLoading(false);
    setError(errorMessage || 'Failed to update profile. Please try again.');
    toast.error(errorMessage || 'Failed to update profile. Please try again.');
  };

  if (authLoading || (!user && !authLoading)) {
    return (
      <Layout>
        <div className={styles.loadingContainer}>
          <p>Loading user data...</p>
        </div>
      </Layout>
    );
  }

  if (!user || !['artist', 'braider'].includes(user.role)) {
     // This case should ideally be caught by the useEffect redirect,
     // but as a fallback, don't render the main content.
    return (
      <Layout>
        <div className={styles.loadingContainer}>
          <p>Redirecting...</p>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <Head>
        <title>Complete Your Profile | Staff Portal</title>
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      <div className={styles.container}>
        <h1 className={styles.title}>Welcome! Let's Complete Your Profile</h1>
        <p className={styles.instructions}>
          Please fill out the information below to complete your artist/braider profile.
          This will help us showcase your talents and manage your bookings effectively.
        </p>

        {error && <p className={styles.error}>{error}</p>}

        {user && user.id && (
          <ProfileManagementCard
            artistId={user.id} // Pass the user_id as artistId
            onProfileUpdate={handleProfileUpdateSuccess}
            onProfileError={handleProfileUpdateError}
            autoStartEditing={true} // Automatically start in editing mode for profile creation
          />
        )}

        {/* The submit button is part of ProfileManagementCard, so no separate button here unless ProfileManagementCard is refactored */}
      </div>
    </Layout>
  );
};

export default CompleteProfilePage;

// Basic CSS Module (styles/CompleteProfile.module.css) - Create this file
/*
.container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 2rem;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.title {
  color: #333;
  text-align: center;
  margin-bottom: 1rem;
}

.instructions {
  text-align: center;
  margin-bottom: 2rem;
  color: #555;
}

.error {
  color: red;
  background-color: #ffebee;
  border: 1px solid #ef9a9a;
  padding: 1rem;
  border-radius: 4px;
  margin-bottom: 1.5rem;
  text-align: center;
}

.loadingContainer {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  font-size: 1.2rem;
}
*/
