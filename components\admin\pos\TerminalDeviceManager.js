/**
 * TerminalDeviceManager - Manage Square Terminal device pairing and status
 * Provides interface for creating device codes, monitoring pairing status, and device management
 */

import { useState, useEffect } from 'react'
import styles from '@/styles/admin/POS.module.css'

export default function TerminalDeviceManager() {
  const [devices, setDevices] = useState([])
  const [loading, setLoading] = useState(true)
  const [creating, setCreating] = useState(false)
  const [error, setError] = useState('')
  const [newDeviceName, setNewDeviceName] = useState('')
  const [showCreateForm, setShowCreateForm] = useState(false)

  useEffect(() => {
    loadDevices()
    // Refresh devices every 30 seconds to check pairing status
    const interval = setInterval(loadDevices, 30000)
    return () => clearInterval(interval)
  }, [])

  const loadDevices = async () => {
    try {
      const response = await fetch('/api/admin/pos/terminal-devices', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setDevices(data.devices || [])
      } else {
        throw new Error('Failed to load devices')
      }
    } catch (err) {
      console.error('Error loading devices:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const createDeviceCode = async () => {
    if (!newDeviceName.trim()) {
      setError('Device name is required')
      return
    }

    setCreating(true)
    setError('')

    try {
      const response = await fetch('/api/admin/pos/terminal-devices', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        },
        body: JSON.stringify({
          deviceName: newDeviceName.trim()
        })
      })

      if (response.ok) {
        const data = await response.json()
        setDevices(prev => [data.deviceCode, ...prev])
        setNewDeviceName('')
        setShowCreateForm(false)
      } else {
        const errorData = await response.json()
        throw new Error(errorData.message || 'Failed to create device code')
      }
    } catch (err) {
      console.error('Error creating device code:', err)
      setError(err.message)
    } finally {
      setCreating(false)
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'PAIRED': return '🟢'
      case 'UNPAIRED': return '🔴'
      case 'UNKNOWN': return '🟡'
      default: return '⚪'
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'PAIRED': return '#28a745'
      case 'UNPAIRED': return '#dc3545'
      case 'UNKNOWN': return '#ffc107'
      default: return '#6c757d'
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleString('en-AU', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
      // Could add a toast notification here
      console.log('Copied to clipboard:', text)
    })
  }

  if (loading) {
    return (
      <div className={styles.deviceManager}>
        <div className={styles.loadingState}>
          <div className={styles.loadingSpinner}></div>
          <p>Loading terminal devices...</p>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.deviceManager}>
      <div className={styles.deviceManagerHeader}>
        <h3>Square Terminal Devices</h3>
        <button
          className={styles.addDeviceButton}
          onClick={() => setShowCreateForm(true)}
          disabled={creating}
        >
          + Add Device
        </button>
      </div>

      {error && (
        <div className={styles.errorMessage}>
          <span className={styles.errorIcon}>⚠️</span>
          {error}
          <button onClick={() => setError('')} className={styles.dismissError}>×</button>
        </div>
      )}

      {showCreateForm && (
        <div className={styles.createDeviceForm}>
          <h4>Create New Device Code</h4>
          <div className={styles.formGroup}>
            <label htmlFor="deviceName">Device Name:</label>
            <input
              id="deviceName"
              type="text"
              value={newDeviceName}
              onChange={(e) => setNewDeviceName(e.target.value)}
              placeholder="e.g., Front Counter Terminal"
              disabled={creating}
            />
          </div>
          <div className={styles.formActions}>
            <button
              className={styles.createButton}
              onClick={createDeviceCode}
              disabled={creating || !newDeviceName.trim()}
            >
              {creating ? 'Creating...' : 'Create Device Code'}
            </button>
            <button
              className={styles.cancelButton}
              onClick={() => {
                setShowCreateForm(false)
                setNewDeviceName('')
                setError('')
              }}
              disabled={creating}
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      <div className={styles.deviceList}>
        {devices.length === 0 ? (
          <div className={styles.noDevices}>
            <div className={styles.noDevicesIcon}>📱</div>
            <h4>No Terminal Devices</h4>
            <p>Create a device code to pair your first Square Terminal.</p>
          </div>
        ) : (
          devices.map((device) => (
            <div key={device.id} className={styles.deviceCard}>
              <div className={styles.deviceHeader}>
                <div className={styles.deviceName}>
                  <span className={styles.deviceIcon}>📱</span>
                  {device.name}
                </div>
                <div 
                  className={styles.deviceStatus}
                  style={{ color: getStatusColor(device.status) }}
                >
                  {getStatusIcon(device.status)} {device.status}
                </div>
              </div>

              <div className={styles.deviceDetails}>
                <div className={styles.deviceCode}>
                  <strong>Pairing Code: </strong>
                  <span 
                    className={styles.codeValue}
                    onClick={() => copyToClipboard(device.code)}
                    title="Click to copy"
                  >
                    {device.code}
                  </span>
                </div>

                <div className={styles.deviceInfo}>
                  <div className={styles.infoItem}>
                    <span className={styles.infoLabel}>Device ID:</span>
                    <span className={styles.infoValue}>
                      {device.deviceId || 'Not paired'}
                    </span>
                  </div>
                  <div className={styles.infoItem}>
                    <span className={styles.infoLabel}>Created:</span>
                    <span className={styles.infoValue}>
                      {formatDate(device.createdAt)}
                    </span>
                  </div>
                  {device.pairBy && (
                    <div className={styles.infoItem}>
                      <span className={styles.infoLabel}>Expires:</span>
                      <span className={styles.infoValue}>
                        {formatDate(device.pairBy)}
                      </span>
                    </div>
                  )}
                </div>

                {device.status === 'UNPAIRED' && (
                  <div className={styles.pairingInstructions}>
                    <h5>Pairing Instructions:</h5>
                    <ol>
                      <li>On your Square Terminal, go to Settings</li>
                      <li>Select "Pair with POS"</li>
                      <li>Enter the pairing code: <strong>{device.code}</strong></li>
                      <li>Follow the on-screen instructions</li>
                    </ol>
                  </div>
                )}
              </div>
            </div>
          ))
        )}
      </div>

      <div className={styles.deviceManagerFooter}>
        <button
          className={styles.refreshButton}
          onClick={loadDevices}
          disabled={loading}
        >
          🔄 Refresh Status
        </button>
        <div className={styles.statusLegend}>
          <span>🟢 Paired</span>
          <span>🔴 Unpaired</span>
          <span>🟡 Unknown</span>
        </div>
      </div>
    </div>
  )
}
