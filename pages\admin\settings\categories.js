import { useState, useEffect, useCallback } from 'react';
import AdminLayout from '@/components/admin/AdminLayout';
import ProtectedRoute from '@/components/admin/ProtectedRoute';
import Modal from '@/components/admin/Modal'; // Assuming this path from InventoryPage
import styles from '@/styles/admin/CategoriesPage.module.css';

const DEFAULT_CATEGORY_STATE = {
  id: null,
  name: '',
  description: '',
  parent_id: '', // Use empty string for "None" option in select
};

export default function CategoriesPage() {
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [apiError, setApiError] = useState(null); // For errors from API calls (add/edit)

  const [showModal, setShowModal] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [currentCategory, setCurrentCategory] = useState(DEFAULT_CATEGORY_STATE);
  const [formErrors, setFormErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false); // For form submission loading state
  const [isDeletingId, setIsDeletingId] = useState(null); // To track which category is being deleted

  const fetchCategories = useCallback(async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await fetch('/api/admin/service-categories');
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to fetch categories: ${response.status}`);
      }
      const data = await response.json();
      setCategories(data.categories || []);
    } catch (err) {
      setError(err.message);
      setCategories([]); // Ensure categories is an array on error
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setCurrentCategory(prev => ({ ...prev, [name]: value }));
    if (formErrors[name]) {
      setFormErrors(prev => ({ ...prev, [name]: null }));
    }
  };

  const validateForm = () => {
    const errors = {};
    if (!currentCategory.name.trim()) {
      errors.name = 'Name is required.';
    }
    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleOpenAddModal = () => {
    setIsEditing(false);
    setCurrentCategory(DEFAULT_CATEGORY_STATE);
    setFormErrors({});
    setApiError(null);
    setShowModal(true);
  };

  const handleOpenEditModal = (category) => {
    setIsEditing(true);
    setCurrentCategory({
      id: category.id,
      name: category.name || '',
      description: category.description || '',
      parent_id: category.parent_id || '',
    });
    setFormErrors({});
    setApiError(null);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setIsEditing(false);
    setCurrentCategory(DEFAULT_CATEGORY_STATE);
    setFormErrors({});
    setApiError(null);
  };

  const handleFormSubmit = async (e) => {
    e.preventDefault();
    if (!validateForm()) {
      return;
    }
    setApiError(null);
    setIsSubmitting(true);

    const payload = {
      name: currentCategory.name.trim(),
      description: currentCategory.description.trim() === '' ? null : currentCategory.description.trim(),
      parent_id: currentCategory.parent_id ? parseInt(currentCategory.parent_id, 10) : null,
    };

    let url = '/api/admin/service-categories';
    let method = 'POST';

    if (isEditing) {
      method = 'PUT';
      payload.id = currentCategory.id; // Add ID for PUT request
    }

    try {
      const response = await fetch(url, {
        method: method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });

      const responseData = await response.json();

      if (!response.ok) {
        throw new Error(responseData.error || `API Error: ${response.status} ${response.statusText}`);
      }

      // Success
      alert(`Category ${isEditing ? 'updated' : 'added'} successfully!`);
      await fetchCategories(); // Refresh categories list
      handleCloseModal();

    } catch (err) {
      console.error(`Failed to ${isEditing ? 'update' : 'add'} category:`, err);
      setApiError(err.message || `An unexpected error occurred.`);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteCategory = async (categoryId, categoryName) => {
    // Use a more specific confirmation message including the category name
    if (!window.confirm(`Are you sure you want to delete the category "${categoryName}"? This action cannot be undone.`)) {
      return;
    }

    setIsDeletingId(categoryId);
    setError(null);      // Clear previous page-level errors, as this is a new action
    setApiError(null);   // Clear previous modal API errors

    try {
      const response = await fetch(`/api/admin/service-categories?id=${categoryId}`, {
        method: 'DELETE',
      });

      const responseData = await response.json(); // Try to parse JSON for error or success messages

      if (!response.ok) {
        // API should return a JSON with an 'error' field for failures
        throw new Error(responseData.error || `API Error: ${response.status} ${response.statusText}`);
      }

      // API returns a 'message' field for success
      alert(responseData.message || `Category "${categoryName}" deleted successfully!`);
      await fetchCategories(); // Refresh categories list

    } catch (err) {
      console.error(`Failed to delete category ID ${categoryId}:`, err);
      // Display error to user using the main page error state
      setError(`Failed to delete category "${categoryName}": ${err.message}`);
      // Optionally, use alert for immediate feedback if setError might be batched
      // alert(`Failed to delete category "${categoryName}": ${err.message}`);
    } finally {
      setIsDeletingId(null);
    }
  };

  const getParentCategoryName = (parentId) => {
    if (!parentId) return 'N/A';
    const parent = categories.find(cat => cat.id === parentId);
    return parent ? parent.name : 'Unknown';
  };


  return (
    <ProtectedRoute adminOnly>
      <AdminLayout title="Service Categories">
        <div className={styles.pageContainer}>
          <div className={styles.header}>
            <h1>Service Categories</h1>
            <button onClick={handleOpenAddModal} className={styles.addButton}>
              Add New Category
            </button>
          </div>

          {loading && <p className={styles.loading}>Loading categories...</p>}
          {error && <p className={styles.error}>Error fetching categories: {error}</p>}

          {!loading && !error && categories.length === 0 && (
            <p className={styles.noCategories}>No categories found. Add one to get started!</p>
          )}

          {!loading && !error && categories.length > 0 && (
            <div className={styles.tableContainer}>
              <table className={styles.categoriesTable}>
                <thead>
                  <tr>
                    <th>Name</th>
                    <th>Description</th>
                    <th>Parent Category</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {categories.map(category => (
                    <tr key={category.id}>
                      <td>{category.name}</td>
                      <td>{category.description || 'N/A'}</td>
                      <td>{getParentCategoryName(category.parent_id)}</td>
                      <td className={styles.actionsCell}>
                        <button
                          onClick={() => handleOpenEditModal(category)}
                          className={styles.editButton}
                          disabled={isDeletingId === category.id} // Disable edit while deleting this row
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleDeleteCategory(category.id, category.name)}
                          className={styles.deleteButton}
                          disabled={isDeletingId === category.id} // Disable delete while deleting this row
                        >
                          {isDeletingId === category.id ? 'Deleting...' : 'Delete'}
                        </button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}

          {showModal && (
            <Modal onClose={handleCloseModal}>
              <div className={styles.modalContent}>
                <h2>{isEditing ? 'Edit Category' : 'Add New Category'}</h2>
                <form onSubmit={handleFormSubmit} className={styles.form}>
                  {apiError && <p className={styles.error}>{apiError}</p>}
                  <div className={styles.formGroup}>
                    <label htmlFor="name">Name <span style={{color: 'red'}}>*</span></label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={currentCategory.name}
                      onChange={handleInputChange}
                      required
                    />
                    {formErrors.name && <p className={styles.errorMessage}>{formErrors.name}</p>}
                  </div>
                  <div className={styles.formGroup}>
                    <label htmlFor="description">Description</label>
                    <textarea
                      id="description"
                      name="description"
                      value={currentCategory.description}
                      onChange={handleInputChange}
                    />
                  </div>
                  <div className={styles.formGroup}>
                    <label htmlFor="parent_id">Parent Category</label>
                    <select
                      id="parent_id"
                      name="parent_id"
                      value={currentCategory.parent_id}
                      onChange={handleInputChange}
                    >
                      <option value="">None</option>
                      {categories
                        .filter(cat => !isEditing || cat.id !== currentCategory.id) // Prevent self-parenting
                        .map(cat => (
                          <option key={cat.id} value={cat.id}>
                            {cat.name}
                          </option>
                        ))}
                    </select>
                  </div>
                  <div className={styles.formActions}>
                    <button type="button" onClick={handleCloseModal} className={styles.cancelButton} disabled={isSubmitting}>
                      Cancel
                    </button>
                    <button type="submit" className={styles.saveButton} disabled={isSubmitting}>
                      {isSubmitting ? (isEditing ? 'Saving...' : 'Adding...') : (isEditing ? 'Save Changes' : 'Add Category')}
                    </button>
                  </div>
                </form>
              </div>
            </Modal>
          )}
        </div>
      </AdminLayout>
    </ProtectedRoute>
  );
}
