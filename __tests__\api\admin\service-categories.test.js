import { createMocks } from 'node-mocks-http';
import handler from '@/pages/api/admin/service-categories';
import { supabaseAdmin } from '@/lib/supabase';

// Mock supabaseAdmin
jest.mock('@/lib/supabase', () => ({
  supabaseAdmin: {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    order: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    delete: jest.fn().mockReturnThis(), // Added delete
    eq: jest.fn().mockReturnThis(),
    single: jest.fn().mockReturnThis(),
  },
}));

// Helper to reset mocks and clear mock data between tests
const resetMocks = () => {
  jest.clearAllMocks(); // Clears all mocks, including call counts

  // Restore default behavior for chained methods
  supabaseAdmin.from.mockReturnThis();
  supabaseAdmin.select.mockReturnThis();
  supabaseAdmin.order.mockReturnThis();
  supabaseAdmin.insert.mockReturnThis();
  supabaseAdmin.update.mockReturnThis();
  supabaseAdmin.delete.mockReturnThis();
  supabaseAdmin.eq.mockReturnThis();
  supabaseAdmin.single.mockReturnThis();
};

describe('/api/admin/service-categories API Endpoint', () => {
  beforeEach(() => {
    resetMocks();
  });

  // Test GET /api/admin/service-categories
  describe('GET handler', () => {
    it('should fetch categories successfully and return serialized data', async () => {
      const mockCategories = [
        { id: 1, name: 'Category A', description: 'Desc A', parent_id: null },
        { id: 2, name: 'Category B', description: 'Desc B', parent_id: 1 },
      ];
      supabaseAdmin.from('service_categories').select().order.mockResolvedValueOnce({ data: mockCategories, error: null });

      const { req, res } = createMocks({ method: 'GET' });
      await handler(req, res);

      expect(res._getStatusCode()).toBe(200);
      const responseJson = JSON.parse(res._getData());
      expect(responseJson.success).toBe(true);
      expect(responseJson.categories).toEqual([
        { id: '1', name: 'Category A', description: 'Desc A', parent_id: null },
        { id: '2', name: 'Category B', description: 'Desc B', parent_id: '1' },
      ]);
      expect(supabaseAdmin.from).toHaveBeenCalledWith('service_categories');
      expect(supabaseAdmin.select).toHaveBeenCalledWith('id, name, description, parent_id');
      expect(supabaseAdmin.order).toHaveBeenCalledWith('name');
    });

    it('should return 500 if there is a database error', async () => {
      supabaseAdmin.from('service_categories').select().order.mockResolvedValueOnce({ data: null, error: new Error('DB Error') });
      const { req, res } = createMocks({ method: 'GET' });
      await handler(req, res);

      expect(res._getStatusCode()).toBe(500);
      expect(JSON.parse(res._getData())).toEqual({
        success: false,
        error: 'Failed to fetch service categories',
        details: 'DB Error',
      });
    });
  });

  // Test POST /api/admin/service-categories
  describe('POST handler', () => {
    it('should create a new category successfully', async () => {
      const newCategoryData = { name: 'New Category', description: 'New Desc', parent_id: null };
      const createdCategory = { id: 3, ...newCategoryData };
      supabaseAdmin.from('service_categories').insert(newCategoryData).select().single.mockResolvedValueOnce({ data: createdCategory, error: null });

      const { req, res } = createMocks({
        method: 'POST',
        body: newCategoryData,
      });
      await handler(req, res);

      expect(res._getStatusCode()).toBe(201);
      const responseJson = JSON.parse(res._getData());
      expect(responseJson.success).toBe(true);
      expect(responseJson.category).toEqual({
        id: '3',
        name: 'New Category',
        description: 'New Desc',
        parent_id: null,
      });
      expect(supabaseAdmin.insert).toHaveBeenCalledWith(newCategoryData);
    });

    it('should return 400 for missing name', async () => {
      const { req, res } = createMocks({ method: 'POST', body: { description: 'No name' } });
      await handler(req, res);
      expect(res._getStatusCode()).toBe(400);
      expect(JSON.parse(res._getData()).error).toBe('Name is required and must be a non-empty string.');
    });

    it('should return 400 for invalid parent_id (non-integer string)', async () => {
      const { req, res } = createMocks({ method: 'POST', body: { name: 'Test', parent_id: 'abc' } });
      await handler(req, res);
      expect(res._getStatusCode()).toBe(400);
      expect(JSON.parse(res._getData()).error).toBe('Parent ID must be an integer if provided.');
    });

    it('should return 400 if parent_id refers to a non-existent category (foreign key violation)', async () => {
      const newCategoryData = { name: 'Orphan Category', parent_id: 999 };
      supabaseAdmin.from('service_categories').insert(expect.objectContaining(newCategoryData)).select().single.mockResolvedValueOnce({
        data: null,
        error: { code: '23503', message: 'foreign key constraint failed' },
      });

      const { req, res } = createMocks({ method: 'POST', body: newCategoryData });
      await handler(req, res);

      expect(res._getStatusCode()).toBe(400);
      expect(JSON.parse(res._getData()).error).toBe('Invalid parent_id. The parent category does not exist.');
    });

    it('should return 409 for duplicate name (unique constraint violation)', async () => {
      const newCategoryData = { name: 'Existing Category' };
      supabaseAdmin.from('service_categories').insert(expect.objectContaining(newCategoryData)).select().single.mockResolvedValueOnce({
        data: null,
        error: { code: '23505', message: 'unique constraint failed' },
      });
      const { req, res } = createMocks({ method: 'POST', body: newCategoryData });
      await handler(req, res);
      expect(res._getStatusCode()).toBe(409);
      expect(JSON.parse(res._getData()).error).toBe('A category with this name already exists.');
    });

    it('should handle null description correctly', async () => {
      const newCategoryData = { name: 'Category with null desc', description: null };
      const createdCategory = { id: 4, name: 'Category with null desc', description: null, parent_id: null };
      supabaseAdmin.from('service_categories').insert(newCategoryData).select().single.mockResolvedValueOnce({ data: createdCategory, error: null });

      const { req, res } = createMocks({
        method: 'POST',
        body: newCategoryData,
      });
      await handler(req, res);
      expect(res._getStatusCode()).toBe(201);
      expect(JSON.parse(res._getData()).category.description).toBeNull();
    });
  });

  // Test PUT /api/admin/service-categories
  describe('PUT handler', () => {
    it('should update an existing category successfully', async () => {
      const updateData = { id: 1, name: 'Updated Name', description: 'Updated Desc', parent_id: null };
      const updatedCategory = { id: 1, name: 'Updated Name', description: 'Updated Desc', parent_id: null };
      // Mock the update operation
      supabaseAdmin.from('service_categories').update({ name: 'Updated Name', description: 'Updated Desc', parent_id: null }).eq('id', 1).select().single.mockResolvedValueOnce({ data: updatedCategory, error: null });

      const { req, res } = createMocks({ method: 'PUT', body: updateData });
      await handler(req, res);

      expect(res._getStatusCode()).toBe(200);
      const responseJson = JSON.parse(res._getData());
      expect(responseJson.success).toBe(true);
      expect(responseJson.category).toEqual({
        id: '1',
        name: 'Updated Name',
        description: 'Updated Desc',
        parent_id: null,
      });
      expect(supabaseAdmin.update).toHaveBeenCalledWith({ name: 'Updated Name', description: 'Updated Desc', parent_id: null });
      expect(supabaseAdmin.eq).toHaveBeenCalledWith('id', 1);
    });

    it('should return 404 if category ID not found for update', async () => {
      const updateData = { id: 999, name: 'Non Existent' };
      supabaseAdmin.from('service_categories').update({ name: 'Non Existent' }).eq('id', 999).select().single.mockResolvedValueOnce({ data: null, error: null });
      const { req, res } = createMocks({ method: 'PUT', body: updateData });
      await handler(req, res);
      expect(res._getStatusCode()).toBe(404);
      expect(JSON.parse(res._getData()).error).toBe('Category with ID 999 not found.');
    });

    it('should return 400 if trying to set parent_id to its own id', async () => {
      const updateData = { id: 1, parent_id: 1 };
      const { req, res } = createMocks({ method: 'PUT', body: updateData });
      await handler(req, res);
      expect(res._getStatusCode()).toBe(400);
      expect(JSON.parse(res._getData()).error).toBe('A category cannot be its own parent.');
    });

    it('should return 400 for missing id', async () => {
      const { req, res } = createMocks({ method: 'PUT', body: { name: 'No ID' } });
      await handler(req, res);
      expect(res._getStatusCode()).toBe(400);
      expect(JSON.parse(res._getData()).error).toBe('Category ID is required.');
    });

    it('should return 400 if no update data is provided', async () => {
      const { req, res } = createMocks({ method: 'PUT', body: { id: 1 } });
      await handler(req, res);
      expect(res._getStatusCode()).toBe(400);
      expect(JSON.parse(res._getData()).error).toBe('No update data provided. Please provide name, description, or parent_id to update.');
    });

    it('should allow updating only description', async () => {
      const updateData = { id: 1, description: 'New Description Only' };
      const updatedCategory = { id: 1, name: 'Original Name', description: 'New Description Only', parent_id: null };
      supabaseAdmin.from('service_categories').update({ description: 'New Description Only' }).eq('id', 1).select().single.mockResolvedValueOnce({ data: updatedCategory, error: null });

      const { req, res } = createMocks({ method: 'PUT', body: updateData });
      await handler(req, res);

      expect(res._getStatusCode()).toBe(200);
      expect(JSON.parse(res._getData()).category.description).toBe('New Description Only');
    });

    it('should allow parent_id to be set to null', async () => {
      const updateData = { id: 1, parent_id: null };
      const updatedCategory = { id: 1, name: 'Original Name', description: 'Desc', parent_id: null };
      supabaseAdmin.from('service_categories').update({ parent_id: null }).eq('id', 1).select().single.mockResolvedValueOnce({ data: updatedCategory, error: null });

      const { req, res } = createMocks({ method: 'PUT', body: updateData });
      await handler(req, res);

      expect(res._getStatusCode()).toBe(200);
      expect(JSON.parse(res._getData()).category.parent_id).toBeNull();
    });
  });

  describe('DELETE handler', () => {
    it('should delete an unused category successfully', async () => {
      const categoryId = '1';
      // Mock: No child categories
      supabaseAdmin.from('service_categories').select().eq('parent_id', 1).mockResolvedValueOnce({ data: [], error: null });
      // Mock: No services using this category
      supabaseAdmin.from('services').select().eq('category_id', 1).mockResolvedValueOnce({ data: [], error: null });
      // Mock: Successful deletion
      supabaseAdmin.from('service_categories').delete().eq('id', 1).select.mockResolvedValueOnce({ data: [{ id: 1, name: 'Deleted Category' }], error: null, count: 1 });

      const { req, res } = createMocks({ method: 'DELETE', query: { id: categoryId } });
      await handler(req, res);

      expect(res._getStatusCode()).toBe(200);
      expect(JSON.parse(res._getData())).toEqual({
        success: true,
        message: `Category with ID ${categoryId} deleted successfully.`,
      });
      expect(supabaseAdmin.from).toHaveBeenCalledWith('service_categories');
      expect(supabaseAdmin.from).toHaveBeenCalledWith('services');
      expect(supabaseAdmin.delete).toHaveBeenCalled();
      expect(supabaseAdmin.eq).toHaveBeenCalledWith('id', 1);
    });

    it('should return 409 if category has sub-categories', async () => {
      const categoryId = '1';
      // Mock: Has child categories
      supabaseAdmin.from('service_categories').select().eq('parent_id', 1).mockResolvedValueOnce({ data: [{ id: 2, name: 'Child Category' }], error: null });

      const { req, res } = createMocks({ method: 'DELETE', query: { id: categoryId } });
      await handler(req, res);

      expect(res._getStatusCode()).toBe(409);
      expect(JSON.parse(res._getData()).error).toBe('Category has sub-categories and cannot be deleted. Please delete or reassign sub-categories first.');
    });

    it('should return 409 if category is in use by services', async () => {
      const categoryId = '1';
      // Mock: No child categories
      supabaseAdmin.from('service_categories').select().eq('parent_id', 1).mockResolvedValueOnce({ data: [], error: null });
      // Mock: Services are using this category
      supabaseAdmin.from('services').select().eq('category_id', 1).mockResolvedValueOnce({ data: [{ id: 101, name: 'Some Service' }], error: null });

      const { req, res } = createMocks({ method: 'DELETE', query: { id: categoryId } });
      await handler(req, res);

      expect(res._getStatusCode()).toBe(409);
      expect(JSON.parse(res._getData()).error).toBe('Category is in use by services and cannot be deleted. Please reassign services to another category first.');
    });

    it('should return 404 if category to delete is not found', async () => {
      const categoryId = '999';
       // Mock: No child categories
      supabaseAdmin.from('service_categories').select().eq('parent_id', 999).mockResolvedValueOnce({ data: [], error: null });
      // Mock: No services using this category
      supabaseAdmin.from('services').select().eq('category_id', 999).mockResolvedValueOnce({ data: [], error: null });
      // Mock: Deletion returns no data (or count 0)
      supabaseAdmin.from('service_categories').delete().eq('id', 999).select.mockResolvedValueOnce({ data: [], error: null, count: 0 });

      const { req, res } = createMocks({ method: 'DELETE', query: { id: categoryId } });
      await handler(req, res);

      expect(res._getStatusCode()).toBe(404);
      expect(JSON.parse(res._getData()).error).toBe(`Category with ID ${categoryId} not found or already deleted.`);
    });

    it('should return 400 for missing category ID', async () => {
      const { req, res } = createMocks({ method: 'DELETE', query: {} });
      await handler(req, res);
      expect(res._getStatusCode()).toBe(400);
      expect(JSON.parse(res._getData()).error).toBe('Category ID is required.');
    });

    it('should return 400 for non-integer category ID', async () => {
      const { req, res } = createMocks({ method: 'DELETE', query: { id: 'abc' } });
      await handler(req, res);
      expect(res._getStatusCode()).toBe(400);
      expect(JSON.parse(res._getData()).error).toBe('Category ID must be an integer.');
    });
  });

  describe('General error handling', () => {
    it('should return 405 for disallowed method (e.g., PATCH)', async () => {
      const { req, res } = createMocks({ method: 'PATCH' });
      await handler(req, res);
      expect(res._getStatusCode()).toBe(405);
      expect(JSON.parse(res._getData()).error).toBe('Method PATCH not allowed.');
    });
  });
});
