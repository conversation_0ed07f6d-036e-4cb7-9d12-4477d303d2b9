-- Migration: Add Quick Events Table for POS Terminal Quick Event Mode
-- This table stores quick event transactions that bypass the full booking workflow

-- Create quick_events table
CREATE TABLE IF NOT EXISTS quick_events (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  transaction_id VARCHAR(100) UNIQUE NOT NULL, -- Unique transaction identifier
  
  -- Service information
  service_id UUID REFERENCES services(id) ON DELETE SET NULL,
  service_name VARCHAR(255) NOT NULL, -- Denormalized for reporting
  
  -- Tier information
  tier_id UUID, -- May not reference actual tier if it's a fallback tier
  tier_name VARCHAR(100) NOT NULL,
  duration INTEGER NOT NULL, -- Duration in minutes
  
  -- Payment information
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'AUD',
  payment_method VARCHAR(50) NOT NULL, -- 'cash', 'card', 'square_terminal', 'square_reader'
  payment_status VARCHAR(20) DEFAULT 'completed',
  payment_details JSONB, -- Store Square payment details, device info, etc.
  
  -- <PERSON><PERSON><PERSON>
  created_by UUI<PERSON> REFERENCES auth.users(id) ON DELETE SET NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_quick_events_transaction_id ON quick_events(transaction_id);
CREATE INDEX IF NOT EXISTS idx_quick_events_service_id ON quick_events(service_id);
CREATE INDEX IF NOT EXISTS idx_quick_events_created_at ON quick_events(created_at);
CREATE INDEX IF NOT EXISTS idx_quick_events_payment_method ON quick_events(payment_method);
CREATE INDEX IF NOT EXISTS idx_quick_events_created_by ON quick_events(created_by);

-- Create updated_at trigger
CREATE OR REPLACE FUNCTION update_quick_events_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_quick_events_updated_at
  BEFORE UPDATE ON quick_events
  FOR EACH ROW
  EXECUTE FUNCTION update_quick_events_updated_at();

-- Add RLS (Row Level Security) policies
ALTER TABLE quick_events ENABLE ROW LEVEL SECURITY;

-- Policy: Allow authenticated users to read quick events
CREATE POLICY "Allow authenticated users to read quick events"
  ON quick_events FOR SELECT
  TO authenticated
  USING (true);

-- Policy: Allow admin users to insert quick events
CREATE POLICY "Allow admin users to insert quick events"
  ON quick_events FOR INSERT
  TO authenticated
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM user_roles 
      WHERE user_id = auth.uid() 
      AND role IN ('admin', 'dev')
    )
  );

-- Policy: Allow admin users to update quick events
CREATE POLICY "Allow admin users to update quick events"
  ON quick_events FOR UPDATE
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM user_roles 
      WHERE user_id = auth.uid() 
      AND role IN ('admin', 'dev')
    )
  );

-- Add comment for documentation
COMMENT ON TABLE quick_events IS 'Quick event transactions for POS Terminal Quick Event Mode - streamlined checkout without full booking workflow';
COMMENT ON COLUMN quick_events.transaction_id IS 'Unique transaction identifier for tracking and receipts';
COMMENT ON COLUMN quick_events.service_name IS 'Denormalized service name for reporting and historical data';
COMMENT ON COLUMN quick_events.tier_name IS 'Denormalized tier name for reporting and historical data';
COMMENT ON COLUMN quick_events.payment_details IS 'JSON storage for Square payment details, device information, etc.';

-- Create view for quick event reporting
CREATE OR REPLACE VIEW quick_events_summary AS
SELECT 
  qe.id,
  qe.transaction_id,
  qe.service_name,
  qe.tier_name,
  qe.duration,
  qe.amount,
  qe.currency,
  qe.payment_method,
  qe.payment_status,
  qe.created_at,
  s.category as service_category,
  u.email as created_by_email
FROM quick_events qe
LEFT JOIN services s ON qe.service_id = s.id
LEFT JOIN auth.users u ON qe.created_by = u.id
ORDER BY qe.created_at DESC;

-- Grant access to the view
GRANT SELECT ON quick_events_summary TO authenticated;

-- Insert sample data for testing (optional - remove in production)
-- INSERT INTO quick_events (
--   transaction_id,
--   service_id,
--   service_name,
--   tier_id,
--   tier_name,
--   duration,
--   amount,
--   currency,
--   payment_method,
--   payment_status
-- ) VALUES (
--   'quick_test_001',
--   (SELECT id FROM services WHERE name ILIKE '%face%' LIMIT 1),
--   'Face Painting',
--   'tier_test_001',
--   'Quick Glitter',
--   30,
--   25.00,
--   'AUD',
--   'cash',
--   'completed'
-- );

-- Migration complete
SELECT 'Quick Events table migration completed successfully' as status;
