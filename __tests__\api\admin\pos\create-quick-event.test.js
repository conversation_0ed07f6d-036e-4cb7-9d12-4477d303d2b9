/**
 * @jest-environment node
 */
// Simplest attempt to ensure util.inherits is available for node-mocks-http
const util = require('util');
if (!util.inherits) {
  // Directly assign from 'node:util' if it's missing on the basic 'util' require.
  // This can happen in some test environments or module loading orders.
  util.inherits = require('node:util').inherits;
}
// If node-mocks-http still fails, the issue is likely deeper in the Jest/node-mocks-http interaction.

import { createMocks } from 'node-mocks-http'
import handler from '@/pages/api/admin/pos/create-quick-event'
import { supabase } from '@/lib/supabase' // Actual path to supabase client
import { authenticateAdminRequest } from '@/lib/auth-utils' // Actual path

jest.mock('@/lib/supabase', () => ({ // Corrected: removed underscore
  supabase: {
    auth: {
      getSession: jest.fn(),
    },
    from: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    select: jest.fn(),
  },
}))

jest.mock('@/lib/auth-utils', () => ({ // Ensure this path is correct
  authenticateAdminRequest: jest.fn(),
}))

describe('/api/admin/pos/create-quick-event API Endpoint', () => {
  let mockQuickEventsInsert
  let mockPaymentsInsert

  beforeEach(() => {
    jest.clearAllMocks()

    // Mock authenticateAdminRequest to simulate successful admin authentication
    (authenticateAdminRequest).mockResolvedValue({
      authorized: true,
      user: { id: 'test-admin-user-id', email: '<EMAIL>' },
    })

    // Specific mock for the chained calls
    mockQuickEventsInsert = jest.fn().mockReturnThis()
    mockPaymentsInsert = jest.fn().mockReturnThis()

    const mockSelectQuickEvents = jest.fn().mockResolvedValue({
      data: [{ id: 'qe_123', created_at: new Date().toISOString() }],
      error: null,
    })
    const mockSelectPayments = jest.fn().mockResolvedValue({
      data: [{ id: 'pay_123' }],
      error: null,
    })

    (supabase.from).mockImplementation((tableName) => {
      if (tableName === 'quick_events') {
        return {
          insert: mockQuickEventsInsert,
          select: mockSelectQuickEvents, // Needed for .select() after insert
        }
      }
      if (tableName === 'payments') {
        return {
          insert: mockPaymentsInsert,
          select: mockSelectPayments, // Needed for .select() after insert
        }
      }
      return { // Default mock for any other table
          insert: jest.fn().mockReturnThis(),
          select: jest.fn().mockResolvedValue({ data: [], error: null }),
      }
    })

    // Ensure .insert(...).select() resolves correctly
    mockQuickEventsInsert.mockReturnValue({ select: mockSelectQuickEvents })
    mockPaymentsInsert.mockReturnValue({ select: mockSelectPayments })


  })

  const validCartItems = [
    {
      service: { id: 's1', name: 'Face Painting' },
      tier: { id: 't1a', name: 'Basic', duration: 15, price: 10.00 },
    },
    {
      service: { id: 's2', name: 'Glitter Tattoo' },
      tier: { id: 't2b', name: 'Deluxe', duration: 30, price: 25.50 },
    },
  ]

  const validPayment = {
    method: 'cash',
    amount: 35.50,
    currency: 'AUD',
    details: null,
  }

  test('should create a quick event transaction with multiple items successfully', async () => {
    const { req, res } = createMocks({
      method: 'POST',
      body: {
        items: validCartItems,
        payment: validPayment,
      },
    })

    await handler(req, res)

    expect(res._getStatusCode()).toBe(201)
    const responseJson = JSON.parse(res._getData())
    expect(responseJson.success).toBe(true)
    expect(responseJson.transaction).toBeDefined()
    expect(responseJson.transaction.itemCount).toBe(validCartItems.length)
    expect(responseJson.transaction.totalAmount).toBe(validPayment.amount)

    // Verify data passed to supabase.from('quick_events').insert()
    expect(mockQuickEventsInsert).toHaveBeenCalledTimes(1)
    const quickEventArg = mockQuickEventsInsert.mock.calls[0][0][0] // First call, first argument, first element of array

    expect(quickEventArg.items_details).toEqual(validCartItems)
    expect(quickEventArg.amount).toBe(validPayment.amount)
    expect(quickEventArg.service_id).toBe(validCartItems[0].service.id)
    expect(quickEventArg.tier_name).toBe(validCartItems[0].tier.name)
    expect(quickEventArg.payment_method).toBe(validPayment.method)
    expect(quickEventArg.created_by).toBe('test-admin-user-id')
    expect(quickEventArg.notes).toContain(`${validCartItems.length} item(s)`)


    // Verify data passed to supabase.from('payments').insert()
    expect(mockPaymentsInsert).toHaveBeenCalledTimes(1)
    const paymentArg = mockPaymentsInsert.mock.calls[0][0][0]
    expect(paymentArg.amount).toBe(validPayment.amount)
    expect(paymentArg.payment_method).toBe(validPayment.method)
    expect(paymentArg.notes).toContain(`${validCartItems.length} item(s)`)
    expect(paymentArg.metadata.type).toBe('quick_event_cart')
    expect(paymentArg.metadata.itemCount).toBe(validCartItems.length)
  })

  test('should return 400 if items array is empty', async () => {
    const { req, res } = createMocks({
      method: 'POST',
      body: {
        items: [], // Empty items array
        payment: validPayment,
      },
    })
    await handler(req, res)
    expect(res._getStatusCode()).toBe(400)
    expect(JSON.parse(res._getData()).error).toBe('Cart items are required')
  })

  test('should return 400 if an item is missing service information', async () => {
    const invalidItems = [
      { tier: { id: 't1', name: 'Tier 1', duration: 10, price: 5 } }, // Missing service
    ]
    const { req, res } = createMocks({
      method: 'POST',
      body: { items: invalidItems, payment: validPayment },
    })
    await handler(req, res)
    expect(res._getStatusCode()).toBe(400)
    expect(JSON.parse(res._getData()).error).toBe('Service information is missing for an item in the cart')
  })

  test('should return 400 if an item is missing tier information', async () => {
    const invalidItems = [
      { service: { id: 's1', name: 'Service 1' } }, // Missing tier
    ]
    const { req, res } = createMocks({
      method: 'POST',
      body: { items: invalidItems, payment: validPayment },
    })
    await handler(req, res)
    expect(res._getStatusCode()).toBe(400)
    expect(JSON.parse(res._getData()).error).toBe('Tier information is missing for an item in the cart')
  })

  test('should return 400 if payment information is missing', async () => {
    const { req, res } = createMocks({
      method: 'POST',
      body: {
        items: validCartItems,
        payment: { method: 'cash' }, // Missing amount and currency
      },
    })
    await handler(req, res)
    expect(res._getStatusCode()).toBe(400)
    expect(JSON.parse(res._getData()).error).toBe('Payment information is required')
  })

  test('should return 401 if admin authentication fails', async () => {
    (authenticateAdminRequest).mockResolvedValueOnce({ authorized: false, error: 'Auth failed' });
    const { req, res } = createMocks({
      method: 'POST',
      body: { items: validCartItems, payment: validPayment },
    });
    await handler(req, res);
    expect(res._getStatusCode()).toBe(401);
    expect(JSON.parse(res._getData()).error).toBe('Auth failed');
  });

  test('should return 500 if Supabase insert into quick_events fails', async () => {
    // Forcing an error on quick_events insert
    const mockSelectError = jest.fn().mockResolvedValue({ data: null, error: { message: 'Supabase error on quick_events' } })
    mockQuickEventsInsert.mockReturnValue({ select: mockSelectError })

    const { req, res } = createMocks({
      method: 'POST',
      body: { items: validCartItems, payment: validPayment },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(500);
    expect(JSON.parse(res._getData()).error).toBe('Failed to create quick event record');
  });

  test('should still succeed if Supabase insert into payments fails (but logs error)', async () => {
    // Forcing an error on payments insert
    const mockSelectPaymentError = jest.fn().mockResolvedValue({ data: null, error: { message: 'Supabase error on payments' } })
    mockPaymentsInsert.mockReturnValue({ select: mockSelectPaymentError })

    // Spy on console.error
    const consoleErrorSpy = jest.spyOn(console, 'error').mockImplementation(() => {});

    const { req, res } = createMocks({
      method: 'POST',
      body: { items: validCartItems, payment: validPayment },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(201); // Should still be 201
    expect(JSON.parse(res._getData()).success).toBe(true);
    expect(consoleErrorSpy).toHaveBeenCalledWith(
        expect.stringContaining('Error creating payment record:'),
        expect.objectContaining({ message: 'Supabase error on payments' })
    );

    consoleErrorSpy.mockRestore();
  });

  test('should handle unexpected errors gracefully', async () => {
    (authenticateAdminRequest).mockImplementationOnce(() => {
      throw new Error("Unexpected auth error");
    });

    const { req, res } = createMocks({
      method: 'POST',
      body: { items: validCartItems, payment: validPayment },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(500);
    expect(JSON.parse(res._getData()).error).toBe('Internal server error');
  });
})
