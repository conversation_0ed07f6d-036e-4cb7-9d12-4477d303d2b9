import { supabase } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/admin-auth'
import { hasBookingPermission, BOOKING_PERMISSIONS } from '@/lib/artist-booking-permissions'

export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(2, 8)
  console.log(`[${requestId}] Weekly Schedule API called: ${req.method}`)

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Authenticate the request
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.authorized) {
      return res.status(401).json({ error: 'Unauthorized' })
    }

    const { user, role } = authResult

    // Check if user has permission to view their own bookings
    if (!hasBookingPermission(role, BOOKING_PERMISSIONS.VIEW_OWN_BOOKINGS)) {
      console.log(`[${requestId}] Access denied for role: ${role}`)
      return res.status(403).json({ error: 'Access denied. Artist/Braider role required.' })
    }

    // Get this week's date range (Monday to Sunday)
    const today = new Date()
    const currentDay = today.getDay() // 0 = Sunday, 1 = Monday, etc.
    const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay // Adjust for Sunday being 0
    
    const startOfWeek = new Date(today)
    startOfWeek.setDate(today.getDate() + mondayOffset)
    startOfWeek.setHours(0, 0, 0, 0)
    
    const endOfWeek = new Date(startOfWeek)
    endOfWeek.setDate(startOfWeek.getDate() + 7)

    console.log(`[${requestId}] Fetching weekly schedule for user: ${user.email}`)
    console.log(`[${requestId}] Week range: ${startOfWeek.toISOString()} to ${endOfWeek.toISOString()}`)

    // Fetch this week's bookings for the artist
    const { data: bookings, error: bookingsError } = await supabase
      .from('bookings')
      .select(`
        id,
        start_time,
        end_time,
        status,
        customers:customer_id (
          name
        ),
        services:service_id (
          name,
          color
        )
      `)
      .or(`assigned_artist_id.eq.${user.id},preferred_artist_id.eq.${user.id}`)
      .gte('start_time', startOfWeek.toISOString())
      .lt('start_time', endOfWeek.toISOString())
      .order('start_time', { ascending: true })

    if (bookingsError) {
      console.error(`[${requestId}] Error fetching bookings:`, bookingsError)
      return res.status(500).json({ error: 'Failed to fetch weekly schedule' })
    }

    // Generate 7 days of the week
    const weekDays = []
    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek)
      date.setDate(startOfWeek.getDate() + i)
      
      const dayStart = new Date(date)
      dayStart.setHours(0, 0, 0, 0)
      
      const dayEnd = new Date(date)
      dayEnd.setHours(23, 59, 59, 999)

      // Filter bookings for this day
      const dayBookings = bookings.filter(booking => {
        const bookingDate = new Date(booking.start_time)
        return bookingDate >= dayStart && bookingDate <= dayEnd
      })

      weekDays.push({
        date: date.toISOString().split('T')[0],
        day_name: date.toLocaleDateString('en-US', { weekday: 'long' }),
        day_short: date.toLocaleDateString('en-US', { weekday: 'short' }),
        is_today: date.toDateString() === today.toDateString(),
        bookings: dayBookings.map(booking => ({
          id: booking.id,
          start_time: booking.start_time,
          end_time: booking.end_time,
          status: booking.status,
          service_name: booking.services?.name || 'Unknown Service',
          service_color: booking.services?.color || '#3b82f6',
          customer_name: booking.customers?.name || 'Unknown Customer'
        })),
        booking_count: dayBookings.length
      })
    }

    const totalBookings = bookings.length

    console.log(`[${requestId}] Successfully fetched ${totalBookings} bookings for this week`)

    res.status(200).json({
      success: true,
      schedule: weekDays,
      week_start: startOfWeek.toISOString().split('T')[0],
      week_end: endOfWeek.toISOString().split('T')[0],
      total_bookings: totalBookings
    })

  } catch (error) {
    console.error(`[${requestId}] Error in weekly schedule API:`, error)
    res.status(500).json({ error: 'Internal server error' })
  }
}
