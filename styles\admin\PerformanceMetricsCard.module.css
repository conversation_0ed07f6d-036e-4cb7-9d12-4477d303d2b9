/* Performance Metrics Card Styles */
.card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin-bottom: 24px;
  border: 1px solid #e5e7eb;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.header h3 {
  margin: 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
}

.periodSelector {
  display: flex;
  background: #f3f4f6;
  border-radius: 8px;
  padding: 4px;
}

.periodButton {
  padding: 8px 16px;
  border: none;
  background: transparent;
  color: #6b7280;
  font-weight: 500;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
}

.periodButton:hover {
  color: #374151;
}

.periodButton.active {
  background: white;
  color: #1f2937;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  color: #6b7280;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Metrics Grid */
.metricsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.metricCard {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.metricHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.metricLabel {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.trendIndicator {
  font-size: 0.75rem;
  font-weight: 600;
}

.metricValue {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.metricSubtext {
  font-size: 0.75rem;
  color: #9ca3af;
}

/* Summary Section */
.summarySection h4 {
  margin: 0 0 16px 0;
  color: #374151;
  font-size: 1rem;
  font-weight: 600;
}

.summaryGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.summaryItem {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.summaryLabel {
  font-size: 0.75rem;
  color: #6b7280;
  text-transform: uppercase;
  font-weight: 500;
}

.summaryValue {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
}

/* Goals Section */
.goalsSection h4 {
  margin: 0 0 16px 0;
  color: #374151;
  font-size: 1rem;
  font-weight: 600;
}

.goalsList {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.goalItem {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
}

.goalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.goalLabel {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.goalProgress {
  font-size: 0.875rem;
  font-weight: 600;
  color: #6b7280;
}

.progressBar {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
}

.progressFill {
  height: 100%;
  background: #3b82f6;
  border-radius: 4px;
  transition: width 0.3s ease;
}

/* Actions Section */
.actionsSection {
  display: flex;
  gap: 12px;
}

.actionButton {
  flex: 1;
  padding: 12px 16px;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  color: #374151;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.actionButton:hover {
  background: #e5e7eb;
  border-color: #9ca3af;
}

/* Responsive Design */
@media (max-width: 768px) {
  .card {
    padding: 16px;
    margin-bottom: 16px;
  }

  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .periodSelector {
    width: 100%;
  }

  .periodButton {
    flex: 1;
    text-align: center;
  }

  .metricsGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .metricCard {
    padding: 16px;
  }

  .metricValue {
    font-size: 1.5rem;
  }

  .summaryGrid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }

  .actionsSection {
    flex-direction: column;
  }

  .goalHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .metricsGrid {
    grid-template-columns: 1fr;
  }

  .summaryGrid {
    grid-template-columns: 1fr;
  }

  .metricHeader {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
