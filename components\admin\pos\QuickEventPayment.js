import { useState } from 'react'
import PaymentMethodSelector from './PaymentMethodSelector'
import POSSquarePaymentNew from './POSSquarePaymentNew'
import POSSquareTerminal from './POSSquareTerminal'
import POSSquareReader from './POSSquareReader'
import { supabase } from '@/lib/supabase'
import { safeRender, safeFormatCurrency } from '@/lib/safe-render-utils' // Added safeFormatCurrency
import styles from '@/styles/admin/POS.module.css'

/**
 * QuickEventPayment component for streamlined payment processing of a cart.
 *
 * @param {Object} props - Component props
 * @param {Array} props.cart - Array of items in the cart [{ service, tier }, ...]
 * @param {Function} props.onBack - Callback to go back
 * @param {Function} props.onComplete - Callback when payment is complete
 * @returns {JSX.Element}
 */
export default function QuickEventPayment({ cart, onBack, onComplete }) {
  const [paymentStep, setPaymentStep] = useState('method') // 'method', 'processing'
  const [paymentMethod, setPaymentMethod] = useState(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [error, setError] = useState(null)

  // Defensive check: Ensure cart is an array to prevent reduce() errors
  const safeCart = Array.isArray(cart) ? cart : []

  // Calculate total amount from the cart
  const totalAmount = safeCart.reduce((sum, item) => {
    const price = parseFloat(item.tier?.price || 0)
    return sum + price
  }, 0)

  const formatDuration = (minutes) => {
    if (minutes < 60) return `${minutes} min`
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes === 0 ? `${hours}h` : `${hours}h ${remainingMinutes}m`
  }

  const handlePaymentMethodSelect = (method, methodData) => {
    setPaymentMethod(method)
    setPaymentStep('processing')
    
    if (method === 'cash') {
      // For cash payments, process immediately
      processCashPayment()
    }
    // For card and terminal payments, the respective Square components will handle the flow
  }

  const processCashPayment = async () => {
    try {
      setIsProcessing(true)
      setError(null)

      // Create quick event transaction record
      const result = await createQuickEventTransaction('cash')

      if (result.success) {
        onComplete(result)
      } else {
        throw new Error(result.error || 'Failed to process cash payment')
      }
    } catch (err) {
      console.error('Cash payment error:', err)
      setError(err.message)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleSquarePaymentSuccess = async (paymentResult) => {
    try {
      setIsProcessing(true)
      setError(null)

      // Create quick event transaction record with Square transaction details
      const paymentMethodType = paymentResult.paymentDetails?.deviceId ? 'square_terminal' : 'card'
      const result = await createQuickEventTransaction(paymentMethodType, paymentResult)

      if (result.success) {
        onComplete(result)
      } else {
        throw new Error(result.error || 'Failed to record payment')
      }
    } catch (err) {
      console.error('Payment completion error:', err)
      setError(err.message)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleSquarePaymentError = (error) => {
    console.error('Square payment error:', error)
    setError(error.message || 'Card payment failed')
    setPaymentStep('method')
    setPaymentMethod(null)
  }

  const createQuickEventTransaction = async (paymentMethodType, paymentDetails = null) => {
    try {
      console.log('💳 Creating quick event transaction for cart...', {
        itemCount: safeCart.length,
        totalAmount,
        paymentMethod: paymentMethodType,
        firstItem: safeCart.length > 0 ? `${safeCart[0].service.name} - ${safeCart[0].tier.name}` : 'Empty Cart'
      })

      // Get admin session for authentication
      const { data: session, error: sessionError } = await supabase.auth.getSession()
      
      if (sessionError || !session?.session) {
        throw new Error('Authentication required for payment processing')
      }

      console.log('✅ Quick event authentication successful for user:', session.session.user?.email)

      const response = await fetch('/api/admin/pos/create-quick-event', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.session.access_token}`,
        },
        body: JSON.stringify({
          items: safeCart.map(item => ({
            service: { id: item.service.id, name: item.service.name },
            tier: { id: item.tier.id, name: item.tier.name, duration: item.tier.duration, price: item.tier.price }
          })),
          payment: {
            method: paymentMethodType,
            amount: totalAmount, // Ensure this is the sum of all cart items
            currency: 'AUD',
            details: paymentDetails
          }
        })
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`)
      }

      const result = await response.json()
      console.log('✅ Quick event transaction created successfully:', result)

      return {
        success: true,
        transaction: result,
        message: 'Quick event payment processed successfully'
      }
    } catch (error) {
      console.error('❌ Error creating quick event transaction:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  const handleBackToMethod = () => {
    setPaymentStep('method')
    setPaymentMethod(null)
    setError(null)
  }

  return (
    <div className={styles.quickEventPayment}>
      <div className={styles.quickPaymentHeader}>
        <h2 className={styles.quickPaymentTitle}>Quick Event Payment</h2>
        <div className={styles.quickPaymentSummary}>
          <div className={styles.serviceSummary}>
            {safeCart.map((item, index) => (
              <div key={index} className={styles.cartItemDisplay}> {/* Using a generic class name, ensure styling if needed */}
                <span className={styles.serviceName}>{safeRender(item.service.name, 'Service')}</span>
                <span className={styles.tierName}>
                  {safeRender(item.tier.name, 'Tier')} ({formatDuration(item.tier.duration)}) - {safeFormatCurrency(item.tier.price)}
                </span>
              </div>
            ))}
          </div>
          <div className={styles.totalAmount}>
            <span className={styles.amountLabel}>Total:</span>
            <span className={styles.amount}>{safeFormatCurrency(totalAmount)}</span>
          </div>
        </div>
      </div>

      {error && (
        <div className={styles.errorMessage}>
          <span className={styles.errorIcon}>⚠️</span>
          <span>{error}</span>
          <button 
            className={styles.errorRetry}
            onClick={() => setError(null)}
          >
            Dismiss
          </button>
        </div>
      )}

      {paymentStep === 'method' && (
        <PaymentMethodSelector
          onPaymentMethodSelect={handlePaymentMethodSelect}
          amount={totalAmount}
          isLoading={isProcessing}
        />
      )}

      {paymentStep === 'processing' && paymentMethod === 'card' && (
        <POSSquarePaymentNew
          amount={totalAmount}
          currency="AUD"
          onSuccess={handleSquarePaymentSuccess}
          onError={handleSquarePaymentError}
          orderDetails={{
            service: safeCart.length > 1 ? 'Multiple Services' : safeCart[0]?.service.name || 'Service',
            tier: safeCart.length > 1 ? `${safeCart.length} items` : safeCart[0]?.tier.name || 'Tier',
            customer: 'Quick Event Customer'
          }}
        />
      )}

      {paymentStep === 'processing' && paymentMethod === 'square_terminal' && (
        <POSSquareTerminal
          amount={totalAmount}
          currency="AUD"
          onSuccess={handleSquarePaymentSuccess}
          onError={handleSquarePaymentError}
          onCancel={handleBackToMethod}
          orderDetails={{
            service: safeCart.length > 1 ? 'Multiple Services' : safeCart[0]?.service.name || 'Service',
            tier: safeCart.length > 1 ? `${safeCart.length} items` : safeCart[0]?.tier.name || 'Tier',
            customer: 'Quick Event Customer',
            orderId: `quick_cart_${Date.now()}_${Math.random().toString(36).substring(2, 8)}` // Added 'cart' to orderId
          }}
        />
      )}

      {paymentStep === 'processing' && paymentMethod === 'square_reader' && (
        <POSSquareReader
          amount={totalAmount}
          currency="AUD"
          onSuccess={handleSquarePaymentSuccess}
          onError={handleSquarePaymentError}
          onCancel={handleBackToMethod}
          orderDetails={{
            service: safeCart.length > 1 ? 'Multiple Services' : safeCart[0]?.service.name || 'Service',
            tier: safeCart.length > 1 ? `${safeCart.length} items` : safeCart[0]?.tier.name || 'Tier',
            customer: 'Quick Event Customer'
          }}
        />
      )}

      <div className={styles.quickPaymentActions}>
        <button
          className={styles.backButton}
          onClick={onBack} // This onBack should lead to the service selection/cart view
          disabled={isProcessing}
        >
          ← Back to Cart
        </button>
      </div>
    </div>
  )
}
