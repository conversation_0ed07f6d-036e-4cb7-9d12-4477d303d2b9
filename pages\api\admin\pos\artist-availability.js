import supabaseAdmin from '@/lib/supabase';
import { authenticateAdminRequest } from '@/lib/admin-auth';

export default async function handler(req, res) {
  console.log('Received request to artist-availability endpoint');

  if (req.method !== 'GET') {
    console.log('Request method is not GET');
    return res.status(405).json({ error: 'Method Not Allowed' });
  }

  try {
    await authenticateAdminRequest(req);
  } catch (error) {
    console.error('Authentication failed:', error);
    if (error.message === 'Unauthorized') {
      return res.status(401).json({ error: 'Unauthorized' });
    }
    return res.status(403).json({ error: 'Forbidden' });
  }

  const { artist_id, date, service_duration_minutes } = req.query;

  console.log('Request parameters:', { artist_id, date, service_duration_minutes });

  if (!artist_id || typeof artist_id !== 'string') {
    return res.status(400).json({ error: 'Missing or invalid artist_id' });
  }

  if (!date || typeof date !== 'string' || isNaN(Date.parse(date))) {
    return res.status(400).json({ error: 'Missing or invalid date' });
  }

  const duration = parseInt(service_duration_minutes, 10);
  if (!service_duration_minutes || typeof service_duration_minutes !== 'string' || isNaN(duration) || duration <= 0) {
    return res.status(400).json({ error: 'Missing or invalid service_duration_minutes' });
  }

  const parsedDate = new Date(date);
  const dayOfWeek = parsedDate.getDay(); // 0 (Sunday) to 6 (Saturday)
  const serviceDuration = parseInt(service_duration_minutes, 10);

  console.log('Parsed inputs:', { parsedDate, dayOfWeek, serviceDuration });

  try {
    // Fetch Artist Profile Details
    const { data: profileData, error: profileError } = await supabaseAdmin
      .from('artist_profiles')
      .select('booking_buffer_time, max_daily_bookings, is_active')
      .eq('artist_id', artist_id)
      .single();

    if (profileError) {
      console.error('Error fetching artist profile:', profileError);
      return res.status(500).json({ error: 'Failed to fetch artist profile', details: profileError.message });
    }

    if (!profileData) {
      return res.status(404).json({ error: 'Artist profile not found' });
    }

    console.log('Fetched artist profile:', profileData);

    if (!profileData.is_active) {
      return res.status(200).json({
        success: true,
        message: 'Artist is not active',
        artist_id,
        date,
        service_duration_minutes,
        available_slots: [],
        profile: profileData,
      });
    }

    // Fetch Regular Schedule
    const { data: scheduleData, error: scheduleError } = await supabaseAdmin
      .from('artist_availability_schedule')
      .select('start_time, end_time, is_available, break_start_time, break_end_time')
      .eq('artist_id', artist_id)
      .eq('day_of_week', dayOfWeek);

    if (scheduleError) {
      console.error('Error fetching regular schedule:', scheduleError);
      return res.status(500).json({ error: 'Failed to fetch regular schedule', details: scheduleError.message });
    }

    // For now, let's assume one schedule record or handle the first one.
    // In a more complex scenario, you might need to iterate or merge if multiple records are possible.
    const schedule = scheduleData && scheduleData.length > 0 ? scheduleData[0] : null;
    console.log('Fetched regular schedule:', schedule);

    // Fetch Exceptions
    const { data: exceptionsData, error: exceptionsError } = await supabaseAdmin
      .from('artist_availability_exceptions')
      .select('exception_type, start_time, end_time')
      .eq('artist_id', artist_id)
      .eq('exception_date', date); // date is in YYYY-MM-DD format

    if (exceptionsError) {
      console.error('Error fetching exceptions:', exceptionsError);
      return res.status(500).json({ error: 'Failed to fetch exceptions', details: exceptionsError.message });
    }
    console.log('Fetched exceptions:', exceptionsData);

    // Renaming for clarity in this subtask
    const regularSchedule = schedule;
    const exceptions = exceptionsData;
    const profile = profileData;

    // 1. Fetch Existing Bookings
    const { data: existingBookingsData, error: bookingsError } = await supabaseAdmin
      .from('bookings')
      .select('start_time, end_time')
      .eq('assigned_artist_id', artist_id)
      .gte('start_time', `${date}T00:00:00.000Z`) // Greater than or equal to the start of the day
      .lt('start_time', `${date}T23:59:59.999Z`)   // Less than the end of the day (effectively same day)
      .not('status', 'in', ['canceled', 'no_show']); // Filter out canceled or no_show bookings

    if (bookingsError) {
      console.error('Error fetching existing bookings:', bookingsError);
      return res.status(500).json({ error: 'Failed to fetch existing bookings', details: bookingsError.message });
    }
    const existingBookings = existingBookingsData || [];
    console.log('Fetched existing bookings:', existingBookings);

    // 2. Check max_daily_bookings
    if (profile.max_daily_bookings && existingBookings.length >= profile.max_daily_bookings) {
      console.log('Artist has reached maximum daily bookings.');
      return res.status(200).json({
        available_slots: [],
        message: "Artist has reached maximum daily bookings."
      });
    }

    // Helper function to create Date objects from time strings (HH:MM:SS) for a given date
    const createDateFromTime = (timeStr, dateObj) => {
        if (!timeStr) return null;
        const [hours, minutes, seconds] = timeStr.split(':');
        const newDate = new Date(dateObj);
        newDate.setUTCHours(parseInt(hours, 10), parseInt(minutes, 10), parseInt(seconds || 0, 10), 0);
        return newDate;
    };

    const inputDateObj = new Date(date + 'T00:00:00Z'); // Ensure we are working with the date part correctly in UTC

    // 3. Determine Effective Working Periods
    let effectiveWorkingPeriods = [];
    let regularBreakStart = null;
    let regularBreakEnd = null;

    if (regularSchedule && regularSchedule.is_available) {
        const periodStart = createDateFromTime(regularSchedule.start_time, inputDateObj);
        const periodEnd = createDateFromTime(regularSchedule.end_time, inputDateObj);
        if (periodStart && periodEnd) {
            effectiveWorkingPeriods.push({ start: periodStart, end: periodEnd });
            console.log('Added regular schedule period:', { start: periodStart.toISOString(), end: periodEnd.toISOString() });
            if (regularSchedule.break_start_time && regularSchedule.break_end_time) {
                regularBreakStart = createDateFromTime(regularSchedule.break_start_time, inputDateObj);
                regularBreakEnd = createDateFromTime(regularSchedule.break_end_time, inputDateObj);
                console.log('Regular break:', { start: regularBreakStart?.toISOString(), end: regularBreakEnd?.toISOString() });
            }
        }
    }

    let unavailablePeriods = []; // For 'unavailable' exceptions with specific times
    let additionalBreaks = [];   // For 'break' exceptions

    exceptions.forEach(ex => {
        const exStart = createDateFromTime(ex.start_time, inputDateObj);
        const exEnd = createDateFromTime(ex.end_time, inputDateObj);

        if (ex.exception_type === 'unavailable') {
            if (!ex.start_time || !ex.end_time) { // Full day off
                console.log('Exception: Full day unavailable');
                effectiveWorkingPeriods = [];
                // Early exit if full day unavailable exception exists
                return res.status(200).json({ available_slots: [], message: "Artist is unavailable on this date (full day)." });
            } else if (exStart && exEnd) { // Partial day unavailable
                console.log('Exception: Partial unavailable period:', { start: exStart.toISOString(), end: exEnd.toISOString() });
                unavailablePeriods.push({ start: exStart, end: exEnd });
            }
        } else if (ex.exception_type === 'custom_hours' && exStart && exEnd) {
            console.log('Exception: Custom hours, replacing regular schedule:', { start: exStart.toISOString(), end: exEnd.toISOString() });
            effectiveWorkingPeriods = [{ start: exStart, end: exEnd }];
            regularBreakStart = null; // Custom hours might override regular breaks unless specified
            regularBreakEnd = null;
        } else if (ex.exception_type === 'break' && exStart && exEnd) {
            console.log('Exception: Additional break:', { start: exStart.toISOString(), end: exEnd.toISOString() });
            additionalBreaks.push({ start: exStart, end: exEnd });
        }
    });

    console.log('Effective working periods after exceptions (initial):', effectiveWorkingPeriods.map(p => ({start: p.start.toISOString(), end: p.end.toISOString()})));

    // If after all considerations, there are no working periods
    if (effectiveWorkingPeriods.length === 0) {
        console.log('No effective working periods determined.');
        return res.status(200).json({ available_slots: [], message: "Artist has no working hours scheduled for this date." });
    }

    // 4. Prepare Blocks to Avoid (Bookings and Breaks)
    let nonWorkablePeriods = [];
    const bookingBufferTimeMillis = (profile.booking_buffer_time || 0) * 60000;

    existingBookings.forEach(booking => {
        const bookingStart = new Date(booking.start_time);
        const bookingEnd = new Date(booking.end_time);
        const bufferBefore = new Date(bookingStart.getTime() - bookingBufferTimeMillis);
        const bufferAfter = new Date(bookingEnd.getTime() + bookingBufferTimeMillis);
        nonWorkablePeriods.push({ start: bufferBefore, end: bufferAfter });
        console.log('Added existing booking to non-workable (with buffer):', { start: bufferBefore.toISOString(), end: bufferAfter.toISOString(), originalStart: bookingStart.toISOString(), originalEnd: bookingEnd.toISOString() });
    });

    if (regularBreakStart && regularBreakEnd) {
        nonWorkablePeriods.push({ start: regularBreakStart, end: regularBreakEnd });
        console.log('Added regular break to non-workable:', { start: regularBreakStart.toISOString(), end: regularBreakEnd.toISOString() });
    }
    additionalBreaks.forEach(b => {
        nonWorkablePeriods.push({ start: b.start, end: b.end });
        console.log('Added additional (exception) break to non-workable:', { start: b.start.toISOString(), end: b.end.toISOString() });
    });
    unavailablePeriods.forEach(up => { // These are specific unavailable times from exceptions
        nonWorkablePeriods.push({ start: up.start, end: up.end });
         console.log('Added partial unavailable (exception) to non-workable:', { start: up.start.toISOString(), end: up.end.toISOString() });
    });

    console.log('All non-workable periods:', nonWorkablePeriods.map(p=> ({start:p.start.toISOString(), end:p.end.toISOString()})));

    // 5. Generate and Filter Time Slots
    const available_slots = [];
    const slotIncrementMinutes = 15; // Configurable: could be from artist profile or global settings
    const serviceDurationMillis = serviceDuration * 60000;

    effectiveWorkingPeriods.forEach(period => {
        let currentTime = new Date(period.start);
        console.log(`Generating slots for period: ${period.start.toISOString()} to ${period.end.toISOString()}`);

        while (currentTime < period.end) {
            const potentialSlotStart = new Date(currentTime);
            const potentialSlotEnd = new Date(potentialSlotStart.getTime() + serviceDurationMillis);

            if (potentialSlotEnd > period.end) {
                console.log(`Slot [${potentialSlotStart.toISOString()}-${potentialSlotEnd.toISOString()}] exceeds period end ${period.end.toISOString()}. Skipping.`);
                break;
            }

            let isOverlapping = false;
            for (const blocked of nonWorkablePeriods) {
                // Check for overlap: (SlotStart < BlockEnd) and (SlotEnd > BlockStart)
                if (potentialSlotStart < blocked.end && potentialSlotEnd > blocked.start) {
                    isOverlapping = true;
                    console.log(`Slot [${potentialSlotStart.toISOString()}-${potentialSlotEnd.toISOString()}] overlaps with non-workable [${blocked.start.toISOString()}-${blocked.end.toISOString()}]. Skipping.`);
                    break;
                }
            }

            if (!isOverlapping) {
                available_slots.push({ time: potentialSlotStart.toISOString(), status: "available" });
                console.log(`Added available slot: ${potentialSlotStart.toISOString()}`);
            }
            currentTime.setTime(currentTime.getTime() + slotIncrementMinutes * 60000);
        }
    });

    // 6. Final Response
    return res.status(200).json({
        available_slots,
        message: available_slots.length > 0 ? "Availability fetched successfully." : "No available slots found for the selected criteria."
    });

  } catch (error) {
    console.error('Unexpected error in artist-availability handler:', error);
    return res.status(500).json({ error: 'Internal Server Error', details: error.message });
  }
}
