import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import ServiceForm from '@/components/admin/inventory/ServiceForm';

// Mock child components and utility functions
jest.mock('@/lib/auth-utils', () => ({
  getAuthToken: jest.fn().mockResolvedValue('dummy-token'),
}));
jest.mock('@/lib/image-utils', () => ({
  resolveImageUrl: jest.fn(url => url),
}));
jest.mock('@/components/admin/inventory/PricingTiersForm', () => () => <div data-testid="pricing-tiers-form">PricingTiersForm Mock</div>);

// Mock global fetch
global.fetch = jest.fn();

const mockServiceCategories = [
  { id: '1', name: 'Category Alpha', description: 'Desc Alpha', parent_id: null },
  { id: '2', name: 'Category Beta', description: 'Desc Beta', parent_id: '1' },
];

const mockSingleCategory = (id, name, description, parent_id) => ({
  id: String(id),
  name: String(name),
  description: description === null ? null : String(description || ''),
  parent_id: parent_id === null ? null : String(parent_id || ''),
});

// Default props for ServiceForm
const defaultProps = {
  service: null,
  onSave: jest.fn(),
  onCancel: jest.fn(),
  onDelete: jest.fn(),
};

describe('ServiceForm - Inline Category Management', () => {
  beforeEach(() => {
    fetch.mockClear();
    // Default mock for fetching categories for the dropdown
    fetch.mockImplementation((url) => {
      if (url === '/api/admin/service-categories') {
        return Promise.resolve({
          ok: true,
          json: async () => ({ success: true, categories: mockServiceCategories }),
        });
      }
      // Fallback for other fetch calls if not specifically mocked in a test
      return Promise.resolve({ ok: false, json: async () => ({ error: 'Unhandled API call' }) });
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('renders category dropdown and "Add New Category" button', async () => {
    render(<ServiceForm {...defaultProps} />);
    await waitFor(() => {
      expect(screen.getByLabelText('Category')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: '+ Add New Category' })).toBeInTheDocument();
    });
    // Check if categories are populated
    expect(await screen.findByRole('option', { name: 'Category Alpha' })).toBeInTheDocument();
  });

  test('"Edit Selected Category" button is initially disabled or not present', async () => {
    render(<ServiceForm {...defaultProps} />);
    await waitFor(() => expect(screen.getByRole('option', { name: 'Category Alpha' })).toBeInTheDocument());

    const editButton = screen.queryByRole('button', { name: /Edit Selected/i });
    // It might be present but disabled, or not present if no category_id
    if (editButton) {
      expect(editButton).toBeDisabled();
    } else {
      // If not present, this is also acceptable as no category is selected
      expect(editButton).not.toBeInTheDocument();
    }
  });


  describe('Inline Add New Category Modal', () => {
    test('opens, validates, submits, and updates main form', async () => {
      const alertSpy = jest.spyOn(window, 'alert').mockImplementation(() => {});
      render(<ServiceForm {...defaultProps} />);
      await waitFor(() => expect(screen.getByRole('button', { name: '+ Add New Category' })).toBeEnabled());

      fireEvent.click(screen.getByRole('button', { name: '+ Add New Category' }));

      // Modal opens
      await screen.findByRole('heading', { name: 'Add New Category' });
      const nameInput = screen.getByLabelText(/Name/i, { selector: 'input[name="newCategoryNameModal"]' });
      const descInput = screen.getByLabelText(/Description/i, { selector: 'textarea[name="newCategoryDescriptionModal"]' });
      const parentSelect = screen.getByLabelText(/Parent Category/i, { selector: 'select[name="newCategoryParentIdModal"]' });
      const submitModalButton = screen.getByRole('button', { name: 'Add Category' });

      // Test validation
      fireEvent.click(submitModalButton);
      expect(await screen.findByText('Category name is required.')).toBeInTheDocument();

      // Fill form
      fireEvent.change(nameInput, { target: { value: 'New Inline Category' } });
      fireEvent.change(descInput, { target: { value: 'Inline Desc' } });
      fireEvent.change(parentSelect, { target: { value: '1' } }); // Parent: Category Alpha

      // Mock POST success for adding category
      const newCreatedCategory = mockSingleCategory(3, 'New Inline Category', 'Inline Desc', '1');
      fetch.mockImplementationOnce((url, options) => { // Specific mock for this POST
        if (url === '/api/admin/service-categories' && options.method === 'POST') {
          return Promise.resolve({
            ok: true,
            status: 201,
            json: async () => ({ success: true, category: newCreatedCategory }),
          });
        }
        return Promise.resolve({ ok: false, json: async () => ({ error: 'Unexpected API call' }) });
      });

      // Mock subsequent category list refresh
      const updatedCategories = [...mockServiceCategories, newCreatedCategory];
      fetch.mockImplementationOnce((url) => { // Specific mock for refresh
         if (url === '/api/admin/service-categories') {
            return Promise.resolve({
              ok: true,
              json: async () => ({ success: true, categories: updatedCategories }),
            });
          }
        return Promise.resolve({ ok: false, json: async () => ({ error: 'Unexpected API call' }) });
      });

      fireEvent.click(submitModalButton);

      await waitFor(() => {
        expect(alertSpy).toHaveBeenCalledWith('Category added successfully!');
      });
      await waitFor(() => {
        // Modal should close
        expect(screen.queryByRole('heading', { name: 'Add New Category' })).not.toBeInTheDocument();
        // Main form's category dropdown should be updated and new category selected
        const mainCategoryDropdown = screen.getByLabelText('Category');
        expect(mainCategoryDropdown).toHaveValue(newCreatedCategory.id); // '3'
        expect(screen.getByRole('option', { name: 'New Inline Category' })).toBeInTheDocument();
      });
      alertSpy.mockRestore();
    });

    test('shows API error in add category modal if submission fails', async () => {
        render(<ServiceForm {...defaultProps} />);
        await waitFor(() => fireEvent.click(screen.getByRole('button', { name: '+ Add New Category' })));
        await screen.findByRole('heading', { name: 'Add New Category' });

        fireEvent.change(screen.getByLabelText(/Name/i, { selector: 'input[name="newCategoryNameModal"]' }), { target: { value: 'Fail Add' } });

        fetch.mockImplementationOnce((url, options) => {
             if (url === '/api/admin/service-categories' && options.method === 'POST') {
                return Promise.resolve({
                    ok: false, status: 400, json: async () => ({ error: 'API add error' })
                });
             }
             return Promise.resolve({ ok: true, json: async () => ({}) }); // Default for other calls
        });

        fireEvent.click(screen.getByRole('button', { name: 'Add Category' }));
        expect(await screen.findByText('API add error')).toBeInTheDocument();
        expect(screen.getByRole('heading', { name: 'Add New Category' })).toBeInTheDocument(); // Modal stays open
    });
  });

  describe('Inline Edit Selected Category Modal', () => {
    test('opens edit modal, submits, and updates main form', async () => {
      const alertSpy = jest.spyOn(window, 'alert').mockImplementation(() => {});
      render(<ServiceForm {...defaultProps} />);
      await waitFor(() => expect(screen.getByRole('option', { name: 'Category Alpha' })).toBeInTheDocument());

      // Select "Category Beta" (id: 2, parent_id: 1) in the main form
      const mainCategoryDropdown = screen.getByLabelText('Category');
      fireEvent.change(mainCategoryDropdown, { target: { value: '2' } });
      expect(mainCategoryDropdown).toHaveValue('2');

      const editButton = await screen.findByRole('button', { name: /Edit Selected/i });
      expect(editButton).toBeEnabled();

      // Mock GET category details for id '2'
      fetch.mockImplementationOnce((url) => {
        if (url === `/api/admin/service-categories/2`) {
          return Promise.resolve({
            ok: true,
            json: async () => ({ success: true, category: mockServiceCategories.find(c => c.id === '2') }),
          });
        }
        return Promise.resolve({ ok: true, json: async () => ({ categories: mockServiceCategories }) }); // for initial load
      });

      fireEvent.click(editButton);

      await screen.findByRole('heading', { name: 'Edit Category' });
      const nameInput = screen.getByLabelText(/Name/i, { selector: 'input[name="editingCategoryName"]' });
      const parentSelect = screen.getByLabelText(/Parent Category/i, { selector: 'select[name="editingCategoryParentId"]' });

      expect(nameInput).toHaveValue('Category Beta');
      expect(parentSelect).toHaveValue('1'); // Parent is Category Alpha

      // Change name and set parent to None
      fireEvent.change(nameInput, { target: { value: 'Category Beta Updated' } });
      fireEvent.change(parentSelect, { target: { value: '' } }); // Set parent to None

      // Mock PUT success for editing category
      const updatedCategoryData = mockSingleCategory(2, 'Category Beta Updated', 'Desc Beta', null);
      fetch.mockImplementationOnce((url, options) => {
        if (url === '/api/admin/service-categories' && options.method === 'PUT') {
          return Promise.resolve({
            ok: true,
            json: async () => ({ success: true, category: updatedCategoryData }),
          });
        }
         return Promise.resolve({ ok: true, json: async () => ({ categories: mockServiceCategories }) });
      });

      // Mock category list refresh
      const finalCategories = mockServiceCategories.map(c => c.id === '2' ? updatedCategoryData : c);
      fetch.mockImplementationOnce((url) => {
         if (url === '/api/admin/service-categories') {
            return Promise.resolve({
              ok: true,
              json: async () => ({ success: true, categories: finalCategories }),
            });
          }
        return Promise.resolve({ ok: false, json: async () => ({ error: 'Unexpected API call' }) });
      });

      fireEvent.click(screen.getByRole('button', { name: 'Save Changes' }));

      await waitFor(() => {
        expect(alertSpy).toHaveBeenCalledWith('Category updated successfully!');
      });
      await waitFor(() => {
        expect(screen.queryByRole('heading', { name: 'Edit Category' })).not.toBeInTheDocument(); // Modal closes
        // Check main dropdown has updated name and is still selected
        expect(mainCategoryDropdown).toHaveValue('2'); // ID is still '2'
        // The option text itself should update after categories refresh
        expect(screen.getByRole('option', { name: 'Category Beta Updated' })).toBeInTheDocument();
      });
      alertSpy.mockRestore();
    });
  });
});
