/**
 * POSSquareReader - Square Reader/Stand integration using Point of Sale API
 * Handles app-to-app communication with Square POS app for card reader transactions
 */

import { useState, useEffect, useCallback } from 'react'
import styles from '@/styles/admin/POS.module.css'

export default function POSSquareReader({ 
  amount, 
  currency = 'AUD', 
  orderDetails, 
  onSuccess, 
  onError, 
  onCancel 
}) {
  const [readerDevices, setReaderDevices] = useState([])
  const [selectedReader, setSelectedReader] = useState(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const [transactionId, setTransactionId] = useState(null)
  const [errorMessage, setErrorMessage] = useState('')
  const [loadingDevices, setLoadingDevices] = useState(true)
  const [sessionData, setSessionData] = useState(null)

  // Load available Reader devices on component mount
  useEffect(() => {
    loadReaderDevices()
    setupCallbackHandler()
  }, [])

  // Store session data for continuity during app switching
  useEffect(() => {
    if (orderDetails) {
      const sessionData = {
        amount,
        currency,
        orderDetails,
        timestamp: Date.now(),
        sessionId: `reader_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`
      }
      setSessionData(sessionData)
      
      // Store in sessionStorage for app switching continuity
      sessionStorage.setItem('square_reader_session', JSON.stringify(sessionData))
    }
  }, [amount, currency, orderDetails])

  const loadReaderDevices = async () => {
    setLoadingDevices(true)
    try {
      const response = await fetch('/api/admin/pos/reader-devices', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
        }
      })
      
      if (response.ok) {
        const data = await response.json()
        setReaderDevices(data.devices || [])
        
        // Auto-select first available reader if only one
        if (data.devices?.length === 1) {
          setSelectedReader(data.devices[0])
        }
      } else {
        throw new Error('Failed to load reader devices')
      }
    } catch (error) {
      console.error('Error loading reader devices:', error)
      setErrorMessage('Failed to load reader devices')
    } finally {
      setLoadingDevices(false)
    }
  }

  const setupCallbackHandler = () => {
    // Set up callback URL for Square POS app return
    const callbackUrl = `${window.location.origin}/admin/pos/reader-callback`
    
    // Store callback handler in window for global access
    window.squareReaderCallback = handleSquareCallback
    
    // Check if we're returning from Square POS app
    const urlParams = new URLSearchParams(window.location.search)
    if (urlParams.has('square_reader_return')) {
      handleSquareCallback(urlParams)
    }
  }

  const handleSquareCallback = useCallback((urlParams) => {
    try {
      // Restore session data
      const storedSession = sessionStorage.getItem('square_reader_session')
      if (storedSession) {
        const session = JSON.parse(storedSession)
        setSessionData(session)
      }

      // Process Square POS app response
      const transactionId = urlParams.get('transaction_id')
      const clientTransactionId = urlParams.get('client_transaction_id')
      const errorCode = urlParams.get('error_code')

      if (errorCode) {
        // Handle error response
        const errorMessage = getErrorMessage(errorCode)
        setErrorMessage(errorMessage)
        setIsProcessing(false)
        onError(new Error(errorMessage))
      } else if (transactionId) {
        // Handle successful transaction
        setIsProcessing(false)
        onSuccess({
          paymentId: transactionId,
          paymentStatus: 'COMPLETED',
          paymentDetails: {
            transactionId,
            clientTransactionId,
            amount: sessionData?.amount || amount,
            currency: sessionData?.currency || currency,
            readerType: selectedReader?.type || 'square_reader',
            sessionId: sessionData?.sessionId
          }
        })
      }

      // Clean up session storage
      sessionStorage.removeItem('square_reader_session')
    } catch (error) {
      console.error('Error handling Square callback:', error)
      setErrorMessage('Failed to process payment response')
      setIsProcessing(false)
      onError(error)
    }
  }, [sessionData, selectedReader, amount, currency, onSuccess, onError])

  const initiateReaderPayment = async () => {
    if (!selectedReader) {
      setErrorMessage('Please select a reader device')
      return
    }

    setIsProcessing(true)
    setErrorMessage('')

    try {
      // Generate client transaction ID
      const clientTransactionId = `reader_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`
      
      // Store transaction details for callback
      const transactionData = {
        clientTransactionId,
        amount,
        currency,
        orderDetails,
        readerType: selectedReader.type,
        timestamp: Date.now()
      }
      
      sessionStorage.setItem('square_reader_transaction', JSON.stringify(transactionData))

      // Detect device type and launch appropriate Square POS flow
      if (isAndroidDevice()) {
        launchAndroidSquarePOS(clientTransactionId)
      } else if (isIOSDevice()) {
        launchIOSSquarePOS(clientTransactionId)
      } else {
        throw new Error('Unsupported device platform for Square Reader integration')
      }

    } catch (error) {
      console.error('Error initiating reader payment:', error)
      setErrorMessage(error.message)
      setIsProcessing(false)
      onError(error)
    }
  }

  const launchAndroidSquarePOS = (clientTransactionId) => {
    const callbackUrl = `${window.location.origin}/admin/pos/reader-callback`
    const amountCents = Math.round(amount * 100)
    
    const intentUrl = 
      `intent:#Intent;` +
      `action=com.squareup.pos.action.CHARGE;` +
      `package=com.squareup;` +
      `S.browser_fallback_url=${encodeURIComponent(callbackUrl)};` +
      `S.com.squareup.pos.WEB_CALLBACK_URI=${encodeURIComponent(callbackUrl)};` +
      `S.com.squareup.pos.CLIENT_ID=${process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID};` +
      `S.com.squareup.pos.API_VERSION=v2.0;` +
      `i.com.squareup.pos.TOTAL_AMOUNT=${amountCents};` +
      `S.com.squareup.pos.CURRENCY_CODE=${currency};` +
      `S.com.squareup.pos.CLIENT_TRANSACTION_ID=${clientTransactionId};` +
      `S.com.squareup.pos.TENDER_TYPES=com.squareup.pos.TENDER_CARD;` +
      `S.com.squareup.pos.NOTE=${encodeURIComponent(orderDetails?.service || 'POS Payment')};` +
      `end`

    window.location.href = intentUrl
  }

  const launchIOSSquarePOS = (clientTransactionId) => {
    const callbackUrl = `${window.location.origin}/admin/pos/reader-callback`
    const amountCents = Math.round(amount * 100)
    
    const dataParameter = {
      amount_money: {
        amount: amountCents.toString(),
        currency_code: currency
      },
      callback_url: callbackUrl,
      client_id: process.env.NEXT_PUBLIC_SQUARE_APPLICATION_ID,
      version: "1.3",
      client_transaction_id: clientTransactionId,
      notes: orderDetails?.service || 'POS Payment',
      options: {
        supported_tender_types: ["CREDIT_CARD", "CARD_ON_FILE"]
      }
    }

    const squareUrl = `square-commerce-v1://payment/create?data=${encodeURIComponent(JSON.stringify(dataParameter))}`
    window.location.href = squareUrl
  }

  const isAndroidDevice = () => {
    return /Android/i.test(navigator.userAgent)
  }

  const isIOSDevice = () => {
    return /iPad|iPhone|iPod/.test(navigator.userAgent)
  }

  const getErrorMessage = (errorCode) => {
    const errorMessages = {
      'user_canceled': 'Payment was cancelled by user',
      'invalid_request': 'Invalid payment request',
      'unauthorized': 'Unauthorized access to Square POS',
      'payment_declined': 'Payment was declined',
      'network_error': 'Network connection error',
      'pos_not_installed': 'Square POS app is not installed',
      'pos_not_logged_in': 'Please log in to Square POS app first'
    }
    
    return errorMessages[errorCode] || `Payment failed: ${errorCode}`
  }

  const handleCancel = () => {
    setIsProcessing(false)
    setTransactionId(null)
    sessionStorage.removeItem('square_reader_session')
    sessionStorage.removeItem('square_reader_transaction')
    onCancel()
  }

  const getReaderIcon = (readerType) => {
    switch (readerType) {
      case 'contactless': return '📲'
      case 'chip': return '💳'
      case 'magstripe': return '💳'
      case 'tap_to_pay': return '📱'
      default: return '📱'
    }
  }

  if (loadingDevices) {
    return (
      <div className={styles.readerPaymentContainer}>
        <div className={styles.loadingState}>
          <div className={styles.loadingSpinner}></div>
          <p>Detecting Square Reader devices...</p>
        </div>
      </div>
    )
  }

  if (readerDevices.length === 0) {
    return (
      <div className={styles.readerPaymentContainer}>
        <div className={styles.noDevicesState}>
          <div className={styles.noDevicesIcon}>📱</div>
          <h3>No Square Reader Available</h3>
          <p>Square POS app with card reader support is required for this payment method.</p>
          <div className={styles.installInstructions}>
            <h4>Setup Instructions:</h4>
            <ol>
              <li>Install Square POS app from your device's app store</li>
              <li>Sign in with your Square account</li>
              <li>Connect your Square Reader device</li>
              <li>Return to this page to process payments</li>
            </ol>
          </div>
          <button 
            className={styles.refreshButton}
            onClick={loadReaderDevices}
          >
            Check Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.readerPaymentContainer}>
      <div className={styles.readerHeader}>
        <h3>Square Reader Payment</h3>
        <div className={styles.paymentAmount}>
          ${parseFloat(amount || 0).toFixed(2)} {currency}
        </div>
      </div>

      {!isProcessing && (
        <div className={styles.readerSelection}>
          <h4>Available Payment Methods</h4>
          <div className={styles.readerList}>
            {readerDevices.map((reader) => (
              <div
                key={reader.id}
                className={`${styles.readerCard} ${
                  selectedReader?.id === reader.id ? styles.selected : ''
                }`}
                onClick={() => setSelectedReader(reader)}
              >
                <div className={styles.readerIcon}>{getReaderIcon(reader.type)}</div>
                <div className={styles.readerInfo}>
                  <div className={styles.readerName}>{reader.name}</div>
                  <div className={styles.readerType}>{reader.description}</div>
                  <div className={styles.readerStatus}>
                    🟢 {reader.status}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {isProcessing && (
        <div className={styles.processingState}>
          <div className={styles.statusIcon}>⏳</div>
          <div className={styles.statusMessage}>
            Launching Square POS app for payment processing...
          </div>
          <div className={styles.readerInstructions}>
            <p>You will be redirected to the Square POS app to complete the payment.</p>
            <p>After payment completion, you'll be returned to this page automatically.</p>
          </div>
        </div>
      )}

      {errorMessage && (
        <div className={styles.errorMessage}>
          <div className={styles.errorIcon}>⚠️</div>
          <div className={styles.errorText}>{errorMessage}</div>
        </div>
      )}

      <div className={styles.readerActions}>
        {!isProcessing ? (
          <button
            className={styles.startPaymentButton}
            onClick={initiateReaderPayment}
            disabled={!selectedReader}
          >
            Launch Square POS
          </button>
        ) : (
          <button
            className={styles.cancelButton}
            onClick={handleCancel}
          >
            Cancel Payment
          </button>
        )}
      </div>
    </div>
  )
}
