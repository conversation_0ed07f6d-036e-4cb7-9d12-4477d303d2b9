.errorBoundary {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: #f9fafb;
  border-radius: 12px;
  margin: 20px 0;
}

.errorContainer {
  text-align: center;
  max-width: 500px;
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.errorIcon {
  font-size: 3rem;
  margin-bottom: 20px;
}

.errorTitle {
  color: #dc2626;
  font-size: 1.5rem;
  font-weight: 600;
  margin: 0 0 16px 0;
}

.errorMessage {
  color: #6b7280;
  font-size: 1rem;
  line-height: 1.6;
  margin: 0 0 32px 0;
}

.errorActions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-bottom: 24px;
}

.retryButton {
  padding: 12px 24px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retryButton:hover {
  background: #2563eb;
  transform: translateY(-1px);
}

.refreshButton {
  padding: 12px 24px;
  background: #6b7280;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refreshButton:hover {
  background: #4b5563;
  transform: translateY(-1px);
}

.errorDetails {
  text-align: left;
  margin-top: 24px;
  padding: 16px;
  background: #f3f4f6;
  border-radius: 8px;
  border: 1px solid #d1d5db;
}

.errorDetails summary {
  cursor: pointer;
  font-weight: 500;
  color: #374151;
  margin-bottom: 12px;
}

.errorStack {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
}

.errorStack h4 {
  color: #374151;
  font-size: 0.875rem;
  font-weight: 600;
  margin: 16px 0 8px 0;
}

.errorStack pre {
  background: #1f2937;
  color: #f9fafb;
  padding: 12px;
  border-radius: 6px;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-word;
  font-size: 0.75rem;
  line-height: 1.4;
}

@media (max-width: 768px) {
  .errorBoundary {
    padding: 20px 10px;
  }
  
  .errorContainer {
    padding: 24px 20px;
  }
  
  .errorActions {
    flex-direction: column;
  }
  
  .retryButton,
  .refreshButton {
    width: 100%;
  }
}
