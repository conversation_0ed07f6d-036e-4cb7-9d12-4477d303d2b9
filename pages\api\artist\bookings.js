import { createClient } from '@supabase/supabase-js';
import { getCurrentUserFromRequest } from '@/lib/supabase'; // Assuming this utility exists

export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(7);
  console.log(`[${requestId}] /api/artist/bookings called, method: ${req.method}`);

  if (req.method !== 'GET') {
    res.setHeader('Allow', ['GET']);
    return res.status(405).json({ error: 'Method Not Allowed' });
  }

  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY,
    { auth: { autoRefreshToken: false, persistSession: false } }
  );

  try {
    const { user, error: userError } = await getCurrentUserFromRequest(req);

    if (userError || !user) {
      console.error(`[${requestId}] Authentication error:`, userError?.message || 'No user found');
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const allowedRoles = ['artist', 'braider', 'admin', 'dev']; // Allow admin/dev for potential broader views later
    if (!user.role || !allowedRoles.includes(user.role)) {
      console.warn(`[${requestId}] User ${user.id} with role '${user.role}' attempted to fetch bookings. Forbidden.`);
      return res.status(403).json({ error: 'Forbidden: You do not have permission to access this resource.' });
    }

    // Get artist_id from artist_profiles table using user.id
    const { data: artistProfile, error: profileError } = await supabase
      .from('artist_profiles')
      .select('id')
      .eq('user_id', user.id)
      .single();

    if (profileError || !artistProfile) {
      console.error(`[${requestId}] Error fetching artist profile for user ${user.id}:`, profileError);
      return res.status(404).json({ error: 'Artist profile not found for the authenticated user.' });
    }
    const artistId = artistProfile.id;

    const { filter = 'upcoming', limit = 10, offset = 0 } = req.query;
    const pageLimit = parseInt(limit, 10);
    const pageOffset = parseInt(offset, 10);
    const now = new Date().toISOString();

    console.log(`[${requestId}] Fetching bookings for artist_id: ${artistId}, filter: ${filter}, limit: ${pageLimit}, offset: ${pageOffset}`);

    // Step 1: Fetch booking_ids assigned to the artist
    const { data: assignments, error: assignmentError } = await supabase
        .from('artist_booking_assignments')
        .select('booking_id')
        .eq('artist_id', artistId);

    if (assignmentError) {
        console.error(`[${requestId}] Error fetching booking assignments for artist ${artistId}:`, assignmentError);
        return res.status(500).json({ error: 'Failed to fetch booking assignments', details: assignmentError.message });
    }

    if (!assignments || assignments.length === 0) {
        console.log(`[${requestId}] No bookings assigned to artist ${artistId}.`);
        return res.status(200).json([]);
    }

    const bookingIds = assignments.map(a => a.booking_id);

    // Step 2: Fetch bookings based on these IDs and the filter
    let query = supabase
      .from('bookings')
      .select(`
        id,
        start_time,
        end_time,
        status,
        location,
        notes,
        total_amount,
        customers (
          id,
          name,
          email,
          phone
        ),
        service_items (
            quantity,
            services (
                id,
                name,
                color,
                duration
            )
        )
      `)
      .in('id', bookingIds)
      .neq('status', 'cancelled'); // Exclude cancelled bookings by default, can be overridden by specific status filters if needed

    if (filter === 'upcoming') {
      query = query.gte('start_time', now).order('start_time', { ascending: true });
    } else if (filter === 'past') {
      query = query.lt('start_time', now).order('start_time', { ascending: false });
    } else {
        // Default or other filters can be added here
        query = query.order('start_time', { ascending: filter === 'upcoming' });
    }

    query = query.range(pageOffset, pageOffset + pageLimit - 1);

    const { data: bookingsData, error: bookingsError } = await query;

    if (bookingsError) {
      console.error(`[${requestId}] Error fetching bookings for artist ${artistId}:`, bookingsError);
      return res.status(500).json({ error: 'Failed to fetch bookings', details: bookingsError.message });
    }

    // Simplified service name for now, assuming one service per booking for this card
    // A more complex booking item structure would require different mapping
    const formattedBookings = bookingsData.map(booking => {
        let service_name = 'Multiple services';
        if (booking.service_items && booking.service_items.length === 1 && booking.service_items[0].services) {
            service_name = booking.service_items[0].services.name;
        } else if (booking.service_items && booking.service_items.length > 1) {
            service_name = booking.service_items.map(si => si.services?.name || 'Unknown Service').join(' & ');
        } else if (booking.services) { // Fallback if services is directly on booking (older structure?)
             service_name = booking.services.name;
        }

        return {
            id: booking.id,
            start_time: booking.start_time,
            end_time: booking.end_time,
            status: booking.status,
            location: booking.location,
            notes: booking.notes,
            total_amount: booking.total_amount,
            customer_name: booking.customers?.name,
            customer_email: booking.customers?.email, // Be mindful of PII
            customer_phone: booking.customers?.phone, // Be mindful of PII
            service_name: service_name,
            // For simplicity, taking color/duration from the first service item if multiple
            service_color: booking.service_items?.[0]?.services?.color,
            service_duration: booking.service_items?.[0]?.services?.duration,
        };
    });


    console.log(`[${requestId}] Bookings fetched successfully for artist ${artistId}:`, formattedBookings.length);
    return res.status(200).json(formattedBookings);

  } catch (error) {
    console.error(`[${requestId}] Unexpected error in /api/artist/bookings:`, error);
    return res.status(500).json({ error: 'Internal Server Error', details: error.message });
  }
}
