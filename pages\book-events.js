import { useState, useEffect } from 'react';
import Head from 'next/head';
import Link from 'next/link';
import Layout from '@/components/Layout';
import BookingModal from '@/components/BookingModal';
import { safeRender } from '@/lib/safe-render-utils';
import styles from '@/styles/BookOnline.module.css';

// ServiceCard component for events booking
function EventServiceCard({ service, onBookService }) {
  const serviceName = safeRender(service?.title, 'Service');
  const serviceDescription = safeRender(service?.description, 'No description available');
  const serviceImage = safeRender(service?.image, '/images/services/face-paint.jpg');
  const serviceBookingLink = `/book-events?service=${encodeURIComponent(service?.id || '')}`;

  const handleBookingClick = (e) => {
    e.preventDefault();
    onBookService(service);
  };

  return (
    <div className={styles.serviceCard}>
      {serviceImage && (
        <div className={styles.serviceImageContainer}>
          <img
            src={serviceImage || '/images/placeholder.svg'}
            alt={serviceName || 'Service'}
            width="400"
            height="250"
            className={styles.serviceImage}
            onError={(e) => {
              e.target.onerror = null;
              e.target.src = '/images/services/placeholder.jpg';
            }}
          />
        </div>
      )}

      <div className={styles.serviceContent}>
        <div className={styles.serviceHeader}>
          <h3 className={styles.serviceTitle}>
            <span className={styles.serviceIcon}>{service?.icon || '🎨'}</span>
            {serviceName}
          </h3>
          {service?.featured && (
            <span className={styles.featuredBadge}>⭐ Featured</span>
          )}
        </div>

        <p className={styles.serviceDescription}>
          {serviceDescription}
        </p>

        <div className={styles.servicePricing}>
          {service?.pricing && Array.isArray(service.pricing) && service.pricing.length > 0 ? (
            service.pricing.map((tier, index) => (
              <div key={index} className={styles.pricingTier}>
                <span className={styles.tierTitle}>{safeRender(tier.title, '')}</span>
                <span className={styles.tierPrice}>{safeRender(tier.price, '')}</span>
              </div>
            ))
          ) : (
            <div className={styles.pricingTier}>
              <span className={styles.tierTitle}>Contact for pricing</span>
              <span className={styles.tierPrice}></span>
            </div>
          )}
        </div>

        <div className={styles.serviceActions}>
          <button
            className={styles.bookButton}
            onClick={handleBookingClick}
          >
            Request Event Quote
          </button>
          <div className={styles.eventBadges}>
            <span className={styles.eventBadge}>🎉 Event Suitable</span>
            <span className={styles.eventBadge}>👥 Group Discounts</span>
          </div>
        </div>
      </div>
    </div>
  );
}

export default function BookEvents() {
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedService, setSelectedService] = useState(null);
  const [showModal, setShowModal] = useState(false);

  // Event booking specific data
  const bookingData = {
    pageTitle: "Book Events & Special Occasions",
    pageDescription: "Professional face painting, body art, and beauty services for your special events, parties, and corporate functions.",
    heroTitle: "Transform Your Event with Ocean Soul Sparkles",
    heroSubtitle: "Professional artists bringing magic to birthdays, corporate events, festivals, and special occasions across Australia",
    servicesIntro: "Choose from our range of event-suitable services with group discounts and custom packages available.",
    ctaTitle: "Ready to make your event unforgettable?",
    ctaDescription: "Contact us for custom quotes, group bookings, and event packages tailored to your needs."
  };

  useEffect(() => {
    fetchEventsServices();
  }, []);

  const fetchEventsServices = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🎉 Fetching events services...');
      const response = await fetch('/api/public/events-services');

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('✅ Events services loaded:', data.services?.length || 0);

      setServices(data.services || []);
    } catch (err) {
      console.error('❌ Error fetching events services:', err);
      setError('Failed to load services. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleBookService = (service) => {
    console.log('🎯 Event service selected:', service?.title);
    setSelectedService(service);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setSelectedService(null);
  };

  return (
    <Layout>
      <Head>
        <title>{bookingData.pageTitle} | Ocean Soul Sparkles</title>
        <meta name="description" content={bookingData.pageDescription} />
        <meta name="keywords" content="event face painting, corporate events, birthday parties, festival services, group bookings, Australia" />
        <meta property="og:title" content={`${bookingData.pageTitle} | Ocean Soul Sparkles`} />
        <meta property="og:description" content={bookingData.pageDescription} />
        <meta property="og:type" content="website" />
      </Head>

      <main className={styles.main}>
        {/* Hero Section */}
        <section className={styles.hero}>
          <div className={styles.heroContent}>
            <h1 className={styles.heroTitle}>{bookingData.heroTitle}</h1>
            <p className={styles.heroSubtitle}>{bookingData.heroSubtitle}</p>
            <div className={styles.heroFeatures}>
              <span className={styles.feature}>🎨 Professional Artists</span>
              <span className={styles.feature}>👥 Group Discounts</span>
              <span className={styles.feature}>📅 Event Packages</span>
              <span className={styles.feature}>🚀 Custom Quotes</span>
            </div>
          </div>
        </section>

        {/* Services Section */}
        <section className={styles.servicesSection}>
          <h2>{bookingData.servicesIntro}</h2>
          {error && (
            <div className={styles.errorContainer}>
              <p className={styles.errorMessage}>{error}</p>
              <button onClick={fetchEventsServices} className={styles.retryButton}>
                Try Again
              </button>
            </div>
          )}
          {loading ? (
            <div className={styles.loadingContainer}>
              <div className={styles.loadingSpinner}></div>
              <p>Loading event services...</p>
            </div>
          ) : (
            <div className={styles.servicesGrid}>
              {services.map((service) => (
                <div
                  key={String(service.id || Math.random())}
                  className={styles.serviceCardWrapper}
                >
                  <EventServiceCard
                    service={service}
                    onBookService={handleBookService}
                  />
                </div>
              ))}
            </div>
          )}
        </section>

        {/* Call to Action Section */}
        <section className={styles.cta}>
          <h2>{bookingData.ctaTitle}</h2>
          <p>{bookingData.ctaDescription}</p>
          <div className={styles.ctaButtons}>
            <Link href="/contact" className={styles.button}>
              Get Custom Quote
            </Link>
            <Link href="/book-online" className={`${styles.button} ${styles.outlineButton}`}>
              Individual Booking
            </Link>
          </div>
        </section>

        {/* Event Types Section */}
        <section className={styles.eventTypes}>
          <h2>Perfect for Your Event</h2>
          <div className={styles.eventTypesGrid}>
            <div className={styles.eventType}>
              <span className={styles.eventIcon}>🎂</span>
              <h3>Birthday Parties</h3>
              <p>Make birthdays magical with professional face painting and body art</p>
            </div>
            <div className={styles.eventType}>
              <span className={styles.eventIcon}>🏢</span>
              <h3>Corporate Events</h3>
              <p>Team building and corporate entertainment with professional artists</p>
            </div>
            <div className={styles.eventType}>
              <span className={styles.eventIcon}>🎪</span>
              <h3>Festivals & Fairs</h3>
              <p>Large-scale event services with multiple artists and stations</p>
            </div>
            <div className={styles.eventType}>
              <span className={styles.eventIcon}>💒</span>
              <h3>Special Occasions</h3>
              <p>Weddings, graduations, and milestone celebrations</p>
            </div>
          </div>
        </section>
      </main>

      {/* Render the modal at the page level */}
      {showModal && selectedService && (
        <BookingModal
          service={selectedService}
          onClose={handleCloseModal}
          isRequestBooking={true}
          bookingType="event"
        />
      )}
    </Layout>
  );
}
