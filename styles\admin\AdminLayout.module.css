.adminLayout {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  width: 250px;
  background: linear-gradient(180deg, #e8d5e8 0%, #d5c8d5 100%);
  color: #5a5a5a;
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  z-index: 100;
  transition: width 0.3s ease, transform 0.3s ease;
  overflow-x: hidden;
  overflow-y: auto;
  box-shadow: 2px 0 10px rgba(200, 162, 200, 0.15);
}

/* Collapsed sidebar state - icon-only mode */
.sidebar.collapsed {
  width: 70px;
  overflow-x: hidden;
}

.sidebarHeader {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(184, 152, 184, 0.3);
  transition: padding 0.3s ease;
  background: rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(10px);
}

.sidebar.collapsed .sidebarHeader {
  padding: 15px 10px;
  justify-content: space-between;
  flex-direction: column;
  gap: 10px;
}

.logoContainer {
  display: flex;
  justify-content: center;
  width: 100%;
}

.logo {
  max-width: 150px;
  height: auto;
  transition: opacity 0.3s ease;
}

.compactLogo {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #c8a2c8, #b8d4f5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 14px;
  color: white;
  cursor: pointer;
  transition: transform 0.2s ease;
  box-shadow: 0 2px 8px rgba(200, 162, 200, 0.3);
}

.compactLogo:hover {
  transform: scale(1.1);
}

.logoInitials {
  font-size: 12px;
  font-weight: 700;
  letter-spacing: 1px;
}

.collapseToggle {
  background: none;
  border: none;
  color: rgba(90, 90, 90, 0.8);
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.collapseToggle:hover {
  background-color: rgba(200, 162, 200, 0.2);
  color: #5a5a5a;
}

.sidebar.collapsed .collapseToggle {
  align-self: center;
}

.closeSidebar {
  display: none;
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
}

.sidebarNav {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
  overflow-x: hidden;
}

.navLink {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: rgba(90, 90, 90, 0.8);
  text-decoration: none;
  transition: all 0.2s;
  position: relative;
  white-space: nowrap;
  overflow: hidden;
  border-radius: 8px;
  margin: 2px 12px;
}

.sidebar.collapsed .navLink {
  padding: 12px;
  justify-content: center;
  margin: 4px 8px;
  border-radius: 8px;
}

.navLink:hover {
  background-color: rgba(200, 162, 200, 0.2);
  color: #5a5a5a;
  transform: translateX(4px);
}

.navLink.active {
  background: linear-gradient(135deg, #c8a2c8, #b8d4f5);
  color: white;
  box-shadow: 0 2px 8px rgba(200, 162, 200, 0.3);
}

.navLink svg {
  margin-right: 10px;
  flex-shrink: 0;
}

.sidebar.collapsed .navLink svg {
  margin-right: 0;
}

.navText {
  transition: opacity 0.3s ease;
}

.sidebar.collapsed .navText {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

/* Legacy support for existing navigation items */
.sidebarNav a {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: rgba(90, 90, 90, 0.8);
  text-decoration: none;
  transition: all 0.2s;
  white-space: nowrap;
  overflow: hidden;
  border-radius: 8px;
  margin: 2px 12px;
}

.sidebar.collapsed .sidebarNav a {
  padding: 12px;
  justify-content: center;
  margin: 4px 8px;
  border-radius: 8px;
}

.sidebarNav a:hover {
  background-color: rgba(200, 162, 200, 0.2);
  color: #5a5a5a;
  transform: translateX(4px);
}

.sidebarNav a.active {
  background: linear-gradient(135deg, #c8a2c8, #b8d4f5);
  color: white;
  box-shadow: 0 2px 8px rgba(200, 162, 200, 0.3);
}

.sidebarNav a svg {
  margin-right: 10px;
  flex-shrink: 0;
}

.sidebar.collapsed .sidebarNav a svg {
  margin-right: 0;
}

/* Hide text content in collapsed state for all navigation items */
.sidebar.collapsed .sidebarNav a {
  overflow: hidden;
  white-space: nowrap;
}

.sidebar.collapsed .sidebarNav a svg + * {
  opacity: 0;
  width: 0;
  margin-left: 0;
  transition: opacity 0.3s ease, width 0.3s ease;
}

/* Tooltip styles for collapsed sidebar */
.sidebar.collapsed .navLink[title]:hover::after,
.sidebar.collapsed .sidebarNav a[title]:hover::after {
  content: attr(title);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background-color: #2c3e50;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 14px;
  white-space: nowrap;
  z-index: 1000;
  margin-left: 10px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar.collapsed .navLink[title]:hover::before,
.sidebar.collapsed .sidebarNav a[title]:hover::before {
  content: '';
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  border: 6px solid transparent;
  border-right-color: #2c3e50;
  z-index: 1001;
  margin-left: 4px;
}

.sidebarFooter {
  padding: 20px;
  border-top: 1px solid rgba(184, 152, 184, 0.3);
  transition: padding 0.3s ease;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.sidebar.collapsed .sidebarFooter {
  padding: 15px 10px;
}

.userInfo {
  display: flex;
  flex-direction: column;
  margin-bottom: 15px;
  transition: opacity 0.3s ease;
  overflow: hidden;
}

.userName {
  font-weight: 500;
  margin-bottom: 5px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.userRole {
  font-size: 12px;
  color: rgba(90, 90, 90, 0.6);
  text-transform: uppercase;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.signOutButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 10px;
  background: linear-gradient(135deg, #f5b8b8, #f5d5a8);
  border: none;
  border-radius: 8px;
  color: #5a5a5a;
  cursor: pointer;
  transition: all 0.2s;
  justify-content: center;
  font-weight: 500;
}

.sidebar.collapsed .signOutButton {
  padding: 12px;
  justify-content: center;
}

.signOutButton:hover {
  background: linear-gradient(135deg, #f5a3a3, #f5ca93);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(245, 184, 184, 0.3);
}

.signOutButton svg {
  margin-right: 8px;
  flex-shrink: 0;
}

.sidebar.collapsed .signOutButton svg {
  margin-right: 0;
}

.sidebar.collapsed .signOutButton span {
  opacity: 0;
  width: 0;
  overflow: hidden;
}

/* Navigation divider */
.navDivider {
  height: 1px;
  background-color: rgba(255, 255, 255, 0.1);
  margin: 10px 20px;
  transition: margin 0.3s ease;
}

.sidebar.collapsed .navDivider {
  margin: 10px 8px;
}

/* Settings button styling */
.settingsButton {
  background: none;
  border: none;
  cursor: pointer;
  text-align: left;
  width: 100%;
}

.settingsButton:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Settings Modal Styles */
.settingsModal {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

.settingsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.settingsSection {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  border: 1px solid #e9ecef;
}

.settingsSection h3 {
  margin: 0 0 15px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.settingsLinks {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.settingsLinks a {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 15px;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  text-decoration: none;
  color: #495057;
  transition: all 0.2s ease;
  font-size: 14px;
}

.settingsLinks a:hover {
  background: #6a0dad;
  color: white;
  border-color: #6a0dad;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(106, 13, 173, 0.2);
}

.settingsLinks a svg {
  flex-shrink: 0;
}

.authRecoveryButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 8px;
  margin-bottom: 8px;
  background-color: rgba(255, 193, 7, 0.8);
  border: none;
  border-radius: 4px;
  color: #212529;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: background-color 0.2s;
}

.authRecoveryButton:hover {
  background-color: rgba(255, 193, 7, 1);
}

.authRecoveryButton svg {
  margin-right: 6px;
}

.mainContent {
  flex: 1;
  margin-left: 70px; /* Default to collapsed layout */
  display: flex;
  flex-direction: column;
  background-color: #f5f7fa;
  transition: margin-left 0.3s ease;
  overflow-x: hidden;
}

/* Collapsed layout main content (default) */
.mainContent.collapsedLayout {
  margin-left: 70px;
}

/* Expanded layout main content */
.mainContent.expandedLayout {
  margin-left: 250px;
}

.header {
  height: 70px;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  padding: 0 20px;
  position: sticky;
  top: 0;
  z-index: 10;
}

.menuButton {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  margin-right: 15px;
}

.pageTitle {
  font-size: 20px;
  font-weight: 500;
  color: #333;
  margin: 0;
}

.headerActions {
  margin-left: auto;
  display: flex;
  align-items: center;
}

.content {
  padding: 20px;
  flex: 1;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden; /* Prevent horizontal scrolling */
}

/* Mobile styles */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    width: 280px;
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .closeSidebar {
    display: block;
  }

  /* Override both collapsed and expanded layouts on mobile */
  .mainContent,
  .mainContent.collapsedLayout,
  .mainContent.expandedLayout {
    margin-left: 0;
  }

  .menuButton {
    display: block;
  }

  /* Hide collapse toggle on mobile since sidebar overlays */
  .collapseToggle {
    display: none;
  }

  .content {
    padding: 15px;
  }

  .header {
    padding: 0 15px;
  }
}

/* iPhone 13 Pro Max and similar devices */
@media (max-width: 428px) {
  .content {
    padding: 12px;
  }

  .header {
    padding: 0 12px;
    height: 60px;
  }

  .pageTitle {
    font-size: 18px;
  }
}

/* Extra small mobile devices */
@media (max-width: 375px) {
  .content {
    padding: 10px;
  }

  .header {
    padding: 0 10px;
    height: 55px;
  }

  .pageTitle {
    font-size: 16px;
  }
}

/* Very small devices */
@media (max-width: 320px) {
  .content {
    padding: 8px;
  }

  .header {
    padding: 0 8px;
    height: 50px;
  }

  .pageTitle {
    font-size: 15px;
  }
}
