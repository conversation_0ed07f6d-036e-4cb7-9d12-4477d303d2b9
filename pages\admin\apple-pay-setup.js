/**
 * Apple Pay Setup and Verification Page
 * Admin interface for configuring and testing Apple Pay domain verification
 */

import { useState, useEffect } from 'react'
import AdminLayout from '@/components/admin/AdminLayout'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import AuthenticationGuard from '@/components/admin/AuthenticationGuard'
import styles from '@/styles/admin/Settings.module.css'

export default function ApplePaySetupPage() {
  const [verificationResult, setVerificationResult] = useState(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const runVerification = async () => {
    setLoading(true)
    setError('')
    
    try {
      const response = await fetch('/api/apple-pay/verify-domain')
      const result = await response.json()
      
      if (response.ok) {
        setVerificationResult(result)
      } else {
        setError(result.message || 'Verification failed')
      }
    } catch (err) {
      setError('Failed to run verification: ' + err.message)
    } finally {
      setLoading(false)
    }
  }

  const testDomainAssociation = async () => {
    try {
      const response = await fetch('/.well-known/apple-developer-merchantid-domain-association')
      if (response.ok) {
        const content = await response.text()
        alert(`Domain association file accessible!\nContent length: ${content.length} characters\nFirst 50 chars: ${content.substring(0, 50)}...`)
      } else {
        alert(`Domain association file not accessible. Status: ${response.status}`)
      }
    } catch (err) {
      alert(`Error accessing domain association file: ${err.message}`)
    }
  }

  useEffect(() => {
    runVerification()
  }, [])

  const getStatusColor = (status) => {
    switch (status) {
      case 'PASS': return '#28a745'
      case 'FAIL': return '#dc3545'
      default: return '#ffc107'
    }
  }

  const getCheckIcon = (passed) => {
    return passed ? '✅' : '❌'
  }

  return (
    <AuthenticationGuard>
      <ProtectedRoute adminOnly>
        <AdminLayout title="Apple Pay Setup">
          <div className={styles.settingsContainer}>
            <div className={styles.settingsHeader}>
              <h1>Apple Pay Domain Verification</h1>
              <p>Configure and verify Apple Pay domain association for Square Web Payments SDK</p>
            </div>

            <div className={styles.settingsContent}>
              {/* Setup Instructions */}
              <div className={styles.settingsSection}>
                <h2>Setup Instructions</h2>
                <div className={styles.instructionsList}>
                  <div className={styles.instructionStep}>
                    <span className={styles.stepNumber}>1</span>
                    <div className={styles.stepContent}>
                      <h3>Download Verification File</h3>
                      <p>In Square Developer Console → Apple Pay → Web, click "Download verification file"</p>
                    </div>
                  </div>
                  
                  <div className={styles.instructionStep}>
                    <span className={styles.stepNumber}>2</span>
                    <div className={styles.stepContent}>
                      <h3>Replace Placeholder File</h3>
                      <p>Replace the file at <code>public/.well-known/apple-developer-merchantid-domain-association</code> with the downloaded file</p>
                    </div>
                  </div>
                  
                  <div className={styles.instructionStep}>
                    <span className={styles.stepNumber}>3</span>
                    <div className={styles.stepContent}>
                      <h3>Deploy to Production</h3>
                      <p>Deploy your changes to production with HTTPS enabled</p>
                    </div>
                  </div>
                  
                  <div className={styles.instructionStep}>
                    <span className={styles.stepNumber}>4</span>
                    <div className={styles.stepContent}>
                      <h3>Verify in Square Console</h3>
                      <p>Return to Square Developer Console and click "Verify" to complete domain verification</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Verification Results */}
              <div className={styles.settingsSection}>
                <div className={styles.sectionHeader}>
                  <h2>Verification Status</h2>
                  <div className={styles.sectionActions}>
                    <button 
                      onClick={runVerification}
                      disabled={loading}
                      className={styles.primaryButton}
                    >
                      {loading ? 'Checking...' : 'Run Verification'}
                    </button>
                    <button 
                      onClick={testDomainAssociation}
                      className={styles.secondaryButton}
                    >
                      Test URL
                    </button>
                  </div>
                </div>

                {error && (
                  <div className={styles.errorMessage}>
                    <span className={styles.errorIcon}>⚠️</span>
                    {error}
                  </div>
                )}

                {verificationResult && (
                  <div className={styles.verificationResults}>
                    <div className={styles.overallStatus} style={{ borderColor: getStatusColor(verificationResult.status) }}>
                      <h3 style={{ color: getStatusColor(verificationResult.status) }}>
                        Overall Status: {verificationResult.status}
                      </h3>
                      <p>Last checked: {new Date(verificationResult.timestamp).toLocaleString()}</p>
                    </div>

                    <div className={styles.checksGrid}>
                      <div className={styles.checkItem}>
                        <span className={styles.checkIcon}>{getCheckIcon(verificationResult.checks.fileExists)}</span>
                        <span className={styles.checkLabel}>File Exists</span>
                        <span className={styles.checkValue}>{verificationResult.checks.fileExists ? 'Yes' : 'No'}</span>
                      </div>

                      <div className={styles.checkItem}>
                        <span className={styles.checkIcon}>{getCheckIcon(verificationResult.checks.apiEndpointAccessible)}</span>
                        <span className={styles.checkLabel}>API Accessible</span>
                        <span className={styles.checkValue}>{verificationResult.checks.apiStatusCode || 'N/A'}</span>
                      </div>

                      <div className={styles.checkItem}>
                        <span className={styles.checkIcon}>{getCheckIcon(verificationResult.checks.isHexFormat)}</span>
                        <span className={styles.checkLabel}>Valid Format</span>
                        <span className={styles.checkValue}>{verificationResult.checks.isHexFormat ? 'Hex' : 'Invalid'}</span>
                      </div>

                      <div className={styles.checkItem}>
                        <span className={styles.checkIcon}>{getCheckIcon(verificationResult.checks.isHttps)}</span>
                        <span className={styles.checkLabel}>HTTPS</span>
                        <span className={styles.checkValue}>{verificationResult.checks.currentProtocol}</span>
                      </div>

                      <div className={styles.checkItem}>
                        <span className={styles.checkIcon}>{getCheckIcon(verificationResult.checks.apiContentMatches)}</span>
                        <span className={styles.checkLabel}>Content Match</span>
                        <span className={styles.checkValue}>{verificationResult.checks.apiContentMatches ? 'Yes' : 'No'}</span>
                      </div>

                      <div className={styles.checkItem}>
                        <span className={styles.checkIcon}>{getCheckIcon(verificationResult.checks.hasExpectedLength)}</span>
                        <span className={styles.checkLabel}>File Size</span>
                        <span className={styles.checkValue}>{verificationResult.checks.fileSize || 0} chars</span>
                      </div>
                    </div>

                    {verificationResult.recommendations?.length > 0 && (
                      <div className={styles.recommendations}>
                        <h4>Recommendations</h4>
                        <ul>
                          {verificationResult.recommendations.map((rec, index) => (
                            <li key={index}>{rec}</li>
                          ))}
                        </ul>
                      </div>
                    )}

                    <div className={styles.technicalDetails}>
                      <h4>Technical Details</h4>
                      <div className={styles.detailsGrid}>
                        <div className={styles.detailItem}>
                          <span className={styles.detailLabel}>Expected URL:</span>
                          <span className={styles.detailValue}>
                            <a 
                              href={verificationResult.checks.expectedUrl} 
                              target="_blank" 
                              rel="noopener noreferrer"
                            >
                              {verificationResult.checks.expectedUrl}
                            </a>
                          </span>
                        </div>
                        <div className={styles.detailItem}>
                          <span className={styles.detailLabel}>Content Type:</span>
                          <span className={styles.detailValue}>{verificationResult.checks.apiContentType || 'N/A'}</span>
                        </div>
                        <div className={styles.detailItem}>
                          <span className={styles.detailLabel}>File Preview:</span>
                          <span className={styles.detailValue} style={{ fontFamily: 'monospace', fontSize: '0.8rem' }}>
                            {verificationResult.checks.fileContent || 'No content'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Next Steps */}
              <div className={styles.settingsSection}>
                <h2>Next Steps</h2>
                <div className={styles.nextStepsList}>
                  <p>After successful verification:</p>
                  <ol>
                    <li>Apple Pay will be available in your Square Web Payments SDK integration</li>
                    <li>Customers can use Apple Pay on supported devices (iPhone, iPad, Mac with Touch ID)</li>
                    <li>Apple Pay payments will appear in your Square Dashboard alongside other payment methods</li>
                    <li>Test Apple Pay payments in your POS Terminal to ensure everything works correctly</li>
                  </ol>
                </div>
              </div>
            </div>
          </div>
        </AdminLayout>
      </ProtectedRoute>
    </AuthenticationGuard>
  )
}
