{"numFailedTestSuites": 1, "numFailedTests": 0, "numPassedTestSuites": 3, "numPassedTests": 21, "numPendingTestSuites": 0, "numPendingTests": 0, "numRuntimeErrorTestSuites": 1, "numTodoTests": 0, "numTotalTestSuites": 4, "numTotalTests": 21, "openHandles": [], "snapshot": {"added": 0, "didUpdate": false, "failure": false, "filesAdded": 0, "filesRemoved": 0, "filesRemovedList": [], "filesUnmatched": 0, "filesUpdated": 0, "matched": 0, "total": 0, "unchecked": 0, "uncheckedKeysByFile": [], "unmatched": 0, "updated": 0}, "startTime": 1749289989922, "success": false, "testResults": [{"assertionResults": [{"ancestorTitles": ["QuickEventServiceSelector"], "duration": 216, "failureDetails": [], "failureMessages": [], "fullName": "QuickEventServiceSelector renders categories initially", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "renders categories initially"}, {"ancestorTitles": ["QuickEventServiceSelector"], "duration": 118, "failureDetails": [], "failureMessages": [], "fullName": "QuickEventServiceSelector renders services and tiers after selecting a category", "invocations": 1, "location": null, "numPassingAsserts": 5, "retryReasons": [], "status": "passed", "title": "renders services and tiers after selecting a category"}, {"ancestorTitles": ["QuickEventServiceSelector"], "duration": 68, "failureDetails": [], "failureMessages": [], "fullName": "QuickEventServiceSelector calls onAddToCart when a service tier is clicked", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "calls onAddToCart when a service tier is clicked"}, {"ancestorTitles": ["QuickEventServiceSelector", "Cart Display and Interaction"], "duration": 83, "failureDetails": [], "failureMessages": [], "fullName": "QuickEventServiceSelector Cart Display and Interaction renders cart items and total amount correctly", "invocations": 1, "location": null, "numPassingAsserts": 7, "retryReasons": [], "status": "passed", "title": "renders cart items and total amount correctly"}, {"ancestorTitles": ["QuickEventServiceSelector", "Cart Display and Interaction"], "duration": 60, "failureDetails": [], "failureMessages": [], "fullName": "QuickEventServiceSelector Cart Display and Interaction calls onRemoveFromCart when \"Remove\" button is clicked", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "calls onRemoveFromCart when \"Remove\" button is clicked"}, {"ancestorTitles": ["QuickEventServiceSelector", "Cart Display and Interaction"], "duration": 63, "failureDetails": [], "failureMessages": [], "fullName": "QuickEventServiceSelector Cart Display and Interaction \"Proceed to Checkout\" button is enabled and calls onProceedToCheckout when cart has items", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "\"Proceed to Checkout\" button is enabled and calls onProceedToCheckout when cart has items"}, {"ancestorTitles": ["QuickEventServiceSelector", "Cart Display and Interaction"], "duration": 45, "failureDetails": [], "failureMessages": [], "fullName": "QuickEventServiceSelector Cart Display and Interaction \"Proceed to Checkout\" button is disabled when cart is empty", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "\"Proceed to Checkout\" button is disabled when cart is empty"}, {"ancestorTitles": ["QuickEventServiceSelector"], "duration": 11, "failureDetails": [], "failureMessages": [], "fullName": "QuickEventServiceSelector handles empty services array gracefully", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "handles empty services array gracefully"}], "endTime": 1749289992770, "message": "", "name": "/app/__tests__/components/admin/pos/QuickEventServiceSelector.test.js", "startTime": 1749289990623, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["QuickEventPayment"], "duration": 163, "failureDetails": [], "failureMessages": [], "fullName": "QuickEventPayment renders correctly with cart items and calculates total amount", "invocations": 1, "location": null, "numPassingAsserts": 6, "retryReasons": [], "status": "passed", "title": "renders correctly with cart items and calculates total amount"}, {"ancestorTitles": ["QuickEventPayment"], "duration": 27, "failureDetails": [], "failureMessages": [], "fullName": "QuickEventPayment handles empty cart gracefully", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "handles empty cart gracefully"}, {"ancestorTitles": ["QuickEventPayment"], "duration": 78, "failureDetails": [], "failureMessages": [], "fullName": "QuickEventPayment processes cash payment and calls createQuickEventTransaction with correct payload", "invocations": 1, "location": null, "numPassingAsserts": 3, "retryReasons": [], "status": "passed", "title": "processes cash payment and calls createQuickEventTransaction with correct payload"}, {"ancestorTitles": ["QuickEventPayment"], "duration": 44, "failureDetails": [], "failureMessages": [], "fullName": "QuickEventPayment processes card payment via POSSquarePaymentNew and calls createQuickEventTransaction", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "processes card payment via POSSquarePaymentNew and calls createQuickEventTransaction"}, {"ancestorTitles": ["QuickEventPayment"], "duration": 78, "failureDetails": [], "failureMessages": [], "fullName": "QuickEventPayment displays error message if createQuickEventTransaction fails", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "displays error message if createQuickEventTransaction fails"}, {"ancestorTitles": ["QuickEventPayment"], "duration": 14, "failureDetails": [], "failureMessages": [], "fullName": "QuickEventPayment calls onBack when back button is clicked", "invocations": 1, "location": null, "numPassingAsserts": 1, "retryReasons": [], "status": "passed", "title": "calls onBack when back button is clicked"}], "endTime": 1749289994400, "message": "", "name": "/app/__tests__/components/admin/pos/QuickEventPayment.test.js", "startTime": 1749289993034, "status": "passed", "summary": ""}, {"assertionResults": [{"ancestorTitles": ["POSTerminal Cart Logic (State Handlers)"], "duration": 43, "failureDetails": [], "failureMessages": [], "fullName": "POSTerminal Cart Logic (State Handlers) handleQuickServiceSelect should add item to quickEventCart", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "handleQuickServiceSelect should add item to quickEventCart"}, {"ancestorTitles": ["POSTerminal Cart Logic (State Handlers)"], "duration": 12, "failureDetails": [], "failureMessages": [], "fullName": "POSTerminal Cart Logic (State Handlers) handleRemoveFromCart should remove the correct item from quickEventCart", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "handleRemoveFromCart should remove the correct item from quickEventCart"}, {"ancestorTitles": ["POSTerminal Cart Logic (State Handlers)"], "duration": 13, "failureDetails": [], "failureMessages": [], "fullName": "POSTerminal Cart Logic (State Handlers) handleProceedToCheckout should change quickEventStep to \"payment\"", "invocations": 1, "location": null, "numPassingAsserts": 2, "retryReasons": [], "status": "passed", "title": "handleProceedToCheckout should change quickEventStep to \"payment\""}, {"ancestorTitles": ["POSTerminal Cart Logic (State Handlers)"], "duration": 10, "failureDetails": [], "failureMessages": [], "fullName": "POSTerminal Cart Logic (State Handlers) handleQuickEventBack (from payment step) should clear quickEventCart and reset step", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "handleQuickEventBack (from payment step) should clear quickEventCart and reset step"}, {"ancestorTitles": ["POSTerminal Cart Logic (State Handlers)"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "POSTerminal Cart Logic (State Handlers) handleQuickEventBack (not from payment step) should not clear cart", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "handleQuickEventBack (not from payment step) should not clear cart"}, {"ancestorTitles": ["POSTerminal Cart Logic (State Handlers)"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "POSTerminal Cart Logic (State Handlers) handleTransactionComplete (for \"quick\" mode) should clear quickEventCart and reset step", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "handleTransactionComplete (for \"quick\" mode) should clear quickEventCart and reset step"}, {"ancestorTitles": ["POSTerminal Cart Logic (State Handlers)"], "duration": 9, "failureDetails": [], "failureMessages": [], "fullName": "POSTerminal Cart Logic (State Handlers) handleTransactionComplete (for \"full\" mode) should not clear quickEventCart or change quickEventStep", "invocations": 1, "location": null, "numPassingAsserts": 4, "retryReasons": [], "status": "passed", "title": "handleTransactionComplete (for \"full\" mode) should not clear quickEventCart or change quickEventStep"}], "endTime": 1749289995424, "message": "", "name": "/app/__tests__/pages/admin/pos/index.test.js", "startTime": 1749289994451, "status": "passed", "summary": ""}, {"assertionResults": [], "coverage": {}, "endTime": 1749289996580, "message": "  ● Test suite failed to run\n\n    TypeError: util.inherits is not a function\n\n      11 | // If node-mocks-http still fails, the issue is likely deeper in the Jest/node-mocks-http interaction.\n      12 |\n    > 13 | import { createMocks } from 'node-mocks-http'\n         | ^\n      14 | import handler from '@/pages/api/admin/pos/create-quick-event'\n      15 | import { supabase } from '@/lib/supabase' // Actual path to supabase client\n      16 | import { authenticateAdminRequest } from '@/lib/auth-utils' // Actual path\n\n      at Object.<anonymous> (node_modules/node-mocks-http/lib/node/_http_incoming.js:38:6)\n      at Object.<anonymous> (node_modules/node-mocks-http/lib/node/http.js:3:27)\n      at Object.<anonymous> (node_modules/node-mocks-http/lib/mockResponse.js:31:14)\n      at Object.<anonymous> (node_modules/node-mocks-http/lib/http-mock.js:9:18)\n      at Object.require (__tests__/api/admin/pos/create-quick-event.test.js:13:1)\n", "name": "/app/__tests__/api/admin/pos/create-quick-event.test.js", "startTime": 1749289996580, "status": "failed", "summary": ""}], "wasInterrupted": false}