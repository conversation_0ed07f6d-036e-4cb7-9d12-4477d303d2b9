<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Service Synchronization Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; color: #155724; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .warning { background-color: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .visibility-flags { display: flex; gap: 10px; }
        .flag { padding: 2px 6px; border-radius: 3px; font-size: 12px; }
        .flag.true { background-color: #28a745; color: white; }
        .flag.false { background-color: #dc3545; color: white; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Ocean Soul Sparkles - Service Synchronization Test</h1>
    <p>This test verifies that services configured in admin inventory appear correctly in all booking interfaces.</p>

    <div class="test-section">
        <h2>Test Controls</h2>
        <button onclick="runAllTests()">Run All Tests</button>
        <button onclick="clearResults()">Clear Results</button>
    </div>

    <div id="results"></div>

    <script>
        let testResults = [];

        function addResult(message, type = 'info') {
            testResults.push({ message, type, timestamp: new Date().toISOString() });
            updateDisplay();
        }

        function updateDisplay() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = testResults.map(result => 
                `<div class="test-section ${result.type}">
                    <strong>[${new Date(result.timestamp).toLocaleTimeString()}]</strong> ${result.message}
                </div>`
            ).join('');
        }

        function clearResults() {
            testResults = [];
            updateDisplay();
        }

        async function runAllTests() {
            clearResults();
            addResult('🚀 Starting comprehensive service synchronization test...', 'info');

            try {
                // Test 1: Admin Services API
                await testAdminServicesAPI();
                
                // Test 2: POS Services API
                await testPOSServicesAPI();
                
                // Test 3: Public Services API
                await testPublicServicesAPI();

                // Test 4: Events Services API
                await testEventsServicesAPI();

                // Test 5: Cross-Interface Comparison
                await testCrossInterfaceSync();
                
                addResult('✅ All tests completed successfully!', 'success');
            } catch (error) {
                addResult(`❌ Test suite failed: ${error.message}`, 'error');
            }
        }

        async function testAdminServicesAPI() {
            addResult('🔍 Testing Admin Services API...', 'info');
            
            try {
                const response = await fetch('/api/admin/services');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const services = data.services || [];
                
                addResult(`✅ Admin API: Found ${services.length} total services`, 'success');
                
                // Analyze visibility flags
                const visibilityStats = {
                    public: services.filter(s => s.visible_on_public === true).length,
                    pos: services.filter(s => s.visible_on_pos === true).length,
                    events: services.filter(s => s.visible_on_events === true).length
                };
                
                addResult(`📊 Visibility Stats - Public: ${visibilityStats.public}, POS: ${visibilityStats.pos}, Events: ${visibilityStats.events}`, 'info');
                
                // Display services table
                displayServicesTable(services, 'admin-services', 'Admin Services');
                
                return services;
            } catch (error) {
                addResult(`❌ Admin API Error: ${error.message}`, 'error');
                throw error;
            }
        }

        async function testPOSServicesAPI() {
            addResult('🔍 Testing POS Services API...', 'info');
            
            try {
                const response = await fetch('/api/admin/pos/services-with-artists');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const services = data.services || [];
                
                addResult(`✅ POS API: Found ${services.length} services with artists`, 'success');
                
                // Check visibility flags
                const posVisibleCount = services.filter(s => s.visible_on_pos === true).length;
                const eventsVisibleCount = services.filter(s => s.visible_on_events === true).length;
                
                addResult(`📊 POS Services - POS Visible: ${posVisibleCount}, Events Visible: ${eventsVisibleCount}`, 'info');
                
                // Display services table
                displayServicesTable(services, 'pos-services', 'POS Services');
                
                return services;
            } catch (error) {
                addResult(`❌ POS API Error: ${error.message}`, 'error');
                throw error;
            }
        }

        async function testPublicServicesAPI() {
            addResult('🔍 Testing Public Services API...', 'info');
            
            try {
                const response = await fetch('/api/public/services');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                const services = data.services || [];
                
                addResult(`✅ Public API: Found ${services.length} public services`, 'success');
                
                // Display services table (different format for public API)
                displayPublicServicesTable(services, 'public-services', 'Public Services');
                
                return services;
            } catch (error) {
                addResult(`❌ Public API Error: ${error.message}`, 'error');
                throw error;
            }
        }

        async function testEventsServicesAPI() {
            addResult('🔍 Testing Events Services API...', 'info');

            try {
                const response = await fetch('/api/public/events-services');

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                const services = data.services || [];

                addResult(`✅ Events API: Found ${services.length} event services`, 'success');

                // Display services table (different format for events API)
                displayPublicServicesTable(services, 'events-services', 'Events Services');

                return services;
            } catch (error) {
                addResult(`❌ Events API Error: ${error.message}`, 'error');
                throw error;
            }
        }

        async function testCrossInterfaceSync() {
            addResult('🔍 Testing cross-interface synchronization...', 'info');

            try {
                const [adminServices, posServices, publicServices, eventsServices] = await Promise.all([
                    testAdminServicesAPI(),
                    testPOSServicesAPI(),
                    testPublicServicesAPI(),
                    testEventsServicesAPI()
                ]);
                
                // Compare service counts
                const adminPosVisible = adminServices.filter(s => s.visible_on_pos === true).length;
                const adminEventsVisible = adminServices.filter(s => s.visible_on_events === true).length;
                const adminPublicVisible = adminServices.filter(s => s.visible_on_public === true).length;

                addResult(`🔄 Sync Check - Admin POS Visible: ${adminPosVisible}, POS API: ${posServices.length}`,
                    adminPosVisible === posServices.length ? 'success' : 'warning');

                addResult(`🔄 Sync Check - Admin Public Visible: ${adminPublicVisible}, Public API: ${publicServices.length}`,
                    adminPublicVisible === publicServices.length ? 'success' : 'warning');

                addResult(`🔄 Sync Check - Admin Events Visible: ${adminEventsVisible}, Events API: ${eventsServices.length}`,
                    adminEventsVisible === eventsServices.length ? 'success' : 'warning');
                
                // Check for specific services
                const testService = adminServices.find(s => s.name.includes('Face Painting'));
                if (testService) {
                    const inPOS = posServices.some(s => s.id === testService.id);
                    const inPublic = publicServices.some(s => s.id === testService.id);
                    const inEvents = eventsServices.some(s => s.id === testService.id);

                    addResult(`🎨 Test Service "${testService.name}" - POS: ${testService.visible_on_pos ? '✅' : '❌'} (${inPOS ? 'Found' : 'Missing'}), Public: ${testService.visible_on_public ? '✅' : '❌'} (${inPublic ? 'Found' : 'Missing'}), Events: ${testService.visible_on_events ? '✅' : '❌'} (${inEvents ? 'Found' : 'Missing'})`, 'info');
                }
                
            } catch (error) {
                addResult(`❌ Sync Test Error: ${error.message}`, 'error');
                throw error;
            }
        }

        function displayServicesTable(services, containerId, title) {
            const tableHTML = `
                <div class="test-section">
                    <h3>${title}</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Category</th>
                                <th>Status</th>
                                <th>Visibility Flags</th>
                                <th>Artists</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${services.map(service => `
                                <tr>
                                    <td>${service.name || 'N/A'}</td>
                                    <td>${service.category || 'N/A'}</td>
                                    <td>${service.status || 'N/A'}</td>
                                    <td>
                                        <div class="visibility-flags">
                                            <span class="flag ${service.visible_on_public}">Public: ${service.visible_on_public}</span>
                                            <span class="flag ${service.visible_on_pos}">POS: ${service.visible_on_pos}</span>
                                            <span class="flag ${service.visible_on_events}">Events: ${service.visible_on_events}</span>
                                        </div>
                                    </td>
                                    <td>${service.availableArtistCount || 'N/A'}</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
            
            document.getElementById('results').insertAdjacentHTML('beforeend', tableHTML);
        }

        function displayPublicServicesTable(services, containerId, title) {
            const tableHTML = `
                <div class="test-section">
                    <h3>${title}</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>Title</th>
                                <th>Category</th>
                                <th>Featured</th>
                                <th>Pricing Tiers</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${services.map(service => `
                                <tr>
                                    <td>${service.title || 'N/A'}</td>
                                    <td>${service.category || 'N/A'}</td>
                                    <td>${service.featured ? '⭐' : ''}</td>
                                    <td>${service.pricingTiers?.length || 0} tiers</td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
            
            document.getElementById('results').insertAdjacentHTML('beforeend', tableHTML);
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            addResult('🌊 Ocean Soul Sparkles Service Synchronization Test Ready', 'info');
            addResult('Click "Run All Tests" to verify service synchronization across all interfaces', 'info');
        });
    </script>
</body>
</html>
