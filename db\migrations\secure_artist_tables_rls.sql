-- Enable RLS and define policies for artist_profiles
ALTER TABLE public.artist_profiles ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Admin can read artist_profiles" ON public.artist_profiles
  FOR SELECT USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users au WHERE au.id = (select auth.uid()) AND au.role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Admin can insert into artist_profiles" ON public.artist_profiles
  FOR INSERT WITH CHECK (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users au WHERE au.id = (select auth.uid()) AND au.role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Admin can update artist_profiles" ON public.artist_profiles
  FOR UPDATE USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users au WHERE au.id = (select auth.uid()) AND au.role IN ('admin', 'staff')
    )
  ) WITH CHECK (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users au WHERE au.id = (select auth.uid()) AND au.role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Admin can delete from artist_profiles" ON public.artist_profiles
  FOR DELETE USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users au WHERE au.id = (select auth.uid()) AND au.role IN ('admin', 'staff')
    )
  );

-- Enable RLS and define policies for artist_availability_schedule
ALTER TABLE public.artist_availability_schedule ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Admin can read artist_availability_schedule" ON public.artist_availability_schedule
  FOR SELECT USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users au WHERE au.id = (select auth.uid()) AND au.role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Admin can insert into artist_availability_schedule" ON public.artist_availability_schedule
  FOR INSERT WITH CHECK (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users au WHERE au.id = (select auth.uid()) AND au.role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Admin can update artist_availability_schedule" ON public.artist_availability_schedule
  FOR UPDATE USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users au WHERE au.id = (select auth.uid()) AND au.role IN ('admin', 'staff')
    )
  ) WITH CHECK (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users au WHERE au.id = (select auth.uid()) AND au.role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Admin can delete from artist_availability_schedule" ON public.artist_availability_schedule
  FOR DELETE USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users au WHERE au.id = (select auth.uid()) AND au.role IN ('admin', 'staff')
    )
  );

-- Enable RLS and define policies for artist_availability_exceptions
ALTER TABLE public.artist_availability_exceptions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Admin can read artist_availability_exceptions" ON public.artist_availability_exceptions
  FOR SELECT USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users au WHERE au.id = (select auth.uid()) AND au.role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Admin can insert into artist_availability_exceptions" ON public.artist_availability_exceptions
  FOR INSERT WITH CHECK (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users au WHERE au.id = (select auth.uid()) AND au.role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Admin can update artist_availability_exceptions" ON public.artist_availability_exceptions
  FOR UPDATE USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users au WHERE au.id = (select auth.uid()) AND au.role IN ('admin', 'staff')
    )
  ) WITH CHECK (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users au WHERE au.id = (select auth.uid()) AND au.role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Admin can delete from artist_availability_exceptions" ON public.artist_availability_exceptions
  FOR DELETE USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users au WHERE au.id = (select auth.uid()) AND au.role IN ('admin', 'staff')
    )
  );

-- Enable RLS and define policies for service_pricing_tiers
ALTER TABLE public.service_pricing_tiers ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Admin can read service_pricing_tiers" ON public.service_pricing_tiers
  FOR SELECT USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users au WHERE au.id = (select auth.uid()) AND au.role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Admin can insert into service_pricing_tiers" ON public.service_pricing_tiers
  FOR INSERT WITH CHECK (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users au WHERE au.id = (select auth.uid()) AND au.role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Admin can update service_pricing_tiers" ON public.service_pricing_tiers
  FOR UPDATE USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users au WHERE au.id = (select auth.uid()) AND au.role IN ('admin', 'staff')
    )
  ) WITH CHECK (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users au WHERE au.id = (select auth.uid()) AND au.role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Admin can delete from service_pricing_tiers" ON public.service_pricing_tiers
  FOR DELETE USING (
    (select auth.role()) IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users au WHERE au.id = (select auth.uid()) AND au.role IN ('admin', 'staff')
    )
  );

-- RLS policies for artist-related tables have been secured
