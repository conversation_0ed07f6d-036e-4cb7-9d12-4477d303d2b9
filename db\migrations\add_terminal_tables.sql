-- =============================================
-- SQUARE TERMINAL INTEGRATION TABLES
-- =============================================

-- Create terminal_checkouts table for tracking Square Terminal checkout requests
CREATE TABLE IF NOT EXISTS public.terminal_checkouts (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  checkout_id TEXT UNIQUE NOT NULL,
  device_id TEXT,
  amount BIGINT NOT NULL, -- Amount in cents
  currency TEXT NOT NULL DEFAULT 'AUD',
  status TEXT NOT NULL CHECK (status IN ('PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELED', 'ERROR')),
  payment_id TEXT,
  payment_note TEXT,
  order_id TEXT,
  cancel_reason TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create terminal_refunds table for tracking Square Terminal refund requests
CREATE TABLE IF NOT EXISTS public.terminal_refunds (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  refund_id TEXT UNIQUE NOT NULL,
  payment_id TEXT NOT NULL,
  device_id TEXT,
  amount BIGINT NOT NULL, -- Amount in cents
  currency TEXT NOT NULL DEFAULT 'AUD',
  status TEXT NOT NULL CHECK (status IN ('PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELED', 'ERROR')),
  reason TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create terminal_checkout_updates table for real-time updates
CREATE TABLE IF NOT EXISTS public.terminal_checkout_updates (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  checkout_id TEXT NOT NULL,
  status TEXT NOT NULL,
  payment_id TEXT,
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_terminal_checkouts_checkout_id ON public.terminal_checkouts(checkout_id);
CREATE INDEX IF NOT EXISTS idx_terminal_checkouts_device_id ON public.terminal_checkouts(device_id);
CREATE INDEX IF NOT EXISTS idx_terminal_checkouts_status ON public.terminal_checkouts(status);
CREATE INDEX IF NOT EXISTS idx_terminal_checkouts_payment_id ON public.terminal_checkouts(payment_id);
CREATE INDEX IF NOT EXISTS idx_terminal_checkouts_created_at ON public.terminal_checkouts(created_at);

CREATE INDEX IF NOT EXISTS idx_terminal_refunds_refund_id ON public.terminal_refunds(refund_id);
CREATE INDEX IF NOT EXISTS idx_terminal_refunds_payment_id ON public.terminal_refunds(payment_id);
CREATE INDEX IF NOT EXISTS idx_terminal_refunds_device_id ON public.terminal_refunds(device_id);
CREATE INDEX IF NOT EXISTS idx_terminal_refunds_status ON public.terminal_refunds(status);

CREATE INDEX IF NOT EXISTS idx_terminal_checkout_updates_checkout_id ON public.terminal_checkout_updates(checkout_id);
CREATE INDEX IF NOT EXISTS idx_terminal_checkout_updates_updated_at ON public.terminal_checkout_updates(updated_at);

-- Add RLS policies for security
ALTER TABLE public.terminal_checkouts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.terminal_refunds ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.terminal_checkout_updates ENABLE ROW LEVEL SECURITY;

-- Policy for terminal_checkouts - only authenticated users can access
CREATE POLICY "terminal_checkouts_authenticated_access" ON public.terminal_checkouts
  FOR ALL USING (auth.role() = 'authenticated');

-- Policy for terminal_refunds - only authenticated users can access
CREATE POLICY "terminal_refunds_authenticated_access" ON public.terminal_refunds
  FOR ALL USING (auth.role() = 'authenticated');

-- Policy for terminal_checkout_updates - only authenticated users can access
CREATE POLICY "terminal_checkout_updates_authenticated_access" ON public.terminal_checkout_updates
  FOR ALL USING (auth.role() = 'authenticated');

-- =============================================
-- ENHANCE EXISTING TABLES FOR TERMINAL SUPPORT
-- =============================================

-- Add terminal-specific fields to payments table if they don't exist
ALTER TABLE public.payments 
ADD COLUMN IF NOT EXISTS terminal_checkout_id TEXT,
ADD COLUMN IF NOT EXISTS terminal_device_id TEXT;

-- Add indexes for new terminal fields
CREATE INDEX IF NOT EXISTS idx_payments_terminal_checkout_id ON public.payments(terminal_checkout_id);
CREATE INDEX IF NOT EXISTS idx_payments_terminal_device_id ON public.payments(terminal_device_id);

-- =============================================
-- FUNCTIONS AND TRIGGERS
-- =============================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updating updated_at timestamps
DROP TRIGGER IF EXISTS update_terminal_checkouts_updated_at ON public.terminal_checkouts;
CREATE TRIGGER update_terminal_checkouts_updated_at
    BEFORE UPDATE ON public.terminal_checkouts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_terminal_refunds_updated_at ON public.terminal_refunds;
CREATE TRIGGER update_terminal_refunds_updated_at
    BEFORE UPDATE ON public.terminal_refunds
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- REALTIME SUBSCRIPTIONS
-- =============================================

-- Enable realtime for terminal checkout updates
ALTER PUBLICATION supabase_realtime ADD TABLE public.terminal_checkout_updates;

-- =============================================
-- SAMPLE DATA AND TESTING
-- =============================================

-- Insert sample terminal checkout for testing (sandbox only)
-- This will be removed in production
INSERT INTO public.terminal_checkouts (
  checkout_id,
  device_id,
  amount,
  currency,
  status,
  payment_note,
  order_id
) VALUES (
  'sample_checkout_' || extract(epoch from now()),
  'sample_device_123',
  5000, -- $50.00 in cents
  'AUD',
  'PENDING',
  'Sample POS Terminal Payment - Hair Braiding Service',
  'sample_order_' || extract(epoch from now())
) ON CONFLICT (checkout_id) DO NOTHING;

-- =============================================
-- CLEANUP AND MAINTENANCE
-- =============================================

-- Function to clean up old terminal checkout updates (older than 7 days)
CREATE OR REPLACE FUNCTION cleanup_old_terminal_updates()
RETURNS void AS $$
BEGIN
    DELETE FROM public.terminal_checkout_updates 
    WHERE updated_at < NOW() - INTERVAL '7 days';
END;
$$ LANGUAGE plpgsql;

-- Create a scheduled job to run cleanup (if pg_cron is available)
-- This would typically be set up separately in production
-- SELECT cron.schedule('cleanup-terminal-updates', '0 2 * * *', 'SELECT cleanup_old_terminal_updates();');

-- =============================================
-- GRANTS AND PERMISSIONS
-- =============================================

-- Grant necessary permissions to authenticated users
GRANT SELECT, INSERT, UPDATE ON public.terminal_checkouts TO authenticated;
GRANT SELECT, INSERT, UPDATE ON public.terminal_refunds TO authenticated;
GRANT SELECT, INSERT ON public.terminal_checkout_updates TO authenticated;

-- Grant usage on sequences
GRANT USAGE ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- =============================================
-- COMMENTS FOR DOCUMENTATION
-- =============================================

COMMENT ON TABLE public.terminal_checkouts IS 'Tracks Square Terminal checkout requests and their status';
COMMENT ON TABLE public.terminal_refunds IS 'Tracks Square Terminal refund requests and their status';
COMMENT ON TABLE public.terminal_checkout_updates IS 'Real-time updates for terminal checkout status changes';

COMMENT ON COLUMN public.terminal_checkouts.checkout_id IS 'Square Terminal checkout ID from Square API';
COMMENT ON COLUMN public.terminal_checkouts.device_id IS 'Square Terminal device ID that processed the checkout';
COMMENT ON COLUMN public.terminal_checkouts.amount IS 'Payment amount in cents (e.g., 5000 = $50.00)';
COMMENT ON COLUMN public.terminal_checkouts.payment_id IS 'Square payment ID when checkout is completed';

COMMENT ON COLUMN public.terminal_refunds.refund_id IS 'Square Terminal refund ID from Square API';
COMMENT ON COLUMN public.terminal_refunds.payment_id IS 'Original Square payment ID being refunded';

-- =============================================
-- VERIFICATION QUERIES
-- =============================================

-- Verify tables were created successfully
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'terminal_checkouts') THEN
        RAISE NOTICE 'SUCCESS: terminal_checkouts table created';
    ELSE
        RAISE EXCEPTION 'FAILED: terminal_checkouts table not created';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'terminal_refunds') THEN
        RAISE NOTICE 'SUCCESS: terminal_refunds table created';
    ELSE
        RAISE EXCEPTION 'FAILED: terminal_refunds table not created';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'terminal_checkout_updates') THEN
        RAISE NOTICE 'SUCCESS: terminal_checkout_updates table created';
    ELSE
        RAISE EXCEPTION 'FAILED: terminal_checkout_updates table not created';
    END IF;
END $$;
