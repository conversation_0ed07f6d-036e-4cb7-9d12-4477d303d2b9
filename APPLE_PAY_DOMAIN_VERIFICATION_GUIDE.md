# Apple Pay Domain Verification Guide for Ocean Soul Sparkles

## 🍎 **Complete Apple Pay Setup Process**

This guide walks you through configuring Apple Pay domain verification for your Square Web Payments SDK integration, enabling Apple Pay as a payment method in your POS Terminal system.

---

## 📋 **Prerequisites**

### **Required Components**
- ✅ Square Developer Account with Web Payments SDK enabled
- ✅ HTTPS-enabled website (Apple Pay requires SSL)
- ✅ Existing Square Web Payments SDK integration
- ✅ Admin access to Ocean Soul Sparkles website

### **Apple Pay Requirements**
- ✅ Valid SSL certificate on production domain
- ✅ Domain association file properly configured
- ✅ Square merchant account with Apple Pay enabled

---

## 🚀 **Step-by-Step Setup Process**

### **Step 1: Access Square Developer Console**

1. **Navigate to Square Developer Console**
   - Go to: https://developer.squareup.com/apps
   - Sign in with your Square account
   - Select your "OceanSoulSparkles" application

2. **Access Apple Pay Settings**
   - In the left sidebar, click **"Apple Pay"**
   - Click on **"Web"** tab
   - You should see the "Add domain" dialog

### **Step 2: Download Domain Association File**

1. **In the Square Developer Console dialog:**
   - Click **"Download verification file"**
   - Save the file (it will be named `apple-developer-merchantid-domain-association`)
   - **Important**: This file contains your unique merchant identifier

2. **File Details:**
   - The file contains hex-encoded data
   - It's typically 60-80 characters long
   - It's unique to your Square merchant account

### **Step 3: Replace the Placeholder File**

1. **Locate the Placeholder File:**
   ```
   public/.well-known/apple-developer-merchantid-domain-association
   ```

2. **Replace with Downloaded File:**
   - Delete the existing placeholder file
   - Copy the downloaded file to the exact same location
   - Ensure the filename matches exactly (no extension)

3. **Verify File Content:**
   - The file should contain only hex characters (0-9, A-F)
   - No extra spaces or line breaks
   - Example format: `7B227073704964223A2239374643304139433836304634363939220A7D`

### **Step 4: Deploy to Production**

1. **Commit Changes:**
   ```bash
   git add public/.well-known/apple-developer-merchantid-domain-association
   git commit -m "Add Apple Pay domain association file"
   git push origin main
   ```

2. **Deploy to Production:**
   - Deploy your changes to the production website
   - Ensure HTTPS is enabled and working

### **Step 5: Verify Domain Association**

1. **Test the URL Manually:**
   - Visit: https://www.oceansoulsparkles.com.au/.well-known/apple-developer-merchantid-domain-association
   - You should see the hex-encoded content
   - The response should have `Content-Type: text/plain`

2. **Use the Admin Verification Tool:**
   - Go to: https://www.oceansoulsparkles.com.au/admin/apple-pay-setup
   - Click "Run Verification" to check all requirements
   - Ensure all checks pass before proceeding

### **Step 6: Complete Square Verification**

1. **Return to Square Developer Console:**
   - In the "Add domain" dialog
   - Ensure the URL shows: `https://www.oceansoulsparkles.com.au/.well-known/apple-developer-merchantid-domain-association`
   - Click **"Verify"** button

2. **Verification Success:**
   - Square will check your domain association file
   - You should see a success message
   - The domain will be added to your approved domains list

---

## 🔧 **Technical Implementation Details**

### **Next.js Configuration**

The following components have been configured for Apple Pay support:

#### **1. Domain Association API Route**
```javascript
// pages/api/apple-pay/domain-association.js
// Serves the domain association file with proper headers
```

#### **2. Next.js Rewrites**
```javascript
// next.config.js
async rewrites() {
  return [
    {
      source: '/.well-known/apple-developer-merchantid-domain-association',
      destination: '/api/apple-pay/domain-association',
    },
  ]
}
```

#### **3. HTTP Headers**
```javascript
// Proper headers for Apple Pay domain verification
'Content-Type': 'text/plain'
'Cache-Control': 'public, max-age=86400'
'Access-Control-Allow-Origin': '*'
```

### **File Structure**
```
project-root/
├── public/
│   └── .well-known/
│       └── apple-developer-merchantid-domain-association
├── pages/
│   ├── api/
│   │   └── apple-pay/
│   │       ├── domain-association.js
│   │       └── verify-domain.js
│   └── admin/
│       └── apple-pay-setup.js
└── next.config.js (updated)
```

---

## 🧪 **Testing and Verification**

### **Automated Verification Tool**

Access the admin verification tool at:
```
https://www.oceansoulsparkles.com.au/admin/apple-pay-setup
```

**Verification Checks:**
- ✅ File exists locally
- ✅ API endpoint accessible
- ✅ Valid hex format
- ✅ HTTPS enabled
- ✅ Content matches between file and API
- ✅ Proper file size

### **Manual Testing**

1. **Direct URL Test:**
   ```bash
   curl -I https://www.oceansoulsparkles.com.au/.well-known/apple-developer-merchantid-domain-association
   ```

2. **Expected Response:**
   ```
   HTTP/2 200
   content-type: text/plain
   cache-control: public, max-age=86400
   ```

3. **Content Verification:**
   ```bash
   curl https://www.oceansoulsparkles.com.au/.well-known/apple-developer-merchantid-domain-association
   ```

### **Common Issues and Solutions**

#### **Issue: 404 Not Found**
- ✅ Verify file exists in `public/.well-known/`
- ✅ Check Next.js rewrite configuration
- ✅ Ensure deployment includes the file

#### **Issue: Wrong Content-Type**
- ✅ Verify API route sets `Content-Type: text/plain`
- ✅ Check Next.js headers configuration

#### **Issue: HTTPS Required**
- ✅ Ensure SSL certificate is valid
- ✅ Verify HTTPS redirects are working
- ✅ Test with production domain only

#### **Issue: Square Verification Fails**
- ✅ Verify exact URL accessibility
- ✅ Check file content matches downloaded file
- ✅ Ensure no extra characters or formatting

---

## 🎯 **Integration with Existing POS System**

### **Square Web Payments SDK Enhancement**

Once Apple Pay domain verification is complete:

1. **Apple Pay Button**: Will automatically appear on supported devices
2. **Payment Processing**: Handled through existing Square Web SDK integration
3. **POS Integration**: Apple Pay payments integrate with current booking workflow
4. **Fallback Support**: Maintains existing payment method hierarchy

### **Payment Method Priority**
1. **Square Terminal** (if available and paired)
2. **Square Reader** (if Square POS app available)
3. **Apple Pay** (if supported device and domain verified)
4. **Manual Card Entry** (always available)
5. **Cash Payment** (manual recording)

---

## 📊 **Monitoring and Maintenance**

### **Regular Checks**
- ✅ Monthly verification of domain association file accessibility
- ✅ SSL certificate renewal monitoring
- ✅ Square Developer Console domain status checks

### **Troubleshooting Tools**
- ✅ Admin verification page: `/admin/apple-pay-setup`
- ✅ API verification endpoint: `/api/apple-pay/verify-domain`
- ✅ Direct file access: `/.well-known/apple-developer-merchantid-domain-association`

---

## 🎉 **Success Criteria**

### **Verification Complete When:**
- ✅ Domain association file accessible at correct URL
- ✅ Square Developer Console shows domain as verified
- ✅ Apple Pay button appears on supported devices
- ✅ Test payments process successfully
- ✅ All verification checks pass in admin tool

### **Production Ready When:**
- ✅ HTTPS properly configured
- ✅ Domain verification successful
- ✅ Apple Pay integrated with POS workflow
- ✅ Staff trained on Apple Pay acceptance
- ✅ Fallback payment methods working

---

## 🚀 **Next Steps After Verification**

1. **Test Apple Pay Payments**: Use supported devices to test the complete payment flow
2. **Staff Training**: Train staff on Apple Pay acceptance and troubleshooting
3. **Customer Communication**: Update website/signage to indicate Apple Pay acceptance
4. **Monitor Performance**: Track Apple Pay usage and success rates
5. **Regular Maintenance**: Schedule periodic verification checks

---

**Apple Pay domain verification setup is now complete and ready for production use!** 🍎

This implementation enables Ocean Soul Sparkles customers to use Apple Pay for secure, convenient payments while maintaining the existing POS Terminal workflow and payment method hierarchy.
