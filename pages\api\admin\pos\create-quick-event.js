import { getAdminClient } from '@/lib/supabase'
import { authenticateAdminRequest } from '@/lib/auth-utils'

/**
 * API endpoint for creating quick event transactions
 * POST /api/admin/pos/create-quick-event
 */
export default async function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // Verify admin authentication
    const authResult = await authenticateAdminRequest(req)
    if (!authResult.authorized) {
      return res.status(401).json({ error: authResult.error })
    }

    const { items, payment } = req.body // Changed from service, tier to items

    // Validate required fields
    if (!Array.isArray(items) || items.length === 0) {
      return res.status(400).json({ error: 'Cart items are required' })
    }

    for (const item of items) {
      if (!item.service?.id || !item.service?.name) {
        return res.status(400).json({ error: 'Service information is missing for an item in the cart' })
      }
      if (!item.tier?.id || !item.tier?.name || !item.tier?.duration || item.tier.price === undefined) {
        return res.status(400).json({ error: 'Tier information is missing for an item in the cart' })
      }
    }

    if (!payment?.method || !payment?.amount || !payment?.currency) {
      return res.status(400).json({ error: 'Payment information is required' })
    }

    // Extract details from the first item for summary fields (optional, but good for overview)
    const firstItemService = items[0].service
    const firstItemTier = items[0].tier

    console.log('🚀 Creating quick event transaction for cart:', {
      itemCount: items.length,
      firstItem: `${firstItemService.name} - ${firstItemTier.name}`,
      amount: payment.amount,
      method: payment.method
    })

    // Generate transaction ID
    const transactionId = `quick_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`

    // Get admin client for database operations (bypasses RLS)
    const supabaseAdmin = getAdminClient()

    // Create quick event record in database
    const { data: quickEventData, error: quickEventError } = await supabaseAdmin
      .from('quick_events')
      .insert([
        {
          transaction_id: transactionId,
          // Populate with first item's details or set to null if cart has multiple items / for summary
          service_id: firstItemService.id,
          service_name: firstItemService.name,
          tier_id: firstItemTier.id,
          tier_name: firstItemTier.name,
          duration: firstItemTier.duration,
          amount: payment.amount, // This is the total amount from the payment object
          currency: payment.currency,
          payment_method: payment.method,
          payment_status: 'completed', // Or based on actual payment status if available
          payment_details: payment.details || null, // e.g., Square transaction ID
          created_by: authResult.user.id,
          created_at: new Date().toISOString(),
          items_details: items, // Store the full cart items array here (JSONB)
          notes: `Quick event cart transaction with ${items.length} item(s).`
        }
      ])
      .select()

    if (quickEventError) {
      console.error('❌ Error creating quick event record:', quickEventError)
      return res.status(500).json({ error: 'Failed to create quick event record' })
    }

    // Create payment record for consistency with full bookings
    const { data: paymentData, error: paymentError } = await supabaseAdmin
      .from('payments')
      .insert([
        {
          transaction_id: transactionId,
          amount: payment.amount,
          currency: payment.currency,
          payment_method: payment.method,
          payment_status: 'completed',
          payment_date: new Date().toISOString(),
          notes: `Quick Event Cart: ${items.length} item(s), starting with ${firstItemService.name} - ${firstItemTier.name}`,
          metadata: {
            type: 'quick_event_cart', // Updated type
            itemCount: items.length,
            first_item_service_id: firstItemService.id,
            first_item_tier_id: firstItemTier.id,
          }
        }
      ])
      .select()

    if (paymentError) {
      console.error('❌ Error creating payment record:', paymentError)
      // Don't fail the request if payment record creation fails
      // The quick event record is the primary record
    }

    console.log('✅ Quick event transaction created successfully:', {
      transactionId,
      quickEventId: quickEventData[0]?.id,
      paymentId: paymentData?.[0]?.id
    })

    // Return success response
    res.status(201).json({
      success: true,
      transaction: {
        id: transactionId,
        quickEventId: quickEventData[0]?.id,
        paymentId: paymentData?.[0]?.id,
        itemCount: items.length,
        firstItemSummary: `${firstItemService.name} - ${firstItemTier.name}`,
        totalAmount: payment.amount, // Use totalAmount from payment object
        currency: payment.currency, // Use currency from payment object
        method: payment.method, // Use method from payment object
        status: 'completed',
        createdAt: quickEventData[0]?.created_at
      }
    })

  } catch (error) {
    console.error('❌ Quick event creation error:', error)
    res.status(500).json({ 
      error: 'Internal server error',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}
