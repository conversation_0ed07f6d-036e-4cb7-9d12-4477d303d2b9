import { useEffect, useState } from 'react';

const DashboardPerformanceMonitor = ({ children }) => {
  const [performanceMetrics, setPerformanceMetrics] = useState({
    authTime: 0,
    loadTime: 0,
    errorCount: 0,
    renderCount: 0
  });

  useEffect(() => {
    const startTime = performance.now();
    let renderCount = 0;
    let errorCount = 0;

    // Monitor authentication time
    const authStartTime = performance.now();
    const checkAuthComplete = () => {
      if (window.__AUTH_COMPLETE__) {
        const authTime = performance.now() - authStartTime;
        setPerformanceMetrics(prev => ({ ...prev, authTime }));
      } else {
        setTimeout(checkAuthComplete, 100);
      }
    };
    checkAuthComplete();

    // Monitor render count
    const originalConsoleLog = console.log;
    console.log = (...args) => {
      const message = args.join(' ');
      if (message.includes('[ArtistDashboard]') || message.includes('[Auth')) {
        renderCount++;
        setPerformanceMetrics(prev => ({ ...prev, renderCount }));
      }
      originalConsoleLog.apply(console, args);
    };

    // Monitor error count
    const originalConsoleError = console.error;
    console.error = (...args) => {
      const message = args.join(' ');
      // Only count non-extension errors
      if (!message.includes('chrome-extension') && 
          !message.includes('overrideMethod') && 
          !message.includes('cancelled true')) {
        errorCount++;
        setPerformanceMetrics(prev => ({ ...prev, errorCount }));
      }
      originalConsoleError.apply(console, args);
    };

    // Monitor total load time
    const loadCompleteTimer = setTimeout(() => {
      const loadTime = performance.now() - startTime;
      setPerformanceMetrics(prev => ({ ...prev, loadTime }));
    }, 2000);

    // Cleanup
    return () => {
      console.log = originalConsoleLog;
      console.error = originalConsoleError;
      clearTimeout(loadCompleteTimer);
    };
  }, []);

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return children;
  }

  return (
    <>
      {children}
      <div style={{
        position: 'fixed',
        bottom: '10px',
        right: '10px',
        background: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        padding: '10px',
        borderRadius: '8px',
        fontSize: '12px',
        fontFamily: 'monospace',
        zIndex: 9999,
        minWidth: '200px'
      }}>
        <div><strong>Dashboard Performance</strong></div>
        <div>Auth Time: {performanceMetrics.authTime.toFixed(0)}ms</div>
        <div>Load Time: {performanceMetrics.loadTime.toFixed(0)}ms</div>
        <div>Renders: {performanceMetrics.renderCount}</div>
        <div>Errors: {performanceMetrics.errorCount}</div>
        <div style={{ marginTop: '5px', fontSize: '10px', opacity: 0.7 }}>
          Target: Auth &lt;2s, Load &lt;3s, Errors = 0
        </div>
      </div>
    </>
  );
};

export default DashboardPerformanceMonitor;
