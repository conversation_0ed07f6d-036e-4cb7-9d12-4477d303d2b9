.customerList {
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header h2 {
  font-size: 1.8rem;
  color: #333;
  margin: 0;
}

.actions {
  display: flex;
  gap: 12px;
}

.addButton {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #6e8efb, #a777e3);
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.addButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.exportButton {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: transparent;
  color: #6e8efb;
  border: 1px solid #6e8efb;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.exportButton:hover {
  background-color: rgba(110, 142, 251, 0.1);
}

.exportDropdown {
  position: relative;
}

.exportOptions {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 8px 0;
  z-index: 10;
  min-width: 200px;
  display: none;
}

.exportDropdown:hover .exportOptions {
  display: block;
}

.exportOptions button {
  display: block;
  width: 100%;
  text-align: left;
  padding: 8px 16px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
  color: #333;
}

.exportOptions button:hover {
  background-color: #f5f5f5;
}

.filters {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.searchContainer {
  position: relative;
}

.searchInput {
  width: 100%;
  padding: 10px 16px 10px 40px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.searchInput:focus {
  border-color: #6e8efb;
  outline: none;
}

.searchIcon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
}

.filterControls {
  display: flex;
  gap: 16px;
  flex-wrap: nowrap;
  align-items: center;
}

.filterSelect {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 0.9rem;
  min-width: 150px;
}

.filterSelect:focus {
  border-color: #6e8efb;
  outline: none;
}

.tableContainer {
  overflow-x: auto;
  margin-bottom: 24px;
}

.customerTable {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.95rem;
}

.customerTable th {
  background-color: #f9f9f9;
  padding: 12px 16px;
  text-align: left;
  font-weight: 600;
  color: #555;
  border-bottom: 2px solid #eaeaea;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.customerTable th:hover {
  background-color: #f0f0f0;
}

.customerTable td {
  padding: 12px 16px;
  border-bottom: 1px solid #eaeaea;
  color: #333;
}

.customerTable tr:hover {
  background-color: rgba(110, 142, 251, 0.05);
}

.sortIndicator {
  display: inline-block;
  margin-left: 4px;
  color: #6e8efb;
}

.actions {
  display: flex;
  gap: 8px;
}

.viewButton {
  display: inline-block;
  padding: 6px 12px;
  background-color: #6e8efb;
  color: white;
  border-radius: 4px;
  font-size: 0.85rem;
  text-decoration: none;
  transition: all 0.2s ease;
}

.viewButton:hover {
  background-color: #5a7df9;
  transform: translateY(-1px);
}

.editButton {
  display: inline-block;
  padding: 6px 12px;
  background-color: transparent;
  color: #6e8efb;
  border: 1px solid #6e8efb;
  border-radius: 4px;
  font-size: 0.85rem;
  text-decoration: none;
  transition: all 0.2s ease;
}

.editButton:hover {
  background-color: rgba(110, 142, 251, 0.1);
  transform: translateY(-1px);
}

.marketingBadge {
  display: inline-block;
  margin-left: 8px;
  font-size: 0.8rem;
  color: #6e8efb;
}

.newBadge {
  display: inline-block;
  margin-left: 8px;
  background-color: #4caf50;
  color: white;
  font-size: 0.7rem;
  padding: 2px 6px;
  border-radius: 10px;
}

.recentBooking {
  background-color: rgba(255, 235, 59, 0.1);
}

/* Statistics Cards */
.statsContainer {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.statCard {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 20px;
  display: flex;
  align-items: center;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.statIcon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  background-color: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: #6c757d;
}

.statIcon.active {
  background-color: #d4edda;
  color: #155724;
}

.statIcon.new {
  background-color: #cce5ff;
  color: #0066cc;
}

.statIcon.bookings {
  background-color: #fff3cd;
  color: #856404;
}

.statContent {
  flex: 1;
}

.statNumber {
  font-size: 2rem;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.statLabel {
  font-size: 0.9rem;
  color: #666;
  margin-top: 4px;
}

/* Bulk Actions */
.bulkActions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
  padding: 15px 20px;
  border-radius: 6px;
  margin-bottom: 20px;
  border-left: 4px solid #6e8efb;
}

.selectedCount {
  font-weight: 500;
  color: #333;
}

.bulkActionButtons {
  position: relative;
}

.bulkButton {
  padding: 8px 16px;
  background: #6e8efb;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;
}

.bulkActionMenu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 10;
  min-width: 180px;
}

.bulkActionMenu button {
  display: block;
  width: 100%;
  padding: 10px 15px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.bulkActionMenu button:hover {
  background: #f5f5f5;
}

.bulkActionMenu button:first-child {
  border-radius: 6px 6px 0 0;
}

.bulkActionMenu button:last-child {
  border-radius: 0 0 6px 6px;
}

/* Checkbox column */
.checkboxColumn {
  width: 40px;
  cursor: default !important;
}

.checkboxColumn:hover {
  background-color: #f9f9f9 !important;
}

/* Customer name styling */
.customerName {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

/* Status and tier badges */
.statusBadge {
  display: inline-block;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;
  min-width: 60px;
}

.tierBadge {
  display: inline-block;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: bold;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.noResults {
  text-align: center;
  padding: 32px;
  color: #666;
  font-size: 1.1rem;
}

.paginationContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24px;
  padding: 16px 0;
  border-top: 1px solid #eaeaea;
}

.pageSize {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.9rem;
  color: #666;
}

.pageSizeSelect {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  font-size: 0.9rem;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.paginationButton {
  padding: 8px 16px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.paginationButton:hover:not(:disabled) {
  background-color: #e5e5e5;
}

.paginationButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pageInfo {
  color: #666;
  font-size: 0.9rem;
}

.error {
  background-color: rgba(255, 0, 0, 0.1);
  color: #d32f2f;
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 16px;
  border-left: 4px solid #d32f2f;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  font-size: 1.1rem;
  color: #666;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .customerTable th:nth-child(5),
  .customerTable td:nth-child(5) {
    display: none;
  }
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .statsContainer {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
  }

  .filterControls {
    flex-wrap: wrap;
    gap: 12px;
  }

  .bulkActions {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .tableContainer {
    overflow-x: auto;
  }

  .customerTable {
  }

  .customerTable th,
  .customerTable td {
    padding: 8px;
  }

  .customerTable th:nth-child(6),
  .customerTable td:nth-child(6) {
    display: none;
  }

  .paginationContainer {
    flex-direction: column;
    gap: 16px;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .statsContainer {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .statCard {
    padding: 15px;
  }

  .statIcon {
    width: 40px;
    height: 40px;
    margin-right: 12px;
  }

  .statNumber {
    font-size: 1.5rem;
  }

  .customerTable th:nth-child(7),
  .customerTable td:nth-child(7) {
    display: none;
  }

  .customerName {
    font-size: 0.9rem;
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .actions {
    flex-direction: column;
    gap: 4px;
  }

  .viewButton,
  .editButton {
    padding: 4px 8px;
    font-size: 0.8rem;
  }
}

@media (max-width: 428px) {
  .customerList {
    padding: 0.5rem;
  }

  .header {
    gap: 12px;
  }

  .header h2 {
    font-size: 1.4rem;
  }

  .customerTable th,
  .customerTable td {
    padding: 6px 4px;
  }

  .customerTable {
    font-size: 0.8rem;
    min-width: 400px; /* Ensure minimum width for horizontal scroll */
  }

  /* Hide email and phone number columns by default on very small screens */
  .customerTable th:nth-child(3), /* Email column */
  .customerTable td:nth-child(3),
  .customerTable th:nth-child(4), /* Phone column */
  .customerTable td:nth-child(4) {
    display: none;
  }

  .actions {
    flex-direction: column;
    gap: 4px;
    min-width: 80px;
  }

  .viewButton,
  .editButton {
    width: 100%;
    text-align: center;
    padding: 4px 6px;
    font-size: 0.7rem;
    min-height: 32px;
  }

  .filterControls {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .filterControls input,
  .filterControls select {
    font-size: 16px; /* Prevent zoom on iOS */
    min-height: 44px;
    padding: 10px;
  }

  .filterSelect {
    min-width: unset;
    width: 100%;
    font-size: 16px;
    min-height: 44px;
  }

  .bulkActions {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .bulkActions button {
    min-height: 44px;
    font-size: 16px;
  }

  .statsContainer {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .paginationContainer {
    flex-direction: column;
    gap: 12px;
    align-items: center;
  }

  .paginationButtons {
    flex-wrap: wrap;
    justify-content: center;
  }

  .paginationButton {
    min-height: 44px;
    min-width: 44px;
  }

  .header .actions {
    flex-direction: column;
    width: 100%;
    gap: 8px;
  }

  .header .actions .addButton,
  .header .actions .exportButton {
    width: 100%;
    justify-content: center;
    min-height: 44px;
    font-size: 16px;
  }
}

/* Extra small devices */
@media (max-width: 375px) {
  .customerList {
    padding: 0.375rem;
  }

  .header h2 {
    font-size: 1.3rem;
  }

  .customerTable {
    min-width: 350px;
  }

  .customerTable th,
  .customerTable td {
    padding: 4px 3px;
    font-size: 0.75rem;
  }

  .actions {
    min-width: 70px;
  }

  .viewButton,
  .editButton {
    padding: 3px 5px;
    font-size: 0.65rem;
    min-height: 28px;
  }
}

/* Very small devices */
@media (max-width: 320px) {
  .customerList {
    padding: 0.25rem;
  }

  .header h2 {
    font-size: 1.2rem;
  }

  .customerTable {
    min-width: 300px;
  }

  .customerTable th,
  .customerTable td {
    padding: 3px 2px;
    font-size: 0.7rem;
  }

  .actions {
    min-width: 60px;
  }

  .viewButton,
  .editButton {
    padding: 2px 4px;
    font-size: 0.6rem;
    min-height: 26px;
  }
}
