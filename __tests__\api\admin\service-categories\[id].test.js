import { createMocks } from 'node-mocks-http';
import handler from '@/pages/api/admin/service-categories/[id]';
import { supabaseAdmin } from '@/lib/supabase'; // Adjusted path for lib

// Mock supabaseAdmin
jest.mock('@/lib/supabase', () => ({
  supabaseAdmin: {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    single: jest.fn().mockReturnThis(),
  },
}));

const resetMocks = () => {
  jest.clearAllMocks();
  supabaseAdmin.from.mockReturnThis();
  supabaseAdmin.select.mockReturnThis();
  supabaseAdmin.eq.mockReturnThis();
  supabaseAdmin.single.mockReturnThis();
};

describe('/api/admin/service-categories/[id] API Endpoint', () => {
  beforeEach(() => {
    resetMocks();
  });

  describe('GET [id] handler', () => {
    it('should fetch a single category successfully and return serialized data', async () => {
      const categoryId = '1';
      const mockCategory = { id: 1, name: 'Category A', description: 'Desc A', parent_id: null };
      supabaseAdmin.from('service_categories').select().eq('id', 1).single.mockResolvedValueOnce({ data: mockCategory, error: null });

      const { req, res } = createMocks({ method: 'GET', query: { id: categoryId } });
      await handler(req, res);

      expect(res._getStatusCode()).toBe(200);
      const responseJson = JSON.parse(res._getData());
      expect(responseJson.success).toBe(true);
      expect(responseJson.category).toEqual({
        id: '1',
        name: 'Category A',
        description: 'Desc A',
        parent_id: null,
      });
      expect(supabaseAdmin.from).toHaveBeenCalledWith('service_categories');
      expect(supabaseAdmin.select).toHaveBeenCalledWith('id, name, description, parent_id');
      expect(supabaseAdmin.eq).toHaveBeenCalledWith('id', 1);
      expect(supabaseAdmin.single).toHaveBeenCalled();
    });

    it('should return 404 if category is not found (PGRST116 error)', async () => {
      const categoryId = '999';
      supabaseAdmin.from('service_categories').select().eq('id', 999).single.mockResolvedValueOnce({
        data: null,
        error: { code: 'PGRST116', message: 'The result contains 0 rows' },
      });

      const { req, res } = createMocks({ method: 'GET', query: { id: categoryId } });
      await handler(req, res);

      expect(res._getStatusCode()).toBe(404);
      expect(JSON.parse(res._getData()).error).toBe(`Category with ID ${categoryId} not found.`);
    });

    it('should return 404 if category is not found (data is null, no error)', async () => {
        // This case might not happen with .single() if it always errors on no data,
        // but good for robustness if behavior changes or .maybeSingle() was used.
      const categoryId = '998';
      supabaseAdmin.from('service_categories').select().eq('id', 998).single.mockResolvedValueOnce({
        data: null,
        error: null,
      });

      const { req, res } = createMocks({ method: 'GET', query: { id: categoryId } });
      await handler(req, res);

      expect(res._getStatusCode()).toBe(404);
      expect(JSON.parse(res._getData()).error).toBe(`Category with ID ${categoryId} not found.`);
    });


    it('should return 400 for invalid category ID format', async () => {
      const { req, res } = createMocks({ method: 'GET', query: { id: 'abc' } });
      await handler(req, res);
      expect(res._getStatusCode()).toBe(400);
      expect(JSON.parse(res._getData()).error).toBe('Category ID must be an integer.');
    });

    it('should return 400 if category ID is missing (though route implies it)', async () => {
      // Testing direct handler call without route parsing
      const { req, res } = createMocks({ method: 'GET', query: {} });
      await handler(req, res);
      expect(res._getStatusCode()).toBe(400);
      expect(JSON.parse(res._getData()).error).toBe('Category ID is required.');
    });

    it('should return 500 for other database errors', async () => {
      const categoryId = '1';
      supabaseAdmin.from('service_categories').select().eq('id', 1).single.mockResolvedValueOnce({
        data: null,
        error: new Error('Generic DB Error'),
      });
      const { req, res } = createMocks({ method: 'GET', query: { id: categoryId } });
      await handler(req, res);

      expect(res._getStatusCode()).toBe(500);
      expect(JSON.parse(res._getData()).error).toBe(`Failed to fetch category with ID ${categoryId}.`);
      expect(JSON.parse(res._getData()).details).toBe('Generic DB Error');
    });
  });

  describe('General error handling for [id] route', () => {
    it('should return 405 for disallowed method (e.g., POST)', async () => {
      const { req, res } = createMocks({ method: 'POST', query: { id: '1' } });
      await handler(req, res);
      expect(res._getStatusCode()).toBe(405);
      expect(JSON.parse(res._getData()).error).toBe('Method POST not allowed.');
    });
  });
});
