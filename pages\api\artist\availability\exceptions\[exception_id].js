import { createClient } from '@supabase/supabase-js';
import { getCurrentUserFromRequest } from '@/lib/supabase'; // Assuming this utility exists

// Define a name for the exceptions table
const ARTIST_AVAILABILITY_EXCEPTIONS_TABLE = 'artist_availability_exceptions';

export default async function handler(req, res) {
  const requestId = Math.random().toString(36).substring(7);
  const { exception_id } = req.query;

  console.log(`[${requestId}] /api/artist/availability/exceptions/${exception_id} called, method: ${req.method}`);

  if (!exception_id) {
    return res.status(400).json({ error: 'Exception ID is required.' });
  }

  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL,
    process.env.SUPABASE_SERVICE_ROLE_KEY,
    { auth: { autoRefreshToken: false, persistSession: false } }
  );

  try {
    const { user, error: userError } = await getCurrentUserFromRequest(req);

    if (userError || !user) {
      console.error(`[${requestId}] Authentication error for exception ${exception_id}:`, userError?.message || 'No user found');
      return res.status(401).json({ error: 'Unauthorized' });
    }

    const allowedRoles = ['artist', 'braider', 'admin', 'dev']; // Admin/dev might manage exceptions
    if (!user.role || !allowedRoles.includes(user.role)) {
      console.warn(`[${requestId}] User ${user.id} with role '${user.role}' attempted to modify exception ${exception_id}. Forbidden.`);
      return res.status(403).json({ error: 'Forbidden: You do not have permission to modify this resource.' });
    }

    // Get artist_id from artist_profiles table using user.id to ensure the user owns the exception
    // or is an admin. For simplicity, this example assumes artist is modifying their own.
    // A more robust solution for admins would involve checking if the admin *can* modify any, or if artist_id is passed.
    const { data: artistProfile, error: profileError } = await supabase
      .from('artist_profiles')
      .select('id')
      .eq('user_id', user.id)
      .single();

    if (profileError || !artistProfile) {
      console.error(`[${requestId}] Error fetching artist profile for user ${user.id} (exception ${exception_id}):`, profileError);
      return res.status(404).json({ error: 'Artist profile not found for the authenticated user.' });
    }
    const artistId = artistProfile.id;

    // Verify the exception belongs to the authenticated artist before modification/deletion
    const { data: existingException, error: fetchError } = await supabase
      .from(ARTIST_AVAILABILITY_EXCEPTIONS_TABLE)
      .select('id, artist_id')
      .eq('id', exception_id)
      .single();

    if (fetchError || !existingException) {
      // Check if the error is because the table doesn't exist (PostgREST error code 42P01)
      if (fetchError && fetchError.code === '42P01') {
          console.error(`[${requestId}] Error accessing exception ${exception_id}: Table '${ARTIST_AVAILABILITY_EXCEPTIONS_TABLE}' not found.`);
          return res.status(500).json({ error: `Database configuration error: Table '${ARTIST_AVAILABILITY_EXCEPTIONS_TABLE}' not found. Please contact support.` });
      }
      console.error(`[${requestId}] Exception ${exception_id} not found or error fetching:`, fetchError);
      return res.status(404).json({ error: 'Exception not found.' });
    }

    if (existingException.artist_id !== artistId && !['admin', 'dev'].includes(user.role)) {
        console.warn(`[${requestId}] User ${user.id} (artist_id ${artistId}) attempted to modify exception ${exception_id} belonging to artist ${existingException.artist_id}. Forbidden.`);
        return res.status(403).json({ error: 'Forbidden: You can only modify your own availability exceptions.' });
    }


    if (req.method === 'PUT') {
      const { exception_date, exception_type, start_time, end_time, notes } = req.body;
      console.log(`[${requestId}] Updating exception ${exception_id} for artist_id: ${artistId}`, req.body);

      // Basic Validation
      if (!exception_date || !exception_type) {
        return res.status(400).json({ error: 'Exception date and type are required.' });
      }
      const validTypes = ['Unavailable', 'Custom Hours', 'Additional Break'];
      if (!validTypes.includes(exception_type)) {
        return res.status(400).json({ error: 'Invalid exception type.' });
      }
      if ((exception_type === 'Custom Hours' || exception_type === 'Additional Break') && (!start_time || !end_time)) {
        return res.status(400).json({ error: 'Start time and end time are required for Custom Hours or Additional Break.' });
      }
      // TODO: Add more robust date and time format validation

      const updatedFields = {
        exception_date,
        exception_type,
        start_time: (exception_type === 'Custom Hours' || exception_type === 'Additional Break') ? start_time : null,
        end_time: (exception_type === 'Custom Hours' || exception_type === 'Additional Break') ? end_time : null,
        notes: notes || null,
        updated_at: new Date().toISOString(),
      };

      const { data: updatedException, error } = await supabase
        .from(ARTIST_AVAILABILITY_EXCEPTIONS_TABLE)
        .update(updatedFields)
        .eq('id', exception_id)
        .eq('artist_id', artistId) // Ensure they only update their own
        .select()
        .single();

      if (error) {
        console.error(`[${requestId}] Error updating exception ${exception_id} for artist ${artistId}:`, error);
        return res.status(500).json({ error: 'Failed to update exception.', details: error.message });
      }

      console.log(`[${requestId}] Exception ${exception_id} updated successfully for artist ${artistId}:`, updatedException);
      return res.status(200).json(updatedException);

    } else if (req.method === 'DELETE') {
      console.log(`[${requestId}] Deleting exception ${exception_id} for artist_id: ${artistId}`);

      const { error } = await supabase
        .from(ARTIST_AVAILABILITY_EXCEPTIONS_TABLE)
        .delete()
        .eq('id', exception_id)
        .eq('artist_id', artistId); // Ensure they only delete their own

      if (error) {
        console.error(`[${requestId}] Error deleting exception ${exception_id} for artist ${artistId}:`, error);
        return res.status(500).json({ error: 'Failed to delete exception.', details: error.message });
      }

      console.log(`[${requestId}] Exception ${exception_id} deleted successfully for artist ${artistId}.`);
      return res.status(200).json({ success: true, message: 'Exception deleted successfully.' });

    } else {
      res.setHeader('Allow', ['PUT', 'DELETE']);
      return res.status(405).json({ error: `Method ${req.method} Not Allowed` });
    }

  } catch (error) {
    // Check for table not found specifically for catch-all, though specific handlers above should catch it.
    if (error.message && error.message.includes(`relation "public.${ARTIST_AVAILABILITY_EXCEPTIONS_TABLE}" does not exist`)) {
        console.error(`[${requestId}] Critical error: Table '${ARTIST_AVAILABILITY_EXCEPTIONS_TABLE}' not found. Please ensure migrations are run.`);
        return res.status(500).json({ error: `Database configuration error: Table '${ARTIST_AVAILABILITY_EXCEPTIONS_TABLE}' not found. Please contact support.` });
    }
    console.error(`[${requestId}] Unexpected error in /api/artist/availability/exceptions/${exception_id}:`, error);
    return res.status(500).json({ error: 'Internal Server Error', details: error.message });
  }
}
