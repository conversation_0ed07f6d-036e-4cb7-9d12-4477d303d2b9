-- Ocean Soul Sparkles Pre-Migration Validation Script
-- Run this script before executing the service consolidation migration
-- This will help identify any potential issues and provide baseline metrics

-- ============================================================================
-- CURRENT STATE ANALYSIS
-- ============================================================================

-- 1. Total service count by category
SELECT 
  COALESCE(category, 'No Category') as category,
  COUNT(*) as service_count,
  COUNT(CASE WHEN status = 'active' THEN 1 END) as active_count,
  COUNT(CASE WHEN visible_on_public = true THEN 1 END) as public_visible,
  COUNT(CASE WHEN visible_on_pos = true THEN 1 END) as pos_visible,
  COUNT(CASE WHEN visible_on_events = true THEN 1 END) as events_visible
FROM services 
GROUP BY category 
ORDER BY service_count DESC;

-- 2. Services that will be consolidated (Body Painting)
SELECT 
  'BODY PAINTING CONSOLIDATION' as consolidation_group,
  id, name, duration, price, status, visible_on_pos, visible_on_events
FROM services 
WHERE category = 'Body Painting' 
ORDER BY price;

-- 3. Services that will be consolidated (Face Painting)
SELECT 
  'FACE PAINTING CONSOLIDATION' as consolidation_group,
  id, name, duration, price, status, visible_on_pos, visible_on_events
FROM services 
WHERE category = 'Face Painting' 
ORDER BY price;

-- 4. Services that will be consolidated (Airbrush)
SELECT 
  'AIRBRUSH CONSOLIDATION' as consolidation_group,
  id, name, duration, price, status, visible_on_pos, visible_on_events
FROM services 
WHERE category IN ('Airbrush', 'airbrush') 
ORDER BY price;

-- 5. Services that will be consolidated (Glitter & Gems)
SELECT 
  'GLITTER & GEMS CONSOLIDATION' as consolidation_group,
  id, name, duration, price, status, visible_on_pos, visible_on_events
FROM services 
WHERE category = 'Glitter & Gems' 
ORDER BY price;

-- 6. Services that will be consolidated (Hair & Braiding)
SELECT 
  'HAIR & BRAIDING CONSOLIDATION' as consolidation_group,
  id, name, duration, price, status, visible_on_pos, visible_on_events
FROM services 
WHERE category IN ('Hair & Braiding', 'braiding') 
ORDER BY price;

-- ============================================================================
-- BOOKING IMPACT ANALYSIS
-- ============================================================================

-- 7. Check for existing bookings that reference services to be consolidated
SELECT 
  'BOOKING IMPACT ANALYSIS' as analysis_type,
  s.category,
  COUNT(b.id) as booking_count,
  MIN(b.start_time) as earliest_booking,
  MAX(b.start_time) as latest_booking
FROM bookings b
JOIN services s ON b.service_id = s.id
WHERE s.category IN ('Body Painting', 'Face Painting', 'Airbrush', 'airbrush', 'Glitter & Gems', 'Hair & Braiding', 'braiding')
GROUP BY s.category
ORDER BY booking_count DESC;

-- 8. Recent bookings (last 30 days) for services to be consolidated
SELECT 
  'RECENT BOOKINGS (30 DAYS)' as analysis_type,
  s.name as service_name,
  s.category,
  COUNT(b.id) as recent_bookings
FROM bookings b
JOIN services s ON b.service_id = s.id
WHERE s.category IN ('Body Painting', 'Face Painting', 'Airbrush', 'airbrush', 'Glitter & Gems', 'Hair & Braiding', 'braiding')
  AND b.start_time >= NOW() - INTERVAL '30 days'
GROUP BY s.id, s.name, s.category
ORDER BY recent_bookings DESC;

-- ============================================================================
-- PRICING TIER ANALYSIS
-- ============================================================================

-- 9. Current pricing tier table status
SELECT 
  'PRICING TIER TABLE STATUS' as analysis_type,
  COUNT(*) as existing_pricing_tiers
FROM service_pricing_tiers;

-- 10. Services that already have pricing tiers (should be none)
SELECT 
  'SERVICES WITH EXISTING TIERS' as analysis_type,
  s.name,
  COUNT(spt.id) as tier_count
FROM services s
LEFT JOIN service_pricing_tiers spt ON s.id = spt.service_id
GROUP BY s.id, s.name
HAVING COUNT(spt.id) > 0;

-- ============================================================================
-- VALIDATION CHECKS
-- ============================================================================

-- 11. Check for duplicate service names within categories
SELECT 
  'DUPLICATE NAME CHECK' as check_type,
  category,
  name,
  COUNT(*) as duplicate_count
FROM services
WHERE category IN ('Body Painting', 'Face Painting', 'Airbrush', 'airbrush', 'Glitter & Gems', 'Hair & Braiding', 'braiding')
GROUP BY category, name
HAVING COUNT(*) > 1;

-- 12. Check for services with zero or negative prices
SELECT 
  'ZERO/NEGATIVE PRICE CHECK' as check_type,
  id, name, price, category
FROM services
WHERE price <= 0
  AND category IN ('Body Painting', 'Face Painting', 'Airbrush', 'airbrush', 'Glitter & Gems', 'Hair & Braiding', 'braiding');

-- 13. Check for services with very short or very long durations
SELECT 
  'DURATION ANOMALY CHECK' as check_type,
  id, name, duration, category,
  CASE 
    WHEN duration < 5 THEN 'Very Short (< 5 min)'
    WHEN duration > 180 THEN 'Very Long (> 3 hours)'
    ELSE 'Normal'
  END as duration_category
FROM services
WHERE (duration < 5 OR duration > 180)
  AND category IN ('Body Painting', 'Face Painting', 'Airbrush', 'airbrush', 'Glitter & Gems', 'Hair & Braiding', 'braiding');

-- ============================================================================
-- MIGRATION READINESS CHECKLIST
-- ============================================================================

-- 14. Services to be consolidated count
SELECT 
  'CONSOLIDATION SUMMARY' as summary_type,
  COUNT(*) as total_services_to_consolidate,
  COUNT(CASE WHEN status = 'active' THEN 1 END) as active_services,
  COUNT(CASE WHEN status != 'active' THEN 1 END) as inactive_services
FROM services
WHERE category IN ('Body Painting', 'Face Painting', 'Airbrush', 'airbrush', 'Glitter & Gems', 'Hair & Braiding', 'braiding');

-- 15. Services to remain separate
SELECT 
  'SERVICES REMAINING SEPARATE' as summary_type,
  COALESCE(category, 'No Category') as category,
  COUNT(*) as service_count
FROM services
WHERE category NOT IN ('Body Painting', 'Face Painting', 'Airbrush', 'airbrush', 'Glitter & Gems', 'Hair & Braiding', 'braiding')
   OR category IS NULL
GROUP BY category
ORDER BY service_count DESC;

-- ============================================================================
-- EXPECTED POST-MIGRATION STATE
-- ============================================================================

-- 16. Expected parent services to be created
SELECT 
  'EXPECTED PARENT SERVICES' as summary_type,
  'Body Painting' as parent_service_name,
  5 as expected_pricing_tiers
UNION ALL
SELECT 
  'EXPECTED PARENT SERVICES',
  'Face Painting',
  7
UNION ALL
SELECT 
  'EXPECTED PARENT SERVICES',
  'Airbrush Face & Body Painting',
  7
UNION ALL
SELECT 
  'EXPECTED PARENT SERVICES',
  'Glitter & Gem Application',
  9
UNION ALL
SELECT 
  'EXPECTED PARENT SERVICES',
  'Hair Braiding & Styling',
  25;

-- ============================================================================
-- FINAL VALIDATION SUMMARY
-- ============================================================================

-- 17. Migration readiness summary
SELECT 
  'MIGRATION READINESS' as status,
  CASE 
    WHEN (SELECT COUNT(*) FROM service_pricing_tiers) = 0 THEN '✅ No existing pricing tiers to conflict'
    ELSE '❌ Existing pricing tiers found - review needed'
  END as pricing_tier_status,
  CASE 
    WHEN (SELECT COUNT(*) FROM services WHERE category IN ('Body Painting', 'Face Painting', 'Airbrush', 'airbrush', 'Glitter & Gems', 'Hair & Braiding', 'braiding')) > 0 
    THEN '✅ Services found for consolidation'
    ELSE '❌ No services found for consolidation'
  END as services_status,
  CASE 
    WHEN (SELECT COUNT(*) FROM bookings b JOIN services s ON b.service_id = s.id WHERE s.category IN ('Body Painting', 'Face Painting', 'Airbrush', 'airbrush', 'Glitter & Gems', 'Hair & Braiding', 'braiding')) > 0
    THEN '⚠️ Existing bookings found - mapping required'
    ELSE '✅ No existing bookings to migrate'
  END as booking_status;

-- ============================================================================
-- INSTRUCTIONS
-- ============================================================================

/*
INSTRUCTIONS FOR RUNNING THIS VALIDATION:

1. Run this entire script in your database
2. Review all output sections carefully
3. Pay special attention to:
   - Booking impact analysis (section 7-8)
   - Validation checks (section 11-13)
   - Migration readiness summary (section 17)

4. Before proceeding with migration:
   ✅ Ensure no unexpected pricing tiers exist
   ✅ Review all services to be consolidated
   ✅ Understand booking impact
   ✅ Verify no critical validation issues

5. If any issues are found:
   - Document them in the implementation guide
   - Adjust migration script as needed
   - Re-run validation after fixes

6. Only proceed with migration when all checks pass
*/
