import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { toast } from 'react-toastify';
import styles from '@/styles/admin/AvailabilitySettings.module.css';
import Head from 'next/head'; // Import Head

const daysOfWeek = [
  { id: 1, name: 'Monday' },    // Supabase/PostgreSQL typically uses 0 for Sunday, 1 for Monday
  { id: 2, name: 'Tuesday' },
  { id: 3, name: 'Wednesday' },
  { id: 4, name: 'Thursday' },
  { id: 5, name: 'Friday' },
  { id: 6, name: 'Saturday' },
  { id: 0, name: 'Sunday' },
];

const initialScheduleDay = {
  is_available: false,
  start_time: '09:00',
  end_time: '17:00',
  break_start_time: '12:00',
  break_end_time: '13:00',
};

export default function AvailabilitySettings() {
  const { user, supabaseClient, loading: authLoading } = useAuth();
  const [activeTab, setActiveTab] = useState('weekly'); // 'weekly' or 'exceptions'

  // Weekly Schedule State
  const [weeklySchedule, setWeeklySchedule] = useState(() =>
    daysOfWeek.map(day => ({ day_of_week: day.id, ...initialScheduleDay }))
  );
  const [loadingSchedule, setLoadingSchedule] = useState(false);
  const [scheduleError, setScheduleError] = useState('');

  // Exceptions State (placeholders for now)
  const [exceptions, setExceptions] = useState([]);
  const [loadingExceptions, setLoadingExceptions] = useState(false);
  const [exceptionError, setExceptionError] = useState('');
  const [showExceptionModal, setShowExceptionModal] = useState(false);
  const [currentException, setCurrentException] = useState(null); // For editing or adding
  const [isEditingException, setIsEditingException] = useState(false);

  const initialExceptionFormState = {
    exception_date: '',
    exception_type: 'Unavailable', // Default type
    start_time: '',
    end_time: '',
    notes: ''
  };
  const [exceptionFormData, setExceptionFormData] = useState(initialExceptionFormState);


  const fetchWeeklySchedule = useCallback(async () => {
    if (!user || !supabaseClient || !user.artistProfile?.id) return;
    setLoadingSchedule(true);
    setScheduleError('');
    try {
      const { data, error } = await supabaseClient
        .from('artist_availability_schedule')
        .select('*')
        .eq('artist_id', user.artistProfile?.id); // Assuming artistProfile.id is available from useAuth or fetched

      if (error) throw error;

      if (data && data.length > 0) {
        const newSchedule = daysOfWeek.map(dayObj => {
          const existingDay = data.find(d => d.day_of_week === dayObj.id);
          return existingDay ?
            { ...existingDay, start_time: existingDay.start_time || initialScheduleDay.start_time, end_time: existingDay.end_time || initialScheduleDay.end_time, break_start_time: existingDay.break_start_time || initialScheduleDay.break_start_time, break_end_time: existingDay.break_end_time || initialScheduleDay.break_end_time }
            : { day_of_week: dayObj.id, ...initialScheduleDay };
        });
        setWeeklySchedule(newSchedule);
      } else {
        // Initialize with default values if no schedule exists
        setWeeklySchedule(daysOfWeek.map(day => ({ day_of_week: day.id, ...initialScheduleDay })));
      }
    } catch (err) {
      console.error("Error fetching weekly schedule:", err);
      setScheduleError('Failed to load weekly schedule. ' + err.message);
      toast.error('Failed to load weekly schedule.');
    } finally {
      setLoadingSchedule(false);
    }
  }, [user, supabaseClient]);

  useEffect(() => {
    if (user && user.artistProfile?.id && activeTab === 'weekly') {
      fetchWeeklySchedule();
    }
    if (user && user.artistProfile?.id) {
      if (activeTab === 'weekly') {
        fetchWeeklySchedule();
      } else if (activeTab === 'exceptions') {
        fetchExceptions();
      }
    }
  }, [user, supabaseClient, activeTab, fetchWeeklySchedule, fetchExceptions]); // Added fetchExceptions

  const handleWeeklyScheduleChange = (dayOfWeek, field, value) => {
    setWeeklySchedule(prevSchedule =>
      prevSchedule.map(day =>
        day.day_of_week === dayOfWeek ? { ...day, [field]: value } : day
      )
    );
  };

  const handleSaveWeeklySchedule = async () => {
    if (!user || !user.artistProfile?.id) {
        toast.error("User profile not found. Cannot save schedule.");
        return;
    }
    setLoadingSchedule(true);
    setScheduleError('');
    try {
      const scheduleToSave = weeklySchedule.map(day => ({
        ...day,
        artist_id: user.artistProfile.id, // This needs to be the artist_profiles.id
        start_time: day.is_available ? day.start_time : null,
        end_time: day.is_available ? day.end_time : null,
        break_start_time: day.is_available && day.break_start_time ? day.break_start_time : null,
        break_end_time: day.is_available && day.break_end_time ? day.break_end_time : null,
      }));

      const response = await fetch('/api/artist/availability/schedule', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${supabaseClient.auth.session()?.access_token}`
        },
        body: JSON.stringify(scheduleToSave),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Server error: ${response.status}`);
      }

      toast.success('Weekly schedule saved successfully!');
      fetchWeeklySchedule(); // Refresh data
    } catch (err) {
      console.error("Error saving weekly schedule:", err);
      setScheduleError('Failed to save weekly schedule. ' + err.message);
      toast.error('Failed to save weekly schedule: ' + err.message);
    } finally {
      setLoadingSchedule(false);
    }
  };

  const handleTabChange = (tab) => {
    setActiveTab(tab);
    setScheduleError('');
    setExceptionError('');
  };

  const fetchExceptions = useCallback(async () => {
    if (!user || !supabaseClient || !user.artistProfile?.id) return;
    setLoadingExceptions(true);
    setExceptionError('');
    try {
      const response = await fetch(`/api/artist/availability/exceptions`, {
        headers: {
          'Authorization': `Bearer ${supabaseClient.auth.session()?.access_token}`
        }
      });
      if (!response.ok) {
        const errData = await response.json();
        throw new Error(errData.error || 'Failed to fetch exceptions');
      }
      const data = await response.json();
      setExceptions(data);
    } catch (err) {
      console.error("Error fetching exceptions:", err);
      setExceptionError('Failed to load exceptions. ' + err.message);
      toast.error('Failed to load exceptions.');
    } finally {
      setLoadingExceptions(false);
    }
  }, [user, supabaseClient]);

  const handleExceptionFormChange = (e) => {
    const { name, value } = e.target;
    setExceptionFormData(prev => ({ ...prev, [name]: value }));
  };

  const openAddExceptionModal = () => {
    setIsEditingException(false);
    setCurrentException(null);
    setExceptionFormData(initialExceptionFormState);
    setShowExceptionModal(true);
  };

  const openEditExceptionModal = (exception) => {
    setIsEditingException(true);
    setCurrentException(exception);
    setExceptionFormData({
      exception_date: exception.exception_date,
      exception_type: exception.exception_type,
      start_time: exception.start_time || '',
      end_time: exception.end_time || '',
      notes: exception.notes || ''
    });
    setShowExceptionModal(true);
  };

  const handleSaveException = async (e) => {
    e.preventDefault();
    if (!user || !user.artistProfile?.id) {
      toast.error("User profile not found.");
      return;
    }
    setLoadingExceptions(true);
    setExceptionError('');

    const payload = {
      ...exceptionFormData,
      artist_id: user.artistProfile.id, // Ensure artist_id is part of payload if API expects it
    };

    // Clear times if type is 'Unavailable'
    if (payload.exception_type === 'Unavailable') {
        payload.start_time = null;
        payload.end_time = null;
    }


    try {
      const url = isEditingException
        ? `/api/artist/availability/exceptions/${currentException.id}`
        : '/api/artist/availability/exceptions';
      const method = isEditingException ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method: method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${supabaseClient.auth.session()?.access_token}`
        },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Server error: ${response.status}`);
      }

      toast.success(`Exception ${isEditingException ? 'updated' : 'added'} successfully!`);
      setShowExceptionModal(false);
      fetchExceptions(); // Refresh the list
    } catch (err) {
      console.error(`Error ${isEditingException ? 'updating' : 'adding'} exception:`, err);
      setExceptionError(`Failed to ${isEditingException ? 'update' : 'add'} exception. ` + err.message);
      toast.error(`Failed to ${isEditingException ? 'update' : 'add'} exception: ` + err.message);
    } finally {
      setLoadingExceptions(false);
    }
  };

  const handleDeleteException = async (exceptionId) => {
    if (!confirm('Are you sure you want to delete this exception?')) return;

    setLoadingExceptions(true);
    setExceptionError('');
    try {
      const response = await fetch(`/api/artist/availability/exceptions/${exceptionId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${supabaseClient.auth.session()?.access_token}`
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Server error: ${response.status}`);
      }
      toast.success('Exception deleted successfully!');
      fetchExceptions(); // Refresh the list
    } catch (err) {
      console.error("Error deleting exception:", err);
      setExceptionError('Failed to delete exception. ' + err.message);
      toast.error('Failed to delete exception: ' + err.message);
    } finally {
      setLoadingExceptions(false);
    }
  };


  if (authLoading) {
    return <div className={styles.loading}>Loading authentication status...</div>;
  }
  if (!user) {
    return <p className={styles.error}>Please log in to manage your availability.</p>;
  }
   if (!user.artistProfile?.id) { // Ensure artistProfile and its id exist
    return <p className={styles.error}>Artist profile not found or incomplete. Cannot manage availability. Please complete your profile or contact support.</p>;
  }


  return (
    <div className={styles.availabilityContainer}>
      <Head>
        <title>Manage Availability | Staff Portal</title>
      </Head>
      <h2>Manage Your Availability</h2>

      <div className={styles.tabs}>
        <button
          className={`${styles.tabButton} ${activeTab === 'weekly' ? styles.activeTab : ''}`}
          onClick={() => handleTabChange('weekly')}
        >
          Weekly Schedule
        </button>
        <button
          className={`${styles.tabButton} ${activeTab === 'exceptions' ? styles.activeTab : ''}`}
          onClick={() => handleTabChange('exceptions')}
        >
          Date-Specific Exceptions
        </button>
      </div>

      {scheduleError && activeTab === 'weekly' && <p className={styles.error}>{scheduleError}</p>}
      {exceptionError && activeTab === 'exceptions' && <p className={styles.error}>{exceptionError}</p>}

      {activeTab === 'weekly' && (
        <div className={styles.tabContent}>
          <h3>Set Your Standard Weekly Availability</h3>
          {loadingSchedule && <p>Loading schedule...</p>}
          {!loadingSchedule && weeklySchedule.length > 0 && weeklySchedule.map((daySetting) => {
            const dayInfo = daysOfWeek.find(d => d.id === daySetting.day_of_week);
            return (
              <div key={daySetting.day_of_week} className={styles.dayRow}>
                <h4>{dayInfo?.name}</h4>
                <div className={styles.daySettings}>
                  <label className={styles.checkboxLabel}>
                    <input
                      type="checkbox"
                      checked={!!daySetting.is_available}
                      onChange={(e) => handleWeeklyScheduleChange(daySetting.day_of_week, 'is_available', e.target.checked)}
                    />
                    Available
                  </label>
                  {daySetting.is_available && (
                    <>
                      <div className={styles.timeInputGroup}>
                        <label htmlFor={`start-${dayInfo?.name}`}>Start Time:</label>
                        <input
                          type="time"
                          id={`start-${dayInfo?.name}`}
                          name="start_time"
                          value={daySetting.start_time || ''}
                          onChange={(e) => handleWeeklyScheduleChange(daySetting.day_of_week, 'start_time', e.target.value)}
                          className={styles.input}
                        />
                      </div>
                      <div className={styles.timeInputGroup}>
                        <label htmlFor={`end-${dayInfo?.name}`}>End Time:</label>
                        <input
                          type="time"
                          id={`end-${dayInfo?.name}`}
                          name="end_time"
                          value={daySetting.end_time || ''}
                          onChange={(e) => handleWeeklyScheduleChange(daySetting.day_of_week, 'end_time', e.target.value)}
                          className={styles.input}
                        />
                      </div>
                      <div className={styles.timeInputGroup}>
                        <label htmlFor={`break-start-${dayInfo?.name}`}>Break Start:</label>
                        <input
                          type="time"
                          id={`break-start-${dayInfo?.name}`}
                          name="break_start_time"
                          value={daySetting.break_start_time || ''}
                          onChange={(e) => handleWeeklyScheduleChange(daySetting.day_of_week, 'break_start_time', e.target.value)}
                          className={styles.input}
                        />
                      </div>
                      <div className={styles.timeInputGroup}>
                        <label htmlFor={`break-end-${dayInfo?.name}`}>Break End:</label>
                        <input
                          type="time"
                          id={`break-end-${dayInfo?.name}`}
                          name="break_end_time"
                          value={daySetting.break_end_time || ''}
                          onChange={(e) => handleWeeklyScheduleChange(daySetting.day_of_week, 'break_end_time', e.target.value)}
                          className={styles.input}
                        />
                      </div>
                    </>
                  )}
                </div>
              </div>
            );
          })}
           {!loadingSchedule && weeklySchedule.length === 0 && <p>No weekly schedule found. Please set one up.</p>}
          <button onClick={handleSaveWeeklySchedule} disabled={loadingSchedule || authLoading} className={styles.saveButton}>
            {loadingSchedule ? 'Saving...' : 'Save Weekly Schedule'}
          </button>
        </div>
      )}

      {activeTab === 'exceptions' && (
        <div className={styles.tabContent}>
          <h3>Manage Availability Exceptions</h3>
          <button onClick={openAddExceptionModal} disabled={loadingExceptions || authLoading} className={styles.addButton}>
            Add New Exception
          </button>
          {loadingExceptions && <p>Loading exceptions...</p>}
          {!loadingExceptions && exceptions.length === 0 && <p>No exceptions found for the upcoming period.</p>}
          {!loadingExceptions && exceptions.length > 0 && (
            <ul className={styles.exceptionsList}>
              {exceptions.map(ex => (
                <li key={ex.id} className={styles.exceptionItem}>
                  <div className={styles.exceptionDetails}>
                    <p><strong>Date:</strong> {new Date(ex.exception_date + 'T00:00:00').toLocaleDateString()}</p> {/* Ensure date is parsed correctly */}
                    <p><strong>Type:</strong> {ex.exception_type}</p>
                    {ex.start_time && <p><strong>From:</strong> {ex.start_time}</p>}
                    {ex.end_time && <p><strong>To:</strong> {ex.end_time}</p>}
                    {ex.notes && <p><strong>Notes:</strong> {ex.notes}</p>}
                  </div>
                  <div className={styles.exceptionActions}>
                    <button onClick={() => openEditExceptionModal(ex)} className={`${styles.button} ${styles.editButton}`}>Edit</button>
                    <button onClick={() => handleDeleteException(ex.id)} className={`${styles.button} ${styles.deleteButton}`} disabled={loadingExceptions}>Delete</button>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      )}

      {showExceptionModal && (
        <div className={styles.modalOverlay}>
          <div className={styles.modalContent}>
            <h4>{isEditingException ? 'Edit' : 'Add'} Exception</h4>
            <form onSubmit={handleSaveException}>
              <div className={styles.formGroup}>
                <label htmlFor="exception_date">Date:</label>
                <input
                  type="date"
                  id="exception_date"
                  name="exception_date"
                  value={exceptionFormData.exception_date}
                  onChange={handleExceptionFormChange}
                  required
                  className={styles.input}
                />
              </div>
              <div className={styles.formGroup}>
                <label htmlFor="exception_type">Type:</label>
                <select
                  id="exception_type"
                  name="exception_type"
                  value={exceptionFormData.exception_type}
                  onChange={handleExceptionFormChange}
                  required
                  className={styles.select}
                >
                  <option value="Unavailable">Unavailable (Full Day)</option>
                  <option value="Custom Hours">Custom Working Hours</option>
                  <option value="Additional Break">Additional Break</option>
                </select>
              </div>
              {(exceptionFormData.exception_type === 'Custom Hours' || exceptionFormData.exception_type === 'Additional Break') && (
                <>
                  <div className={styles.formGroup}>
                    <label htmlFor="start_time">Start Time:</label>
                    <input
                      type="time"
                      id="start_time"
                      name="start_time"
                      value={exceptionFormData.start_time}
                      onChange={handleExceptionFormChange}
                      required={exceptionFormData.exception_type !== 'Unavailable'}
                      className={styles.input}
                    />
                  </div>
                  <div className={styles.formGroup}>
                    <label htmlFor="end_time">End Time:</label>
                    <input
                      type="time"
                      id="end_time"
                      name="end_time"
                      value={exceptionFormData.end_time}
                      onChange={handleExceptionFormChange}
                      required={exceptionFormData.exception_type !== 'Unavailable'}
                      className={styles.input}
                    />
                  </div>
                </>
              )}
              <div className={styles.formGroup}>
                <label htmlFor="notes">Notes (Optional):</label>
                <textarea
                  id="notes"
                  name="notes"
                  value={exceptionFormData.notes}
                  onChange={handleExceptionFormChange}
                  className={styles.textarea}
                  rows="3"
                />
              </div>
              <div className={styles.modalActions}>
                <button type="button" onClick={() => setShowExceptionModal(false)} className={styles.cancelButton} disabled={loadingExceptions}>
                  Cancel
                </button>
                <button type="submit" className={styles.saveButton} disabled={loadingExceptions}>
                  {loadingExceptions ? 'Saving...' : (isEditingException ? 'Update Exception' : 'Add Exception')}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
