import React from 'react'
import { render, screen, fireEvent, within } from '@testing-library/react' // Added within
import '@testing-library/jest-dom'
import QuickEventServiceSelector from '@/components/admin/pos/QuickEventServiceSelector'
// Import the specific functions to be mocked/spied on from the actual module path
import { safeRender, safeFormatCurrency } from '@/lib/safe-render-utils'

// Mock safeRender and safeFormatCurrency
jest.mock('@/lib/safe-render-utils', () => ({
  ...jest.requireActual('@/lib/safe-render-utils'), // Import and retain default behavior
  safeRender: jest.fn((value, fallback = '') => value || fallback),
  safeFormatCurrency: jest.fn((value, currency = 'AUD') => {
    const num = parseFloat(value)
    if (isNaN(num)) return '$0.00' // Or some other fallback for invalid numbers
    return `$${num.toFixed(2)}`
  }),
}))


const mockServices = [
  {
    id: 's1',
    name: 'Face Painting',
    category: 'Painting',
    visible_on_events: true,
    pricing_tiers: [
      { id: 't1a', name: 'Basic', duration: 15, price: 10, sort_order: 1 },
      { id: 't1b', name: 'Deluxe', duration: 30, price: 20, sort_order: 2, is_default: true },
    ],
  },
  {
    id: 's2',
    name: 'Glitter Tattoo',
    category: 'Glitter',
    visible_on_pos: true,
    pricing_tiers: [
      { id: 't2a', name: 'Small', duration: 10, price: 5, sort_order: 1 },
    ],
  },
  {
    id: 's3',
    name: 'Hidden Service', // Not visible on POS/Events
    category: 'Hidden',
    visible_on_events: false,
    visible_on_pos: false,
    pricing_tiers: [{ id: 't3a', name: 'Standard', duration: 60, price: 50 }],
  }
]

describe('QuickEventServiceSelector', () => {
  let mockOnAddToCart
  let mockOnRemoveFromCart
  let mockOnProceedToCheckout

  beforeEach(() => {
    mockOnAddToCart = jest.fn()
    mockOnRemoveFromCart = jest.fn()
    mockOnProceedToCheckout = jest.fn()
    // Clear mocks for safeRender and safeFormatCurrency if specific call counts are needed per test
    safeFormatCurrency.mockClear()
    safeRender.mockClear()
  })

  test('renders categories initially', () => {
    render(
      <QuickEventServiceSelector
        services={mockServices}
        quickEventCart={[]}
        onAddToCart={mockOnAddToCart}
        onRemoveFromCart={mockOnRemoveFromCart}
        onProceedToCheckout={mockOnProceedToCheckout}
      />
    )
    expect(screen.getByText('Select Service Category')).toBeInTheDocument()
    expect(screen.getByText('Painting')).toBeInTheDocument() // Category name
    expect(screen.getByText('Glitter')).toBeInTheDocument()
    expect(screen.queryByText('Hidden Service')).not.toBeInTheDocument() // Service name, should not be visible
  })

  test('renders services and tiers after selecting a category', () => {
    render(
      <QuickEventServiceSelector
        services={mockServices}
        quickEventCart={[]}
        onAddToCart={mockOnAddToCart}
        onRemoveFromCart={mockOnRemoveFromCart}
        onProceedToCheckout={mockOnProceedToCheckout}
      />
    )
    fireEvent.click(screen.getByText('Painting'))
    expect(screen.getByText('Face Painting')).toBeInTheDocument() // Service name
    expect(screen.getByText('Basic')).toBeInTheDocument() // Tier name
    expect(screen.getByText('$10.00')).toBeInTheDocument() // Tier price
    expect(screen.getByText('Deluxe')).toBeInTheDocument()
    expect(screen.getByText('$20.00')).toBeInTheDocument()
  })

  test('calls onAddToCart when a service tier is clicked', () => {
    jest.useFakeTimers()
    render(
      <QuickEventServiceSelector
        services={mockServices}
        quickEventCart={[]}
        onAddToCart={mockOnAddToCart}
        onRemoveFromCart={mockOnRemoveFromCart}
        onProceedToCheckout={mockOnProceedToCheckout}
      />
    )
    fireEvent.click(screen.getByText('Painting')) // Select category
    fireEvent.click(screen.getByText('Basic')) // Select tier

    jest.advanceTimersByTime(200) // For the setTimeout in handleServiceTierSelect

    expect(mockOnAddToCart).toHaveBeenCalledTimes(1)
    expect(mockOnAddToCart).toHaveBeenCalledWith(
      expect.objectContaining({ id: 's1', name: 'Face Painting' }),
      expect.objectContaining({ id: 't1a', name: 'Basic', price: 10 })
    )
    jest.useRealTimers()
  })

  describe('Cart Display and Interaction', () => {
    const mockCart = [
      { service: mockServices[0], tier: mockServices[0].pricing_tiers[0] }, // Face Painting - Basic $10
      { service: mockServices[1], tier: mockServices[1].pricing_tiers[0] }, // Glitter Tattoo - Small $5
    ]

    test('renders cart items and total amount correctly', () => {
      render(
        <QuickEventServiceSelector
          services={mockServices}
          quickEventCart={mockCart}
          onAddToCart={mockOnAddToCart}
          onRemoveFromCart={mockOnRemoveFromCart}
          onProceedToCheckout={mockOnProceedToCheckout}
        />
      )
      // Ensure a category is selected to show the cart (cart shows alongside services of a category)
      fireEvent.click(screen.getByText('Painting'))

      // Use the class name directly as a string, because CSS modules are mocked by identity-obj-proxy
      const cartSection = screen.getByText('Shopping Cart').closest('.cartSection')
      expect(cartSection).toBeInTheDocument()

      // Check first cart item within the cart section
      expect(within(cartSection).getByText(mockServices[0].name)).toBeInTheDocument()
      expect(within(cartSection).getByText(new RegExp(`${mockServices[0].pricing_tiers[0].name}.*\\$10.00`))).toBeInTheDocument()
      // Check second cart item within the cart section
      expect(within(cartSection).getByText(mockServices[1].name)).toBeInTheDocument()
      expect(within(cartSection).getByText(new RegExp(`${mockServices[1].pricing_tiers[0].name}.*\\$5.00`))).toBeInTheDocument()

      // Check total amount (10 + 5 = 15)
      expect(screen.getByText('Total:')).toBeInTheDocument()
      expect(screen.getByText('$15.00')).toBeInTheDocument()
    })

    test('calls onRemoveFromCart when "Remove" button is clicked', () => {
      render(
        <QuickEventServiceSelector
          services={mockServices}
          quickEventCart={mockCart}
          onAddToCart={mockOnAddToCart}
          onRemoveFromCart={mockOnRemoveFromCart}
          onProceedToCheckout={mockOnProceedToCheckout}
        />
      )
      fireEvent.click(screen.getByText('Painting')) // Select category to show cart

      const removeButtons = screen.getAllByText('Remove')
      expect(removeButtons).toHaveLength(2)
      fireEvent.click(removeButtons[0]) // Remove the first item

      expect(mockOnRemoveFromCart).toHaveBeenCalledTimes(1)
      expect(mockOnRemoveFromCart).toHaveBeenCalledWith(0) // Index of the item
    })

    test('"Proceed to Checkout" button is enabled and calls onProceedToCheckout when cart has items', () => {
      render(
        <QuickEventServiceSelector
          services={mockServices}
          quickEventCart={mockCart}
          onAddToCart={mockOnAddToCart}
          onRemoveFromCart={mockOnRemoveFromCart}
          onProceedToCheckout={mockOnProceedToCheckout}
        />
      )
      fireEvent.click(screen.getByText('Painting')) // Select category

      const proceedButton = screen.getByText(/Proceed to Checkout \(\d+ items?\)/)
      expect(proceedButton).toBeInTheDocument()
      expect(proceedButton).not.toBeDisabled()
      fireEvent.click(proceedButton)
      expect(mockOnProceedToCheckout).toHaveBeenCalledTimes(1)
    })

    test('"Proceed to Checkout" button is disabled when cart is empty', () => {
      render(
        <QuickEventServiceSelector
          services={mockServices}
          quickEventCart={[]} // Empty cart
          onAddToCart={mockOnAddToCart}
          onRemoveFromCart={mockOnRemoveFromCart}
          onProceedToCheckout={mockOnProceedToCheckout}
        />
      )
      fireEvent.click(screen.getByText('Painting')) // Select category

      // The cart section, including the button, might not render if cart is empty.
      // If it does render but button is disabled:
      const proceedButton = screen.queryByText(/Proceed to Checkout/)
      if (proceedButton) {
         expect(proceedButton).toBeDisabled()
      } else {
        // This is also acceptable: if cart is empty, no checkout button.
         expect(screen.queryByText('Shopping Cart')).not.toBeInTheDocument()
      }
    })
  })

  test('handles empty services array gracefully', () => {
    render(
      <QuickEventServiceSelector
        services={[]}
        quickEventCart={[]}
        onAddToCart={mockOnAddToCart}
        onRemoveFromCart={mockOnRemoveFromCart}
        onProceedToCheckout={mockOnProceedToCheckout}
      />
    );
    expect(screen.getByText('No quick event services available')).toBeInTheDocument();
  });
})
