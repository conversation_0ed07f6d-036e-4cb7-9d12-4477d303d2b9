// Quick test script to verify the POS services API endpoint
const fetch = require('node-fetch');

async function testPOSServicesAPI() {
  try {
    console.log('🧪 Testing POS services API endpoint...');
    
    const response = await fetch('http://localhost:3000/api/admin/pos/services-with-artists');
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    console.log('✅ API Response received');
    console.log('📊 Total services:', data.services?.length || 0);
    
    if (data.services && data.services.length > 0) {
      console.log('🔍 Sample service structure:');
      const sample = data.services[0];
      console.log({
        name: sample.name,
        category: sample.category,
        visible_on_pos: sample.visible_on_pos,
        visible_on_events: sample.visible_on_events,
        availableArtistCount: sample.availableArtistCount,
        hasPricingTiers: sample.pricing_tiers?.length > 0
      });
      
      console.log('📋 All services:');
      data.services.forEach(service => {
        console.log(`  - ${service.name} (${service.category || 'No category'}) - POS: ${service.visible_on_pos}, Events: ${service.visible_on_events}`);
      });
    } else {
      console.log('❌ No services returned');
    }
    
  } catch (error) {
    console.error('❌ Error testing API:', error.message);
  }
}

testPOSServicesAPI();
