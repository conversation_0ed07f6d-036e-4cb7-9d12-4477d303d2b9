/**
 * TerminalTestSuite - Comprehensive testing component for Square Terminal integration
 * Provides automated testing for device pairing, checkout flow, and error scenarios
 */

import { useState, useEffect } from 'react'
import styles from '@/styles/admin/POS.module.css'

export default function TerminalTestSuite() {
  const [testResults, setTestResults] = useState([])
  const [isRunning, setIsRunning] = useState(false)
  const [currentTest, setCurrentTest] = useState('')
  const [testConfig, setTestConfig] = useState({
    testAmount: 10.00,
    testCurrency: 'AUD',
    skipDeviceTests: false,
    skipCheckoutTests: false,
    skipWebhookTests: false
  })

  const testSuites = [
    {
      id: 'device_management',
      name: 'Device Management Tests',
      tests: [
        'Load device list',
        'Create device code',
        'Verify device status',
        'Handle device errors'
      ]
    },
    {
      id: 'checkout_flow',
      name: 'Checkout Flow Tests',
      tests: [
        'Create terminal checkout',
        'Monitor checkout status',
        'Handle checkout completion',
        'Handle checkout cancellation'
      ]
    },
    {
      id: 'error_handling',
      name: 'Error Handling Tests',
      tests: [
        'Invalid device ID',
        'Network timeout',
        'API authentication',
        'Malformed requests'
      ]
    },
    {
      id: 'integration',
      name: 'Integration Tests',
      tests: [
        'POS workflow integration',
        'Database record creation',
        'Webhook processing',
        'Real-time updates'
      ]
    }
  ]

  const runTest = async (testName, testFunction) => {
    setCurrentTest(testName)
    const startTime = Date.now()
    
    try {
      const result = await testFunction()
      const duration = Date.now() - startTime
      
      setTestResults(prev => [...prev, {
        name: testName,
        status: 'passed',
        duration,
        message: result.message || 'Test passed successfully',
        timestamp: new Date().toISOString()
      }])
      
      return true
    } catch (error) {
      const duration = Date.now() - startTime
      
      setTestResults(prev => [...prev, {
        name: testName,
        status: 'failed',
        duration,
        message: error.message,
        error: error.stack,
        timestamp: new Date().toISOString()
      }])
      
      return false
    }
  }

  const testDeviceList = async () => {
    const response = await fetch('/api/admin/pos/terminal-devices', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
      }
    })
    
    if (!response.ok) {
      throw new Error(`Device list API failed: ${response.status}`)
    }
    
    const data = await response.json()
    return { message: `Found ${data.devices?.length || 0} devices` }
  }

  const testCreateDeviceCode = async () => {
    const response = await fetch('/api/admin/pos/terminal-devices', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
      },
      body: JSON.stringify({
        deviceName: `Test Device ${Date.now()}`
      })
    })
    
    if (!response.ok) {
      throw new Error(`Device creation failed: ${response.status}`)
    }
    
    const data = await response.json()
    return { message: `Device code created: ${data.deviceCode?.code}` }
  }

  const testTerminalCheckout = async () => {
    // First get available devices
    const devicesResponse = await fetch('/api/admin/pos/terminal-devices', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
      }
    })
    
    const devicesData = await devicesResponse.json()
    const pairedDevices = devicesData.devices?.filter(d => d.status === 'PAIRED') || []
    
    if (pairedDevices.length === 0) {
      throw new Error('No paired devices available for checkout test')
    }
    
    const testDevice = pairedDevices[0]
    
    const response = await fetch('/api/admin/pos/terminal-checkout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('adminToken')}`
      },
      body: JSON.stringify({
        deviceId: testDevice.deviceId,
        amount: testConfig.testAmount,
        currency: testConfig.testCurrency,
        orderDetails: {
          service: 'Test Service',
          customer: 'Test Customer',
          orderId: `test_${Date.now()}`
        }
      })
    })
    
    if (!response.ok) {
      throw new Error(`Checkout creation failed: ${response.status}`)
    }
    
    const data = await response.json()
    return { message: `Checkout created: ${data.checkout?.id}` }
  }

  const testAPIAuthentication = async () => {
    const response = await fetch('/api/admin/pos/terminal-devices', {
      headers: {
        'Authorization': 'Bearer invalid_token'
      }
    })
    
    if (response.status !== 401) {
      throw new Error(`Expected 401 unauthorized, got ${response.status}`)
    }
    
    return { message: 'Authentication properly rejected invalid token' }
  }

  const runAllTests = async () => {
    setIsRunning(true)
    setTestResults([])
    setCurrentTest('')
    
    const tests = [
      { name: 'Load Device List', func: testDeviceList },
      { name: 'Create Device Code', func: testCreateDeviceCode },
      { name: 'Test Authentication', func: testAPIAuthentication },
      { name: 'Terminal Checkout', func: testTerminalCheckout }
    ]
    
    let passedCount = 0
    
    for (const test of tests) {
      const passed = await runTest(test.name, test.func)
      if (passed) passedCount++
      
      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500))
    }
    
    setCurrentTest('')
    setIsRunning(false)
    
    // Summary
    setTestResults(prev => [...prev, {
      name: 'Test Summary',
      status: passedCount === tests.length ? 'passed' : 'warning',
      message: `${passedCount}/${tests.length} tests passed`,
      timestamp: new Date().toISOString()
    }])
  }

  const clearResults = () => {
    setTestResults([])
    setCurrentTest('')
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'passed': return '✅'
      case 'failed': return '❌'
      case 'warning': return '⚠️'
      default: return '⏳'
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'passed': return '#28a745'
      case 'failed': return '#dc3545'
      case 'warning': return '#ffc107'
      default: return '#6c757d'
    }
  }

  return (
    <div className={styles.terminalTestSuite}>
      <div className={styles.testSuiteHeader}>
        <h3>Square Terminal Test Suite</h3>
        <div className={styles.testControls}>
          <button
            className={styles.runTestsButton}
            onClick={runAllTests}
            disabled={isRunning}
          >
            {isRunning ? '⏳ Running Tests...' : '▶️ Run All Tests'}
          </button>
          <button
            className={styles.clearResultsButton}
            onClick={clearResults}
            disabled={isRunning}
          >
            🗑️ Clear Results
          </button>
        </div>
      </div>

      <div className={styles.testConfiguration}>
        <h4>Test Configuration</h4>
        <div className={styles.configGrid}>
          <div className={styles.configItem}>
            <label>Test Amount:</label>
            <input
              type="number"
              step="0.01"
              value={testConfig.testAmount}
              onChange={(e) => setTestConfig(prev => ({
                ...prev,
                testAmount: parseFloat(e.target.value) || 0
              }))}
              disabled={isRunning}
            />
          </div>
          <div className={styles.configItem}>
            <label>Currency:</label>
            <select
              value={testConfig.testCurrency}
              onChange={(e) => setTestConfig(prev => ({
                ...prev,
                testCurrency: e.target.value
              }))}
              disabled={isRunning}
            >
              <option value="AUD">AUD</option>
              <option value="USD">USD</option>
            </select>
          </div>
        </div>
      </div>

      {currentTest && (
        <div className={styles.currentTestIndicator}>
          <div className={styles.testSpinner}></div>
          <span>Running: {currentTest}</span>
        </div>
      )}

      <div className={styles.testResults}>
        <h4>Test Results</h4>
        {testResults.length === 0 ? (
          <div className={styles.noResults}>
            <p>No test results yet. Click "Run All Tests" to begin.</p>
          </div>
        ) : (
          <div className={styles.resultsList}>
            {testResults.map((result, index) => (
              <div
                key={index}
                className={`${styles.testResult} ${styles[result.status]}`}
              >
                <div className={styles.resultHeader}>
                  <span className={styles.resultIcon}>
                    {getStatusIcon(result.status)}
                  </span>
                  <span className={styles.resultName}>{result.name}</span>
                  <span className={styles.resultDuration}>
                    {result.duration ? `${result.duration}ms` : ''}
                  </span>
                </div>
                <div className={styles.resultMessage}>{result.message}</div>
                {result.error && (
                  <details className={styles.errorDetails}>
                    <summary>Error Details</summary>
                    <pre>{result.error}</pre>
                  </details>
                )}
                <div className={styles.resultTimestamp}>
                  {new Date(result.timestamp).toLocaleTimeString()}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className={styles.testSuiteInfo}>
        <h4>Available Test Suites</h4>
        <div className={styles.suitesList}>
          {testSuites.map((suite) => (
            <div key={suite.id} className={styles.testSuiteItem}>
              <h5>{suite.name}</h5>
              <ul>
                {suite.tests.map((test, index) => (
                  <li key={index}>{test}</li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
