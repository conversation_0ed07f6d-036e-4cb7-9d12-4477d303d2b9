.availabilityContainer {
  padding: 20px;
  background-color: #f9f9f9; /* Lighter background for the whole container */
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  font-family: 'Arial', sans-serif;
}

.tabs {
  display: flex;
  margin-bottom: 0; /* Remove bottom margin to connect with tabContent border */
  border-bottom: 1px solid #ddd;
}

.tabButton { /* Renamed from .tabs button for clarity */
  padding: 12px 24px;
  cursor: pointer;
  border: none;
  background-color: transparent;
  font-size: 1rem;
  font-weight: 600; /* Slightly bolder */
  color: #555;
  border-bottom: 3px solid transparent;
  margin-bottom: -1px; /* Align with the container's border */
  transition: color 0.2s ease-in-out, border-bottom-color 0.2s ease-in-out;
}

.tabButton.activeTab {
  color: #007bff;
  border-bottom-color: #007bff;
}

.tabButton:hover {
  color: #0056b3;
  background-color: #e9ecef; /* Light hover for tab */
}

.tabContent {
  padding: 25px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-top: none; /* Already handled by .tabs border */
  border-radius: 0 0 8px 8px;
}

.tabContent h3 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  font-size: 1.5rem;
}

.dayRow {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e0e0e0; /* Lighter separator */
}

.dayRow:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.dayRow h4 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #343a40;
  font-size: 1.2rem;
}

.daySettings {
  padding-left: 10px;
}

.timeInputGroup {
  display: flex;
  flex-wrap: wrap; /* Allow wrapping on smaller screens */
  align-items: center;
  gap: 10px;
  margin-bottom: 12px;
}

.timeInputGroup label {
  min-width: 90px; /* Adjusted min-width */
  font-weight: 500;
  color: #495057;
}

.input, /* Generic input style */
.select,
.textarea {
  padding: 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 0.95rem;
  width: 100%; /* Default to full width within its container */
  box-sizing: border-box;
}

.timeInputGroup input[type="time"],
.timeInputGroup input[type="date"],
.formGroup input[type="time"], /* For modal form */
.formGroup input[type="date"], /* For modal form */
.formGroup .select, /* For modal select */
.formGroup .textarea /* For modal textarea */
 {
  padding: 8px 10px;
  border: 1px solid #ccc;
  border-radius: 4px;
  font-size: 0.9rem;
  flex-grow: 1; /* Allow time inputs to grow */
}


.checkboxLabel {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  cursor: pointer;
}
.checkboxLabel input[type="checkbox"] {
  margin-right: 5px;
}


.saveButton,
.addButton,
.button { /* General button style */
  background-color: #007bff;
  color: white;
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.2s ease-in-out, opacity 0.2s ease-in-out;
  font-weight: 500;
}

.saveButton:hover,
.addButton:hover,
.button:hover {
  background-color: #0056b3;
}

.saveButton:disabled,
.addButton:disabled,
.button:disabled {
  background-color: #ccc;
  cursor: not-allowed;
  opacity: 0.7;
}

.error {
  color: #721c24; /* Darker red for better contrast */
  background-color: #f8d7da; /* Lighter red background */
  border: 1px solid #f5c6cb; /* Softer red border */
  padding: 12px;
  border-radius: 4px;
  margin: 15px 0;
  font-size: 0.9rem;
}

.loading {
  padding: 20px;
  text-align: center;
  font-size: 1.1rem;
  color: #555;
}

/* --- Styles for Exceptions Tab --- */

.exceptionsList {
  list-style-type: none;
  padding: 0;
  margin-top: 20px;
}

.exceptionItem {
  display: flex;
  flex-wrap: wrap; /* Allow wrapping for actions on small screens */
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  margin-bottom: 10px;
  background-color: #ffffff; /* White background for items */
  box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.exceptionDetails {
  flex-grow: 1;
}

.exceptionDetails p {
  margin: 4px 0;
  font-size: 0.95rem;
  color: #333;
}

.exceptionDetails p strong {
  color: #555;
}

.exceptionActions {
  display: flex;
  gap: 10px;
  margin-top: 10px; /* Add some space if actions wrap */
}

.exceptionActions .button { /* Use general button style */
  padding: 6px 12px;
  font-size: 0.85rem;
}

.editButton {
  background-color: #ffc107; /* Amber */
  color: #212529;
}
.editButton:hover {
  background-color: #e0a800;
}

.deleteButton {
  background-color: #dc3545; /* Red */
}
.deleteButton:hover {
  background-color: #c82333;
}

/* --- Modal Styles --- */
.modalOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6); /* Darker overlay */
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1050; /* Ensure it's above other content */
  padding: 15px; /* Add padding for smaller screens */
}

.modalContent {
  background: white;
  padding: 25px;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.3);
  width: 100%;
  max-width: 500px;
  max-height: 90vh; /* Max height with scroll */
  overflow-y: auto; /* Enable scroll if content overflows */
}

.modalContent h4 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 1.4rem;
  color: #333;
}

.formGroup { /* Style for form groups within the modal */
  margin-bottom: 15px;
}

.formGroup label {
  display: block;
  margin-bottom: 5px;
  font-weight: 600;
  color: #495057;
}

.modalActions {
  margin-top: 25px;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.cancelButton { /* Specific style for cancel button in modal */
  background-color: #6c757d; /* Gray */
  color: white;
}
.cancelButton:hover {
  background-color: #5a6268;
}
