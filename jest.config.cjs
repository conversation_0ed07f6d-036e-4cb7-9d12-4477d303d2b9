module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  testPathIgnorePatterns: ['<rootDir>/.next/', '<rootDir>/node_modules/'],
  moduleNameMapper: {
    // Handle CSS modules
    '\\.module\\.css$': 'identity-obj-proxy',
    // Handle regular CSS files if any (optional, but good practice)
    '\\.css$': 'identity-obj-proxy',
    // Existing aliases
    '^@/components/(.*)$': '<rootDir>/components/$1',
    '^@/lib/(.*)$': '<rootDir>/lib/$1',
    '^@/contexts/(.*)$': '<rootDir>/contexts/$1',
    '^@/(.*)$': '<rootDir>/$1'
  },
  transform: {
    '^.+\\.(js|jsx|ts|tsx)$': ['babel-jest', { configFile: './babel.config.json' }]
  },
  transformIgnorePatterns: [
    '/node_modules/(?!@supabase/supabase-js).+\\.js$'
  ]
}
