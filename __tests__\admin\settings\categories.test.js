import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CategoriesPage from '@/pages/admin/settings/categories';

// Mock AdminLayout and ProtectedRoute
jest.mock('@/components/admin/AdminLayout', () => ({ children, title }) => <div data-testid="admin-layout" data-title={title}>{children}</div>);
jest.mock('@/components/admin/ProtectedRoute', () => ({ children }) => <div data-testid="protected-route">{children}</div>);

// Mock Modal: For this test, we'll make it render its children directly
// to easily access form elements without dealing with portal complexity.
jest.mock('@/components/admin/Modal', () => ({ children, onClose }) => (
  <div data-testid="modal">
    {children}
    <button onClick={onClose} data-testid="modal-close-button">Close Modal</button>
  </div>
));

// Mock global fetch
global.fetch = jest.fn();

const mockCategories = [
  { id: '1', name: 'Category 1', description: 'Desc 1', parent_id: null },
  { id: '2', name: 'Category 2', description: 'Desc 2', parent_id: '1' },
  { id: '3', name: 'Category 3', description: 'Desc 3', parent_id: null },
];

const mockSingleCategory = (id, name, description, parent_id) => ({
  id: String(id),
  name: String(name),
  description: description === null ? null : String(description || ''),
  parent_id: parent_id === null ? null : String(parent_id || ''),
});


describe('CategoriesPage UI', () => {
  beforeEach(() => {
    fetch.mockClear();
    // Default successful fetch for categories
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, categories: mockCategories }),
    });
  });

  test('renders loading state initially then displays categories', async () => {
    render(<CategoriesPage />);
    expect(screen.getByText('Loading categories...')).toBeInTheDocument();
    await waitFor(() => {
      expect(screen.getByText('Category 1')).toBeInTheDocument();
      expect(screen.getByText('Category 2')).toBeInTheDocument();
      expect(screen.getByText('Desc 1')).toBeInTheDocument();
      // Check parent category rendering
      const rows = screen.getAllByRole('row');
      // Row 0 is header, Row 1 is Category 1, Row 2 is Category 2
      // For Category 2, parent is Category 1
      expect(rows[2]).toHaveTextContent('Category 1');
    });
  });

  test('displays error message if fetching categories fails', async () => {
    fetch.mockReset(); // Reset to override default mock
    fetch.mockResolvedValueOnce({
      ok: false,
      status: 500,
      json: async () => ({ error: 'Server Error' }),
    });
    render(<CategoriesPage />);
    await waitFor(() => {
      expect(screen.getByText(/Error fetching categories: Server Error/i)).toBeInTheDocument();
    });
  });

  test('displays "No categories found" message if list is empty', async () => {
    fetch.mockReset();
    fetch.mockResolvedValueOnce({
      ok: true,
      json: async () => ({ success: true, categories: [] }),
    });
    render(<CategoriesPage />);
    await waitFor(() => {
      expect(screen.getByText('No categories found. Add one to get started!')).toBeInTheDocument();
    });
  });

  describe('Add New Category', () => {
    test('opens "Add New Category" modal and submits successfully', async () => {
      render(<CategoriesPage />);
      await waitFor(() => expect(screen.getByText('Category 1')).toBeInTheDocument()); // Ensure initial load is done

      fireEvent.click(screen.getByText('Add New Category'));

      // Modal should be visible, check for a form element like the name input
      await screen.findByRole('heading', { name: /Add New Category/i });
      expect(screen.getByLabelText(/Name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/Description/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/Parent Category/i)).toBeInTheDocument();

      // Fill the form
      fireEvent.change(screen.getByLabelText(/Name/i), { target: { value: 'New Test Category' } });
      fireEvent.change(screen.getByLabelText(/Description/i), { target: { value: 'Test Desc' } });
      // Select a parent category (assuming Category 1 is an option)
      fireEvent.change(screen.getByLabelText(/Parent Category/i), { target: { value: '1' } });


      // Mock the POST request for adding
      fetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        json: async () => ({ success: true, category: mockSingleCategory(4, 'New Test Category', 'Test Desc', '1') }),
      });
      // Mock the categories refresh call
      const updatedCategories = [...mockCategories, mockSingleCategory(4, 'New Test Category', 'Test Desc', '1')];
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, categories: updatedCategories }),
      });

      // Spy on alert
      const alertSpy = jest.spyOn(window, 'alert').mockImplementation(() => {});

      fireEvent.click(screen.getByRole('button', { name: /Add Category/i }));

      await waitFor(() => {
        expect(fetch).toHaveBeenCalledWith('/api/admin/service-categories', expect.objectContaining({
          method: 'POST',
          body: JSON.stringify({ name: 'New Test Category', description: 'Test Desc', parent_id: 1 }),
        }));
        expect(alertSpy).toHaveBeenCalledWith('Category added successfully!');
        // Modal should close - check if a distinctive element from the modal is gone
        expect(screen.queryByRole('heading', { name: /Add New Category/i })).not.toBeInTheDocument();
        // New category should be in the table
        expect(screen.getByText('New Test Category')).toBeInTheDocument();
      });
      alertSpy.mockRestore();
    });

    test('shows client-side validation error for empty name', async () => {
      render(<CategoriesPage />);
      await waitFor(() => expect(screen.getByText('Category 1')).toBeInTheDocument());
      fireEvent.click(screen.getByText('Add New Category'));
      await screen.findByRole('heading', { name: /Add New Category/i });

      fireEvent.click(screen.getByRole('button', { name: /Add Category/i }));
      expect(await screen.findByText('Name is required.')).toBeInTheDocument();
      expect(fetch).not.toHaveBeenCalledWith('/api/admin/service-categories', expect.objectContaining({ method: 'POST' }));
    });

    test('shows API error in modal if add fails', async () => {
      render(<CategoriesPage />);
      await waitFor(() => expect(screen.getByText('Category 1')).toBeInTheDocument());
      fireEvent.click(screen.getByText('Add New Category'));
      await screen.findByRole('heading', { name: /Add New Category/i });

      fireEvent.change(screen.getByLabelText(/Name/i), { target: { value: 'Fail Category' } });

      fetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: async () => ({ error: 'Failed to create category from API' }),
      });

      fireEvent.click(screen.getByRole('button', { name: /Add Category/i }));

      await waitFor(() => {
        expect(screen.getByText('Failed to create category from API')).toBeInTheDocument();
      });
      // Modal should still be open
      expect(screen.getByRole('heading', { name: /Add New Category/i })).toBeInTheDocument();
    });
  });

  describe('Edit Category', () => {
    test('opens "Edit Category" modal with pre-filled data and submits successfully', async () => {
      render(<CategoriesPage />);
      await waitFor(() => expect(screen.getByText('Category 1')).toBeInTheDocument());

      // Click edit button for Category 2 (which has parent_id: '1')
      const editButtons = screen.getAllByRole('button', { name: /Edit/i });
      fireEvent.click(editButtons[1]); // Edit Category 2

      await screen.findByRole('heading', { name: /Edit Category/i });
      expect(screen.getByLabelText(/Name/i)).toHaveValue('Category 2');
      expect(screen.getByLabelText(/Description/i)).toHaveValue('Desc 2');
      expect(screen.getByLabelText(/Parent Category/i)).toHaveValue('1'); // Parent is Category 1

      // Change data
      fireEvent.change(screen.getByLabelText(/Name/i), { target: { value: 'Category 2 Updated' } });
      fireEvent.change(screen.getByLabelText(/Parent Category/i), { target: { value: '' } }); // Set parent to None


      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, category: mockSingleCategory(2, 'Category 2 Updated', 'Desc 2', null) }),
      });
      const updatedCategories = mockCategories.map(cat =>
        cat.id === '2' ? mockSingleCategory(2, 'Category 2 Updated', 'Desc 2', null) : cat
      );
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, categories: updatedCategories }),
      });
      const alertSpy = jest.spyOn(window, 'alert').mockImplementation(() => {});

      fireEvent.click(screen.getByRole('button', { name: /Save Changes/i }));

      await waitFor(() => {
        expect(fetch).toHaveBeenCalledWith('/api/admin/service-categories', expect.objectContaining({
          method: 'PUT',
          body: JSON.stringify({ id: '2', name: 'Category 2 Updated', description: 'Desc 2', parent_id: null }),
        }));
        expect(alertSpy).toHaveBeenCalledWith('Category updated successfully!');
        expect(screen.queryByRole('heading', { name: /Edit Category/i })).not.toBeInTheDocument();
        expect(screen.getByText('Category 2 Updated')).toBeInTheDocument();
      });
      alertSpy.mockRestore();
    });

    test('prevents selecting self as parent in edit mode', async () => {
      render(<CategoriesPage />);
      await waitFor(() => expect(screen.getByText('Category 1')).toBeInTheDocument());

      const editButtons = screen.getAllByRole('button', { name: /Edit/i });
      fireEvent.click(editButtons[0]); // Edit Category 1

      await screen.findByRole('heading', { name: /Edit Category/i });
      const parentSelect = screen.getByLabelText(/Parent Category/i);

      // Check that "Category 1" is not an option in the parent dropdown
      const options = Array.from(parentSelect.options).map(option => option.value);
      expect(options).not.toContain('1');
    });
  });

  describe('Delete Category', () => {
    let alertSpy;
    let confirmSpy;

    beforeEach(() => {
      alertSpy = jest.spyOn(window, 'alert').mockImplementation(() => {});
      confirmSpy = jest.spyOn(window, 'confirm');
    });

    afterEach(() => {
      alertSpy.mockRestore();
      confirmSpy.mockRestore();
    });

    test('successfully deletes a category after confirmation', async () => {
      confirmSpy.mockReturnValueOnce(true); // User confirms deletion

      render(<CategoriesPage />);
      await waitFor(() => expect(screen.getByText('Category 1')).toBeInTheDocument());

      const deleteButtons = screen.getAllByRole('button', { name: /Delete/i });
      fireEvent.click(deleteButtons[0]); // Click delete for "Category 1"

      expect(confirmSpy).toHaveBeenCalledWith('Are you sure you want to delete the category "Category 1"? This action cannot be undone.');

      // Mock successful DELETE API response
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, message: 'Category "Category 1" deleted successfully!' }),
      });

      // Mock categories refresh call (Category 1 removed)
      const remainingCategories = mockCategories.filter(cat => cat.id !== '1');
      fetch.mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true, categories: remainingCategories }),
      });

      await waitFor(() => {
        expect(fetch).toHaveBeenCalledWith('/api/admin/service-categories?id=1', { method: 'DELETE' });
        expect(alertSpy).toHaveBeenCalledWith('Category "Category 1" deleted successfully!');
        expect(screen.queryByText('Category 1')).not.toBeInTheDocument();
        expect(screen.getByText('Category 2')).toBeInTheDocument(); // Other categories still there
      });
    });

    test('does not delete if user cancels confirmation', async () => {
      confirmSpy.mockReturnValueOnce(false); // User cancels deletion
      render(<CategoriesPage />);
      await waitFor(() => expect(screen.getByText('Category 1')).toBeInTheDocument());

      const deleteButtons = screen.getAllByRole('button', { name: /Delete/i });
      fireEvent.click(deleteButtons[0]);

      expect(confirmSpy).toHaveBeenCalled();
      expect(fetch).not.toHaveBeenCalledWith(expect.stringContaining('/api/admin/service-categories?id=1'), expect.objectContaining({ method: 'DELETE' }));
      expect(screen.getByText('Category 1')).toBeInTheDocument(); // Category still there
    });

    test('shows error message if API deletion fails (e.g., category in use)', async () => {
      confirmSpy.mockReturnValueOnce(true);
      render(<CategoriesPage />);
      await waitFor(() => expect(screen.getByText('Category 1')).toBeInTheDocument());

      const deleteButtons = screen.getAllByRole('button', { name: /Delete/i });
      fireEvent.click(deleteButtons[0]); // Attempt to delete Category 1

      // Mock failed DELETE API response (409 Conflict)
      fetch.mockResolvedValueOnce({
        ok: false,
        status: 409,
        json: async () => ({ error: 'Category is in use and cannot be deleted.' }),
      });

      await waitFor(() => {
        expect(fetch).toHaveBeenCalledWith('/api/admin/service-categories?id=1', { method: 'DELETE' });
        // Check for the page-level error display
        expect(screen.getByText(/Failed to delete category "Category 1": Category is in use and cannot be deleted./i)).toBeInTheDocument();
      });
      expect(screen.getByText('Category 1')).toBeInTheDocument(); // Category still in list
    });
  });

  // Reminder for manual tests
  describe('Manual Test Reminders', () => {
    test.todo('Manually check POS quick event mode scrolling on a mobile viewport to ensure usability.');
  });
});
