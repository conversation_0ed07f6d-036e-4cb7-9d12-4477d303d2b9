/**
 * Terminal Devices API - Manage Square Terminal device pairing and status
 * Handles device code generation, pairing status, and device management
 */

import { supabaseAdmin } from '@/lib/supabase-admin'

export default async function handler(req, res) {
  // Generate unique request ID for tracking
  const requestId = `terminal_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`
  
  console.log(`[${requestId}] Terminal Devices API request: ${req.method}`)

  // Only allow GET and POST methods
  if (!['GET', 'POST'].includes(req.method)) {
    return res.status(405).json({ 
      error: 'Method not allowed',
      message: `${req.method} method is not supported for this endpoint`
    })
  }

  try {
    // Verify admin authentication
    const authHeader = req.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        error: 'Unauthorized',
        message: 'Valid admin token required'
      })
    }

    const token = authHeader.split(' ')[1]
    
    // Verify token with Supabase
    const { data: { user }, error: authError } = await supabaseAdmin.auth.getUser(token)
    
    if (authError || !user) {
      console.error(`[${requestId}] Authentication failed:`, authError)
      return res.status(401).json({
        error: 'Authentication failed',
        message: 'Invalid or expired token'
      })
    }

    // Get Square configuration from environment or database
    const squareAccessToken = process.env.SQUARE_ACCESS_TOKEN
    const squareEnvironment = process.env.SQUARE_ENVIRONMENT || 'sandbox'
    const squareLocationId = process.env.NEXT_PUBLIC_SQUARE_LOCATION_ID

    if (!squareAccessToken || !squareLocationId) {
      return res.status(500).json({
        error: 'Configuration error',
        message: 'Square API credentials not configured'
      })
    }

    if (req.method === 'GET') {
      // List existing device codes and their status
      return await handleGetDevices(req, res, requestId, squareAccessToken, squareEnvironment, squareLocationId)
    } else if (req.method === 'POST') {
      // Create new device code for pairing
      return await handleCreateDeviceCode(req, res, requestId, squareAccessToken, squareEnvironment, squareLocationId)
    }

  } catch (error) {
    console.error(`[${requestId}] Terminal devices API error:`, error)
    return res.status(500).json({
      error: 'Internal server error',
      message: 'Failed to process terminal device request',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    })
  }
}

/**
 * Handle GET request - List existing devices and their status
 */
async function handleGetDevices(req, res, requestId, accessToken, environment, locationId) {
  try {
    console.log(`[${requestId}] Fetching terminal devices...`)

    // Square API endpoint for listing device codes
    const baseUrl = environment === 'production'
      ? 'https://connect.squareup.com'
      : 'https://connect.squareupsandbox.com'

    const url = `${baseUrl}/v2/devices/codes`

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Square-Version': '2023-10-18'
      }
    })

    const responseData = await response.json()

    if (!response.ok) {
      console.error(`[${requestId}] Square API error:`, responseData)
      throw new Error(responseData.errors?.[0]?.detail || 'Failed to fetch devices')
    }

    // Filter devices for this location and format response
    const devices = (responseData.device_codes || [])
      .filter(device => device.location_id === locationId)
      .map(device => ({
        id: device.id,
        name: device.name,
        code: device.code,
        status: device.status,
        deviceId: device.device_id,
        productType: device.product_type,
        createdAt: device.created_at,
        pairBy: device.pair_by,
        statusChangedAt: device.status_changed_at
      }))

    console.log(`[${requestId}] Found ${devices.length} terminal devices`)

    return res.status(200).json({
      success: true,
      devices: devices,
      count: devices.length,
      message: `Found ${devices.length} terminal device(s)`
    })

  } catch (error) {
    console.error(`[${requestId}] Error fetching devices:`, error)
    return res.status(500).json({
      error: 'Failed to fetch devices',
      message: error.message
    })
  }
}

/**
 * Handle POST request - Create new device code for pairing
 */
async function handleCreateDeviceCode(req, res, requestId, accessToken, environment, locationId) {
  try {
    const { deviceName } = req.body

    console.log(`[${requestId}] Creating device code for: ${deviceName || 'Terminal Device'}`)

    // Square API endpoint for creating device codes
    const baseUrl = environment === 'production'
      ? 'https://connect.squareup.com'
      : 'https://connect.squareupsandbox.com'

    const url = `${baseUrl}/v2/devices/codes`

    // Generate idempotency key
    const idempotencyKey = `device_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`

    const requestBody = {
      idempotency_key: idempotencyKey,
      device_code: {
        product_type: 'TERMINAL_API',
        name: deviceName || `Terminal Device created on ${new Date().toLocaleDateString()}`,
        location_id: locationId
      }
    }

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
        'Square-Version': '2023-10-18'
      },
      body: JSON.stringify(requestBody)
    })

    const responseData = await response.json()

    if (!response.ok) {
      console.error(`[${requestId}] Square API error:`, responseData)
      throw new Error(responseData.errors?.[0]?.detail || 'Failed to create device code')
    }

    const deviceCode = responseData.device_code

    console.log(`[${requestId}] Device code created successfully: ${deviceCode.code}`)

    return res.status(201).json({
      success: true,
      deviceCode: {
        id: deviceCode.id,
        name: deviceCode.name,
        code: deviceCode.code,
        status: deviceCode.status,
        productType: deviceCode.product_type,
        locationId: deviceCode.location_id,
        pairBy: deviceCode.pair_by,
        createdAt: deviceCode.created_at,
        statusChangedAt: deviceCode.status_changed_at
      },
      message: `Device code ${deviceCode.code} created successfully`
    })

  } catch (error) {
    console.error(`[${requestId}] Error creating device code:`, error)
    return res.status(500).json({
      error: 'Failed to create device code',
      message: error.message
    })
  }
}
