/* CategoriesPage.module.css */
.pageContainer {
  padding: 1.5rem;
  background-color: #f9fafb;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.header h1 {
  font-size: 1.75rem;
  font-weight: 600;
  color: #111827;
}

.addButton {
  background-color: #4f46e5;
  color: white;
  padding: 0.6rem 1.2rem;
  border-radius: 0.375rem;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.addButton:hover {
  background-color: #4338ca;
}

.loading,
.error,
.noCategories {
  text-align: center;
  padding: 2rem;
  color: #6b7280;
  font-size: 1rem;
}

.error {
  color: #ef4444;
  background-color: #fee2e2;
  border: 1px solid #fca5a5;
  border-radius: 0.375rem;
  padding: 1rem;
}

.tableContainer {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  overflow-x: auto;
}

.categoriesTable {
  width: 100%;
  border-collapse: collapse;
}

.categoriesTable th,
.categoriesTable td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid #e5e7eb;
  font-size: 0.875rem;
}

.categoriesTable th {
  background-color: #f3f4f6;
  color: #374151;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.categoriesTable td {
  color: #111827;
}

.actionsCell button {
  margin-right: 0.5rem;
  padding: 0.3rem 0.6rem;
  border-radius: 0.25rem;
  border: 1px solid transparent;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s, border-color 0.2s;
}

.editButton {
  background-color: #3b82f6;
  color: white;
}

.editButton:hover {
  background-color: #2563eb;
}

.deleteButton {
  background-color: #ef4444; /* Red */
  color: white;
}

.deleteButton:hover {
  background-color: #dc2626; /* Darker red */
}

.deleteButton:disabled {
  background-color: #fca5a5; /* Lighter red when disabled */
  cursor: not-allowed;
  opacity: 0.7;
}

/* Modal Styles (Simplified - assuming a Modal component handles most of this) */
.modalContent {
  padding: 1.5rem;
}

.modalContent h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #111827;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
}

.formGroup label {
  margin-bottom: 0.25rem;
  font-weight: 500;
  color: #374151;
  font-size: 0.875rem;
}

.formGroup input,
.formGroup textarea,
.formGroup select {
  padding: 0.6rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.formGroup input:focus,
.formGroup textarea:focus,
.formGroup select:focus {
  outline: none;
  border-color: #4f46e5;
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.25);
}

.formGroup textarea {
  min-height: 80px;
  resize: vertical;
}

.formActions {
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

.formActions button {
  padding: 0.6rem 1.2rem;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  border: 1px solid transparent;
}

.saveButton {
  background-color: #10b981;
  color: white;
}

.saveButton:hover {
  background-color: #059669;
}

.cancelButton {
  background-color: #e5e7eb;
  color: #374151;
}

.cancelButton:hover {
  background-color: #d1d5db;
}

.errorMessage {
  color: #ef4444;
  font-size: 0.8rem;
  margin-top: 0.25rem;
}
