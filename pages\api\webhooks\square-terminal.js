/**
 * Square Terminal Webhook Handler
 * Handles webhook events from Square Terminal API for real-time payment status updates
 */

import { supabaseAdmin } from '@/lib/supabase-admin'
import crypto from 'crypto'

export default async function handler(req, res) {
  // Generate unique request ID for tracking
  const requestId = `webhook_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`
  
  console.log(`[${requestId}] Square Terminal webhook received: ${req.method}`)

  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ 
      error: 'Method not allowed',
      message: 'Only POST requests are allowed for webhooks'
    })
  }

  try {
    // Verify webhook signature (if configured)
    const webhookSignatureKey = process.env.SQUARE_WEBHOOK_SIGNATURE_KEY
    if (webhookSignatureKey) {
      const signature = req.headers['x-square-signature']
      if (!signature) {
        console.error(`[${requestId}] Missing webhook signature`)
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'Missing webhook signature'
        })
      }

      // Verify signature
      const body = JSON.stringify(req.body)
      const expectedSignature = crypto
        .createHmac('sha256', webhookSignatureKey)
        .update(body)
        .digest('base64')

      if (signature !== expectedSignature) {
        console.error(`[${requestId}] Invalid webhook signature`)
        return res.status(401).json({
          error: 'Unauthorized',
          message: 'Invalid webhook signature'
        })
      }
    }

    const { type, data } = req.body

    console.log(`[${requestId}] Processing webhook event: ${type}`)

    switch (type) {
      case 'terminal.checkout.created':
        return await handleTerminalCheckoutCreated(req, res, requestId, data)
      
      case 'terminal.checkout.updated':
        return await handleTerminalCheckoutUpdated(req, res, requestId, data)
      
      case 'terminal.refund.created':
        return await handleTerminalRefundCreated(req, res, requestId, data)
      
      case 'terminal.refund.updated':
        return await handleTerminalRefundUpdated(req, res, requestId, data)
      
      case 'terminal.action.created':
        return await handleTerminalActionCreated(req, res, requestId, data)
      
      case 'terminal.action.updated':
        return await handleTerminalActionUpdated(req, res, requestId, data)
      
      default:
        console.log(`[${requestId}] Unhandled webhook event type: ${type}`)
        return res.status(200).json({
          success: true,
          message: `Webhook event ${type} received but not processed`
        })
    }

  } catch (error) {
    console.error(`[${requestId}] Webhook processing error:`, error)
    return res.status(500).json({
      error: 'Webhook processing failed',
      message: error.message,
      details: process.env.NODE_ENV === 'development' ? error.stack : undefined
    })
  }
}

/**
 * Handle terminal checkout created event
 */
async function handleTerminalCheckoutCreated(req, res, requestId, data) {
  try {
    const checkout = data.object.checkout

    console.log(`[${requestId}] Terminal checkout created: ${checkout.id}`)

    // Store checkout record for tracking
    const { error: insertError } = await supabaseAdmin
      .from('terminal_checkouts')
      .insert([{
        checkout_id: checkout.id,
        device_id: checkout.device_options?.device_id,
        amount: checkout.amount_money.amount,
        currency: checkout.amount_money.currency,
        status: checkout.status,
        payment_note: checkout.payment_note,
        order_id: checkout.order_id,
        created_at: checkout.created_at,
        updated_at: checkout.updated_at
      }])

    if (insertError) {
      console.error(`[${requestId}] Error storing checkout:`, insertError)
    }

    return res.status(200).json({
      success: true,
      message: 'Terminal checkout created event processed'
    })

  } catch (error) {
    console.error(`[${requestId}] Error handling checkout created:`, error)
    return res.status(500).json({
      error: 'Failed to process checkout created event',
      message: error.message
    })
  }
}

/**
 * Handle terminal checkout updated event
 */
async function handleTerminalCheckoutUpdated(req, res, requestId, data) {
  try {
    const checkout = data.object.checkout

    console.log(`[${requestId}] Terminal checkout updated: ${checkout.id}, status: ${checkout.status}`)

    // Update checkout record
    const { error: updateError } = await supabaseAdmin
      .from('terminal_checkouts')
      .update({
        status: checkout.status,
        payment_id: checkout.payment_id,
        cancel_reason: checkout.cancel_reason,
        updated_at: checkout.updated_at
      })
      .eq('checkout_id', checkout.id)

    if (updateError) {
      console.error(`[${requestId}] Error updating checkout:`, updateError)
    }

    // If checkout is completed, update associated booking
    if (checkout.status === 'COMPLETED' && checkout.payment_id) {
      await updateBookingPaymentStatus(requestId, checkout)
    }

    // Broadcast real-time update to connected clients
    await broadcastCheckoutUpdate(requestId, checkout)

    return res.status(200).json({
      success: true,
      message: 'Terminal checkout updated event processed'
    })

  } catch (error) {
    console.error(`[${requestId}] Error handling checkout updated:`, error)
    return res.status(500).json({
      error: 'Failed to process checkout updated event',
      message: error.message
    })
  }
}

/**
 * Handle terminal refund created event
 */
async function handleTerminalRefundCreated(req, res, requestId, data) {
  try {
    const refund = data.object.refund

    console.log(`[${requestId}] Terminal refund created: ${refund.id}`)

    // Store refund record
    const { error: insertError } = await supabaseAdmin
      .from('terminal_refunds')
      .insert([{
        refund_id: refund.id,
        payment_id: refund.payment_id,
        device_id: refund.device_options?.device_id,
        amount: refund.amount_money.amount,
        currency: refund.amount_money.currency,
        status: refund.status,
        reason: refund.reason,
        created_at: refund.created_at,
        updated_at: refund.updated_at
      }])

    if (insertError) {
      console.error(`[${requestId}] Error storing refund:`, insertError)
    }

    return res.status(200).json({
      success: true,
      message: 'Terminal refund created event processed'
    })

  } catch (error) {
    console.error(`[${requestId}] Error handling refund created:`, error)
    return res.status(500).json({
      error: 'Failed to process refund created event',
      message: error.message
    })
  }
}

/**
 * Handle terminal refund updated event
 */
async function handleTerminalRefundUpdated(req, res, requestId, data) {
  try {
    const refund = data.object.refund

    console.log(`[${requestId}] Terminal refund updated: ${refund.id}, status: ${refund.status}`)

    // Update refund record
    const { error: updateError } = await supabaseAdmin
      .from('terminal_refunds')
      .update({
        status: refund.status,
        updated_at: refund.updated_at
      })
      .eq('refund_id', refund.id)

    if (updateError) {
      console.error(`[${requestId}] Error updating refund:`, updateError)
    }

    return res.status(200).json({
      success: true,
      message: 'Terminal refund updated event processed'
    })

  } catch (error) {
    console.error(`[${requestId}] Error handling refund updated:`, error)
    return res.status(500).json({
      error: 'Failed to process refund updated event',
      message: error.message
    })
  }
}

/**
 * Handle terminal action created event
 */
async function handleTerminalActionCreated(req, res, requestId, data) {
  console.log(`[${requestId}] Terminal action created event received`)
  
  return res.status(200).json({
    success: true,
    message: 'Terminal action created event processed'
  })
}

/**
 * Handle terminal action updated event
 */
async function handleTerminalActionUpdated(req, res, requestId, data) {
  console.log(`[${requestId}] Terminal action updated event received`)
  
  return res.status(200).json({
    success: true,
    message: 'Terminal action updated event processed'
  })
}

/**
 * Update booking payment status when terminal checkout completes
 */
async function updateBookingPaymentStatus(requestId, checkout) {
  try {
    // Find booking by payment note or order ID
    const { data: bookings, error: findError } = await supabaseAdmin
      .from('bookings')
      .select('id, pos_session_id')
      .or(`notes.ilike.%${checkout.payment_note}%,pos_session_id.eq.${checkout.order_id}`)
      .limit(1)

    if (findError || !bookings?.length) {
      console.warn(`[${requestId}] No booking found for checkout: ${checkout.id}`)
      return
    }

    const booking = bookings[0]

    // Update payment record
    const { error: updateError } = await supabaseAdmin
      .from('payments')
      .update({
        payment_status: 'completed',
        square_payment_id: checkout.payment_id,
        transaction_id: checkout.payment_id
      })
      .eq('booking_id', booking.id)

    if (updateError) {
      console.error(`[${requestId}] Error updating payment status:`, updateError)
    } else {
      console.log(`[${requestId}] Updated payment status for booking: ${booking.id}`)
    }

  } catch (error) {
    console.error(`[${requestId}] Error updating booking payment status:`, error)
  }
}

/**
 * Broadcast checkout update to connected clients
 */
async function broadcastCheckoutUpdate(requestId, checkout) {
  try {
    // Use Supabase realtime to broadcast update
    const { error } = await supabaseAdmin
      .from('terminal_checkout_updates')
      .insert([{
        checkout_id: checkout.id,
        status: checkout.status,
        payment_id: checkout.payment_id,
        updated_at: new Date().toISOString()
      }])

    if (error) {
      console.error(`[${requestId}] Error broadcasting checkout update:`, error)
    }

  } catch (error) {
    console.error(`[${requestId}] Error in broadcast:`, error)
  }
}
