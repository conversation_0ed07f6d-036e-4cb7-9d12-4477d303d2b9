/**
 * Dashboard Performance Optimizer
 * 
 * This script optimizes dashboard performance by:
 * 1. Suppressing browser extension errors (React DevTools, etc.)
 * 2. Preventing error cascading that causes page flashing
 * 3. Optimizing React hydration
 * 4. Reducing console noise
 */

(function() {
  'use strict';

  // Store original console methods
  const originalConsoleError = console.error;
  const originalConsoleWarn = console.warn;
  const originalConsoleLog = console.log;

  // Extension error patterns that cause the 133 JavaScript errors
  const EXTENSION_ERROR_PATTERNS = [
    /overrideMethod/i,
    /cancelled true/i,
    /chrome-extension/i,
    /moz-extension/i,
    /safari-extension/i,
    /extension context invalidated/i,
    /message port closed/i,
    /receiving end does not exist/i,
    /hook\.js:\d+/i,
    /react-devtools/i,
    /devtools/i,
    /runtime\.lastError/i,
    /loading chunk \d+ failed/i,
    /loading css chunk \d+ failed/i
  ];

  // React hydration warning patterns
  const HYDRATION_WARNING_PATTERNS = [
    /hydration/i,
    /did not match/i,
    /server-rendered html/i,
    /suppresshydrationwarning/i,
    /text content does not match/i,
    /prop.*did not match/i
  ];

  /**
   * Check if an error message is from a browser extension
   */
  function isExtensionError(message, source = '') {
    const fullMessage = `${message} ${source}`.toLowerCase();
    return EXTENSION_ERROR_PATTERNS.some(pattern => pattern.test(fullMessage));
  }

  /**
   * Check if an error is a React hydration warning
   */
  function isHydrationWarning(message) {
    return HYDRATION_WARNING_PATTERNS.some(pattern => pattern.test(message));
  }

  /**
   * Enhanced console.error that filters extension errors
   */
  console.error = function(...args) {
    const message = args.map(arg => String(arg)).join(' ');
    
    // Suppress extension errors completely
    if (isExtensionError(message)) {
      if (window.DEBUG_EXTENSION_ERRORS) {
        originalConsoleError.call(console, '[SUPPRESSED EXTENSION ERROR]', ...args);
      }
      return;
    }

    // Suppress hydration warnings in production
    if (process.env.NODE_ENV === 'production' && isHydrationWarning(message)) {
      return;
    }

    // Log other errors normally
    originalConsoleError.apply(console, args);
  };

  /**
   * Enhanced console.warn that filters extension warnings
   */
  console.warn = function(...args) {
    const message = args.map(arg => String(arg)).join(' ');
    
    // Suppress extension warnings
    if (isExtensionError(message)) {
      if (window.DEBUG_EXTENSION_ERRORS) {
        originalConsoleWarn.call(console, '[SUPPRESSED EXTENSION WARNING]', ...args);
      }
      return;
    }

    // Suppress hydration warnings in production
    if (process.env.NODE_ENV === 'production' && isHydrationWarning(message)) {
      return;
    }

    // Log other warnings normally
    originalConsoleWarn.apply(console, args);
  };

  /**
   * Suppress window errors from extensions
   */
  const originalOnError = window.onerror;
  window.onerror = function(message, source, lineno, colno, error) {
    if (isExtensionError(message, source)) {
      return true; // Prevent default error handling
    }
    
    if (originalOnError) {
      return originalOnError.call(this, message, source, lineno, colno, error);
    }
    
    return false;
  };

  /**
   * Suppress unhandled promise rejections from extensions
   */
  window.addEventListener('unhandledrejection', function(event) {
    const reason = event.reason;
    const message = reason?.message || String(reason);
    
    if (isExtensionError(message)) {
      event.preventDefault();
      return;
    }
  });

  /**
   * Optimize React performance by reducing re-renders
   */
  function optimizeReactPerformance() {
    // Debounce React state updates to prevent excessive re-renders
    if (window.React && window.React.useState) {
      const originalUseState = window.React.useState;
      
      window.React.useState = function(initialState) {
        const [state, setState] = originalUseState(initialState);
        
        // Debounced setState to prevent rapid updates
        const debouncedSetState = (function() {
          let timeoutId;
          return function(newState) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => {
              setState(newState);
            }, 0);
          };
        })();
        
        return [state, debouncedSetState];
      };
    }
  }

  /**
   * Optimize authentication flow performance
   */
  function optimizeAuthFlow() {
    // Cache auth state to prevent repeated API calls
    const AUTH_CACHE_KEY = 'oss_auth_performance_cache';
    const CACHE_DURATION = 30000; // 30 seconds
    
    window.optimizedAuthFetch = function(url, options = {}) {
      const cacheKey = `${url}_${JSON.stringify(options)}`;
      const cached = sessionStorage.getItem(`${AUTH_CACHE_KEY}_${cacheKey}`);
      
      if (cached) {
        const { data, timestamp } = JSON.parse(cached);
        if (Date.now() - timestamp < CACHE_DURATION) {
          return Promise.resolve(new Response(JSON.stringify(data), {
            status: 200,
            headers: { 'Content-Type': 'application/json' }
          }));
        }
      }
      
      return fetch(url, options).then(response => {
        if (response.ok && url.includes('/api/artist/dashboard')) {
          response.clone().json().then(data => {
            sessionStorage.setItem(`${AUTH_CACHE_KEY}_${cacheKey}`, JSON.stringify({
              data,
              timestamp: Date.now()
            }));
          });
        }
        return response;
      });
    };
  }

  /**
   * Reduce page flashing by optimizing loading states
   */
  function optimizeLoadingStates() {
    // Add CSS to prevent layout shifts
    const style = document.createElement('style');
    style.textContent = `
      .dashboard-loading {
        min-height: 400px;
        opacity: 0.8;
        transition: opacity 0.2s ease;
      }
      
      .dashboard-loaded {
        opacity: 1;
        transition: opacity 0.3s ease;
      }
      
      /* Prevent flash of unstyled content */
      .artist-dashboard-container {
        visibility: hidden;
      }
      
      .artist-dashboard-container.loaded {
        visibility: visible;
        animation: fadeIn 0.3s ease;
      }
      
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * Initialize performance optimizations
   */
  function initializeOptimizations() {
    try {
      optimizeReactPerformance();
      optimizeAuthFlow();
      optimizeLoadingStates();
      
      // Mark as initialized
      window.__DASHBOARD_PERFORMANCE_OPTIMIZED__ = true;
      
      console.log('🚀 Dashboard performance optimizations initialized');
    } catch (error) {
      originalConsoleError('Failed to initialize dashboard performance optimizations:', error);
    }
  }

  /**
   * Restore original console methods (for debugging)
   */
  window.restoreDashboardConsole = function() {
    console.error = originalConsoleError;
    console.warn = originalConsoleWarn;
    console.log = originalConsoleLog;
    console.log('Dashboard console methods restored');
  };

  // Initialize when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeOptimizations);
  } else {
    initializeOptimizations();
  }

})();
