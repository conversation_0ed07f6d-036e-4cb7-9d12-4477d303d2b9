import { useState } from 'react'
import styles from '@/styles/admin/POS.module.css'

/**
 * QuickEventModeToggle component for switching between Full Booking and Quick Event modes
 *
 * @param {Object} props - Component props
 * @param {string} props.currentMode - Current POS mode ('full' or 'quick')
 * @param {Function} props.onModeChange - Callback when mode changes
 * @param {boolean} props.disabled - Whether the toggle is disabled
 * @returns {JSX.Element}
 */
export default function QuickEventModeToggle({ currentMode, onModeChange, disabled = false }) {
  const [isAnimating, setIsAnimating] = useState(false)

  const handleModeChange = (newMode) => {
    if (disabled || isAnimating || newMode === currentMode) return

    setIsAnimating(true)
    onModeChange(newMode)
    
    // Reset animation state after transition
    setTimeout(() => setIsAnimating(false), 300)
  }

  return (
    <div className={styles.modeToggleContainer}>
      <div className={styles.modeToggleLabel}>
        <span className={styles.modeIcon}>⚡</span>
        <span>POS Mode</span>
      </div>
      
      <div className={`${styles.modeToggle} ${disabled ? styles.disabled : ''}`}>
        <button
          className={`${styles.modeOption} ${currentMode === 'full' ? styles.active : ''} ${isAnimating ? styles.animating : ''}`}
          onClick={() => handleModeChange('full')}
          disabled={disabled}
          title="Full booking workflow with artist selection and scheduling"
        >
          <span className={styles.modeOptionIcon}>📅</span>
          <span className={styles.modeOptionText}>Full Booking</span>
          <span className={styles.modeOptionSubtext}>Artist & Time Selection</span>
        </button>
        
        <button
          className={`${styles.modeOption} ${currentMode === 'quick' ? styles.active : ''} ${isAnimating ? styles.animating : ''}`}
          onClick={() => handleModeChange('quick')}
          disabled={disabled}
          title="Quick event mode for fast face painting and immediate services"
        >
          <span className={styles.modeOptionIcon}>⚡</span>
          <span className={styles.modeOptionText}>Quick Event</span>
          <span className={styles.modeOptionSubtext}>Fast Service & Payment</span>
        </button>
      </div>

      <div className={styles.modeDescription}>
        {currentMode === 'full' ? (
          <p>
            <strong>Full Booking Mode:</strong> Complete workflow with artist selection, 
            time slot booking, and customer information collection.
          </p>
        ) : (
          <p>
            <strong>Quick Event Mode:</strong> Streamlined checkout for events. 
            Select service duration and proceed directly to payment.
          </p>
        )}
      </div>
    </div>
  )
}
